"""
Profile Service - 處理用戶檔案相關的業務邏輯
"""

import asyncio
import os
import re
import shutil
import time
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse

import bleach
import discord
from PIL import Image

from config.app_config import get_config
from database.postgresql.async_manager import get_pool, get_redis_client
from gacha.exceptions import (
    DatabaseOperationError,
    LikeProfileError,
    ProfileCardSetError,
    ProfileUpdateError,
    UserDoesNotOwnCardError,
)
from gacha.models.profile_models import ProfileCardInfo, ProfileData
from gacha.repositories.profile import profile_repository
from gacha.services import user_service
from gacha.services.ui import profile_image_generator
from utils.logger import logger

# 建立一個可靠的基礎目錄路徑，指向專案根目錄
# 假設 profile_service.py 位於 gacha/services/profile_service.py
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))

# --- 模組級配置 ---
RARITY_MAP = {1: "C", 2: "R", 3: "SR", 4: "SSR", 5: "UR", 6: "LR", 7: "EX"}
DEFAULT_RARITY_NAME = "UnknownRarity"
LIKE_COOLDOWN_SECONDS = 3600  # 1小時冷卻時間
BACKGROUND_IMAGES_BASE_PATH = os.path.join(
    BASE_DIR, "database", "background_images"
)  # <-- 使用 BASE_DIR
IMAGE_BASE_PATH = str(
    get_config(
        "gacha_core_settings.profile.image_local_base_path",
        "downloaded_gacha_master_cards",
    )
)
PREVIEW_IMAGES_BASE_PATH = os.path.join(
    BASE_DIR, "database", "previews"
)  # <-- 使用 BASE_DIR

# --- 模組初始化 ---
os.makedirs(BACKGROUND_IMAGES_BASE_PATH, exist_ok=True)
os.makedirs(PREVIEW_IMAGES_BASE_PATH, exist_ok=True)
logger.info("[ProfileService] 背景圖片存儲路徑: %s", BACKGROUND_IMAGES_BASE_PATH)
logger.info("[ProfileService] 卡片圖片基礎路徑: %s", IMAGE_BASE_PATH)


def sanitize_filename_part(text: str, max_length: int = 50) -> str:
    """清理文本使其适合作为文件名的一部分（最终统一标准）"""
    if not text:
        return "unknown"

    # 移除换行符
    text = text.replace("\r", "").replace("\n", "")

    # 统一处理特殊替换规则
    text = text.replace("//", "__")
    text = text.replace(", ", ",_")
    # 将冒号替换为下划线，而不是可能包含冒号的'__'
    text = text.replace(":", "_")

    # 将所有空格替换为下划线
    text = text.replace(" ", "_")

    # 将日文间隔号替换为下划线
    text = text.replace("・", "_")

    # 移除其他不合法字符
    text = re.sub(r'[\\/*?"<>|]', "", text)

    # 限制长度
    return text[:max_length]


def _generate_static_preview_sync(image_path: str, frame_number: int) -> Optional[str]:
    """
    (同步) 如果圖片是動畫，則根據指定的影格編號生成一個臨時靜態預覽圖。
    返回新預覽圖的路徑，如果不需要生成或失敗則返回 None。
    注意：生成的檔案需要在使用後手動清理。
    """
    # 根據規範，移除 try-except，讓檔案操作或圖片處理錯誤自然冒泡
    if (
        not image_path
        or not os.path.exists(image_path)
        or not image_path.lower().endswith((".gif", ".webp"))
    ):
        return None

    with Image.open(image_path) as img:
        if not getattr(img, "is_animated", False):
            return None

        # 確保請求的影格編號在有效範圍內
        n_frames = getattr(img, "n_frames", 1)
        target_frame_index = max(0, min(frame_number, n_frames - 1))

        img.seek(target_frame_index)

        # 創建唯一的臨時預覽檔名和路徑（使用時間戳確保唯一性）
        timestamp = int(time.time() * 1000000)  # 微秒級時間戳
        rarity_dir_name = os.path.basename(os.path.dirname(image_path))
        preview_dir = os.path.join(PREVIEW_IMAGES_BASE_PATH, rarity_dir_name)
        os.makedirs(preview_dir, exist_ok=True)

        original_filename = os.path.basename(image_path)
        name_part, _ = os.path.splitext(original_filename)
        # 檔名包含時間戳確保唯一性，用完即刪
        preview_filename = (
            f"{name_part}_frame_{target_frame_index}_temp_{timestamp}.png"
        )
        preview_path = os.path.join(preview_dir, preview_filename)

        # 將提取的影格另存為新的PNG圖片
        # 轉換為RGBA以確保透明度得到正確處理
        frame = img.convert("RGBA")
        frame.save(preview_path, "PNG")

        logger.debug(
            f"為 {image_path} 在影格 {target_frame_index} 生成了臨時靜態預覽 -> {preview_path}"
        )
        return os.path.abspath(preview_path)


async def _generate_static_preview_if_needed(
    card_info: ProfileCardInfo, frame_number: Optional[int]
) -> None:
    """
    (異步) 檢查是否需要為動畫生成靜態預覽圖，並更新 card_info。
    """
    if frame_number is None or not card_info.local_image_path:
        return

    preview_path = await asyncio.to_thread(
        _generate_static_preview_sync, card_info.local_image_path, frame_number
    )
    if preview_path:
        card_info.static_preview_path = preview_path


async def get_profile_data(
    user_id: int, discord_display_name: str, avatar_url: Optional[str] = None
) -> ProfileData:
    """獲取完整的檔案資料"""
    # 根據規範，移除 try-except，讓底層錯誤自然冒泡
    user = await user_service.get_user(user_id, create_if_missing=True)
    profile_settings_task = profile_repository.get_or_create_user_profile(user_id)
    user_stats_task = profile_repository.get_user_statistics(user_id)
    profile, stats = await asyncio.gather(profile_settings_task, user_stats_task)

    collection_ids_to_fetch = []
    if profile.showcased_card_collection_id:
        collection_ids_to_fetch.append(profile.showcased_card_collection_id)

    sub_card_collection_id_map = {}
    for slot in range(1, 5):
        collection_id = getattr(profile, f"sub_card_{slot}_collection_id")
        if collection_id:
            collection_ids_to_fetch.append(collection_id)
            sub_card_collection_id_map[collection_id] = slot

    unique_collection_ids = list(set(collection_ids_to_fetch))

    card_infos_dict: Dict[int, Optional[ProfileCardInfo]] = {}
    if unique_collection_ids:
        card_info_tasks = [
            profile_repository.get_card_info_by_collection_id(cid)
            for cid in unique_collection_ids
        ]
        card_info_results = await asyncio.gather(*card_info_tasks)

        for card_info in card_info_results:
            if card_info:
                card_infos_dict[card_info.collection_id] = card_info
                _set_local_image_path(card_info)

    main_card = (
        card_infos_dict.get(profile.showcased_card_collection_id)
        if profile.showcased_card_collection_id
        else None
    )

    sub_cards: Dict[int, ProfileCardInfo] = {
        slot: card
        for collection_id, slot in sub_card_collection_id_map.items()
        if (card := card_infos_dict.get(collection_id)) is not None
    }

    # --- NEW LOGIC FOR PREVIEW GENERATION ---
    preview_tasks = []
    if (
        main_card
        and hasattr(profile, "showcased_card_frame_number")
        and profile.showcased_card_frame_number is not None
    ):
        task = _generate_static_preview_if_needed(
            main_card, profile.showcased_card_frame_number
        )
        preview_tasks.append(task)

    for slot, card in sub_cards.items():
        frame_number = getattr(profile, f"sub_card_{slot}_frame_number", None)
        if frame_number is not None:
            task = _generate_static_preview_if_needed(card, frame_number)
            preview_tasks.append(task)

    if preview_tasks:
        await asyncio.gather(*preview_tasks)
    # --- END OF NEW LOGIC ---

    # 收集所有生成的 preview 檔案路徑，用於後續清理
    preview_files_to_cleanup = [
        card.static_preview_path
        for card in [main_card] + list(sub_cards.values())
        if card and card.static_preview_path
    ]

    return ProfileData(
        user_id=user_id,
        nickname=discord_display_name,
        oil_balance=user.oil_balance,
        oil_ticket_balance=user.oil_ticket_balance,
        total_draws=user.total_draws,
        like_count=profile.like_count,
        collection_completion_rate=stats["completion_rate"],
        total_owned_cards=stats["total_owned"],
        rarity_counts=stats["rarity_counts"],
        main_card=main_card,
        sub_cards=sub_cards,
        background_image_url=profile.background_image_url,
        user_status=profile.user_status,
        avatar_url=avatar_url,
        _preview_files_to_cleanup=preview_files_to_cleanup,
    )


def _set_local_image_path(card_info: ProfileCardInfo) -> None:
    """
    設置卡片的本地圖片路徑，並按優先級查找備用副檔名。
    優先順序: 原始副檔名 -> .webp -> .png -> .jpg -> .jpeg -> .gif
    """
    if not card_info.image_url:
        card_info.local_image_path = None
        return

    rarity_name = RARITY_MAP.get(card_info.rarity, DEFAULT_RARITY_NAME)
    name_part = sanitize_filename_part(
        card_info.name if card_info.name else card_info.series
    )

    # 1. 確定原始副檔名
    parsed_url = urlparse(card_info.image_url)
    original_filename = os.path.basename(parsed_url.path)
    _, original_ext = os.path.splitext(original_filename)
    original_ext = original_ext.lower()

    # 2. 建立副檔名查找順序列表
    fallback_extensions = [".webp", ".png", ".jpg", ".jpeg", ".gif"]
    search_extensions = []
    if original_ext and original_ext in fallback_extensions:
        search_extensions.append(original_ext)

    # 添加備用副檔名，避免重複
    for ext in fallback_extensions:
        if ext not in search_extensions:
            search_extensions.append(ext)

    # 3. 遍歷查找列表，找到第一個存在的圖片
    found_path = None
    for ext in search_extensions:
        image_filename = f"{card_info.card_id}_{name_part}{ext}"
        local_path = os.path.join(
            BASE_DIR, IMAGE_BASE_PATH, rarity_name, image_filename
        )
        if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
            found_path = local_path
            break  # 找到後立即停止

    # 4. 設置最終路徑
    if found_path:
        card_info.local_image_path = os.path.relpath(found_path, BASE_DIR).replace(
            "\\", "/"
        )
        logger.debug(
            "卡片 %s (%s) 找到本地圖片: %s",
            card_info.card_id,
            card_info.name,
            card_info.local_image_path,
        )
    else:
        logger.warning(
            "卡片 %s (%s) 在所有備用格式中均未找到有效的本地圖片。查找順序: %s",
            card_info.card_id,
            card_info.name,
            search_extensions,
        )
        card_info.local_image_path = None


async def get_cached_profile_image_url(user_id: int) -> Optional[str]:
    """獲取檔案圖片URL（從數據庫）"""
    # 根據規範，移除 try-except，讓資料庫錯誤自然冒泡
    cached_url = await profile_repository.get_profile_image_url(user_id)
    if cached_url:
        logger.debug("Retrieved profile URL from database for user %s.", user_id)
        return cached_url
    return None


async def update_cached_profile_image_url(user_id: int, image_url: str) -> bool:
    """更新檔案圖片URL（存儲到數據庫）"""
    # 根據規範，移除 try-except，讓資料庫錯誤自然冒泡
    await profile_repository.update_profile_image_url(user_id, image_url)
    logger.info("Updated profile image URL in database for user %s", user_id)
    return True


async def invalidate_profile_cache(user_id: int) -> bool:
    """使檔案快取失效（從數據庫清除）"""
    # 根據規範，移除 try-except，讓資料庫錯誤自然冒泡
    await profile_repository.clear_profile_image_url(user_id)
    logger.info("Cleared profile image URL from database for user %s", user_id)
    return True


async def set_main_card(user_id: int, card_id: int) -> Dict[str, Any]:
    """設定主展示卡片"""
    # 根據規範，移除 try-except，讓業務邏輯和資料庫錯誤自然冒泡
    user_card = await _verify_user_owns_card(user_id, card_id)
    await profile_repository.update_showcased_card(user_id, user_card["collection_id"])
    return {"card_id": user_card["card_id"], "name": user_card["name"]}


async def set_sub_card(user_id: int, slot_index: int, card_id: int) -> Dict[str, Any]:
    """設定副展示卡片"""
    # 根據規範，移除 try-except，讓業務邏輯和資料庫錯誤自然冒泡
    if slot_index not in [1, 2, 3, 4]:
        raise ProfileCardSetError("無效的槽位")
    user_card = await _verify_user_owns_card(user_id, card_id)
    await profile_repository.update_sub_card(
        user_id, slot_index, user_card["collection_id"]
    )
    return {
        "card_id": user_card["card_id"],
        "name": user_card["name"],
        "slot": slot_index,
    }


async def clear_sub_card(user_id: int, slot_index: int) -> int:
    """清除指定位置的副展示卡片"""
    # 根據規範，移除 try-except，讓業務邏輯和資料庫錯誤自然冒泡
    if slot_index not in [1, 2, 3, 4]:
        raise ProfileCardSetError(f"無效的副卡片位置: {slot_index}")
    await profile_repository.clear_sub_card_slot(user_id, slot_index)
    return slot_index


async def reset_background(user_id: int) -> None:
    """重置背景圖片"""
    # 根據規範，移除 try-except，讓檔案和資料庫錯誤自然冒泡
    await _cleanup_user_background_images(user_id)
    await profile_repository.update_background_image(user_id, None)
    logger.info("背景圖片已重置: user_id=%s", user_id)


async def set_background_image_from_bytes(
    user_id: int, image_bytes: bytes, filename: str
) -> None:
    """直接從圖片字節數據設定背景圖片"""
    # 根據規範，移除 try-except，讓檔案和資料庫錯誤自然冒泡
    # 確保用戶存在，這是修復用戶不存在錯誤的關鍵
    await user_service.get_user(user_id, create_if_missing=True)

    # _save_background_sync 返回的是相對路徑
    relative_path = await asyncio.to_thread(
        _save_background_sync, user_id, image_bytes, filename
    )
    await profile_repository.update_background_image(user_id, relative_path)
    logger.info(
        "背景圖片已直接保存: user_id=%s, size=%s bytes, path=%s",
        user_id,
        len(image_bytes),
        relative_path,
    )


def _save_background_sync(user_id: int, image_bytes: bytes, filename: str) -> str:
    """同步保存背景圖片文件"""
    user_bg_dir = os.path.join(BACKGROUND_IMAGES_BASE_PATH, str(user_id))
    os.makedirs(user_bg_dir, exist_ok=True)
    _, extension = os.path.splitext(filename)
    local_filename = f"background{extension or '.png'}"
    local_path = os.path.join(user_bg_dir, local_filename)
    if os.path.exists(local_path):
        os.remove(local_path)
    with open(local_path, "wb") as f:
        f.write(image_bytes)
    # 返回相對於 BASE_DIR 的相對路徑，並確保使用正斜杠
    return os.path.relpath(local_path, BASE_DIR).replace("\\", "/")


async def like_profile(liker_id: int, owner_id: int) -> None:
    """按讚檔案"""
    # 根據規範，移除 try-except，讓業務邏輯和底層錯誤自然冒泡
    # 導入 Redis 客戶端用於冷卻時間管理
    redis_client = get_redis_client()
    cooldown_key = f"profile_like_cd:{liker_id}:{owner_id}"

    # 檢查 Redis 是否可用
    if redis_client and await redis_client.exists(cooldown_key):
        raise LikeProfileError("您最近已經按過讚了，請稍後再試。", cooldown=True)

    await profile_repository.increment_like_count(owner_id)

    # 設置冷卻時間（如果 Redis 可用）
    if redis_client:
        await redis_client.setex(cooldown_key, LIKE_COOLDOWN_SECONDS, "1")


async def _verify_user_owns_card(user_id: int, card_id: int) -> Dict[str, Any]:
    """驗證用戶是否擁有指定的卡片"""
    query = """
        SELECT uc.id as collection_id, uc.card_id, mc.name, uc.quantity
        FROM gacha_user_collections uc
        JOIN gacha_master_cards mc ON uc.card_id = mc.card_id
        WHERE uc.user_id = $1 AND uc.card_id = $2
        LIMIT 1
    """
    # 根據規範，移除 try-except，讓資料庫錯誤自然冒泡
    pool = get_pool()
    if pool is None:
        raise DatabaseOperationError("資料庫連線池未初始化")
    async with pool.acquire() as connection:
        result = await connection.fetchrow(query, user_id, card_id)
    if result:
        return dict(result)
    raise UserDoesNotOwnCardError(
        f"您並未擁有ID為 {card_id} 的卡片，或該卡片不存在。",
        card_id=card_id,
        user_id=user_id,
    )


async def set_user_status(user_id: int, status: str) -> Optional[str]:
    """設定用戶個性簽名"""
    # 根據規範，移除 try-except，讓業務邏輯和資料庫錯誤自然冒泡
    if len(status) > 150:
        raise ProfileUpdateError("個性簽名不能超過150個字符")

    # 清理HTML標籤，防止XSS攻擊
    sanitized_status = (
        bleach.clean(status.strip(), tags=[], strip=True) if status else None
    )
    await profile_repository.update_user_status(user_id, sanitized_status)
    await invalidate_profile_cache(user_id)
    return sanitized_status


async def _cleanup_user_background_images(user_id: int) -> None:
    """清理用戶的背景圖片目錄"""
    await asyncio.to_thread(_cleanup_sync, user_id)


def _cleanup_sync(user_id: int):
    """同步清理目錄"""
    user_bg_dir = os.path.join(BACKGROUND_IMAGES_BASE_PATH, str(user_id))
    if os.path.exists(user_bg_dir):
        # 根據規範，移除 try-except，讓檔案操作錯誤自然冒泡
        shutil.rmtree(user_bg_dir)
        logger.info("已清理用戶背景圖片目錄: %s", user_bg_dir)


async def cleanup_preview_files(preview_files: List[str]) -> None:
    """清理 preview 檔案列表"""
    if not preview_files:
        return

    def _cleanup_sync():
        for file_path in preview_files:
            # 根據規範，移除 try-except，讓檔案操作錯誤自然冒泡
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.debug("已清理 preview 檔案: %s", file_path)

    await asyncio.to_thread(_cleanup_sync)


async def get_profile_image_url(
    bot: discord.Client, user_id: int, user_name: str
) -> Optional[str]:
    """獲取玩家個人檔案圖片的CDN URL"""
    profile_data = None
    # 根據規範，移除 try-except，讓錯誤自然冒泡
    try:
        if cached_url := await get_cached_profile_image_url(user_id):
            logger.debug("Database cache hit for user %s: %s", user_id, cached_url)
            return cached_url

        discord_user = await _get_discord_user(bot, user_id)
        profile_data = await get_profile_data(
            user_id=user_id,
            discord_display_name=discord_user.display_name
            if discord_user
            else user_name,
            avatar_url=str(discord_user.display_avatar.url)
            if discord_user and discord_user.display_avatar
            else None,
        )

        image_bytes = await profile_image_generator.generate_profile_image(profile_data)

        if private_channel_id := getattr(bot, "private_channel_id", None):
            if cdn_url := await _upload_to_cdn(
                bot, image_bytes, user_id, private_channel_id
            ):
                await update_cached_profile_image_url(user_id, cdn_url)
                return cdn_url
        else:
            logger.warning("Private channel for CDN upload is not configured.")
        return None
    finally:
        # 確保即使在生成圖片失敗時也能清理預覽文件
        if (
            profile_data
            and hasattr(profile_data, "_preview_files_to_cleanup")
            and profile_data._preview_files_to_cleanup
        ):
            logger.debug(
                "準備清理 %d 個預覽檔案...", len(profile_data._preview_files_to_cleanup)
            )
            await cleanup_preview_files(profile_data._preview_files_to_cleanup)


async def _get_discord_user(
    bot: discord.Client, user_id: int
) -> Optional[discord.User]:
    """獲取 Discord 用戶對象"""
    # 根據規範，移除 try-except，讓 discord.py 的錯誤自然冒泡
    return await bot.fetch_user(user_id)


async def _upload_to_cdn(
    bot: discord.Client, image_bytes: bytes, user_id: int, private_channel_id: int
) -> Optional[str]:
    """上傳圖片到 Discord CDN"""
    # 根據規範，移除 try-except，讓 discord.py 的錯誤自然冒泡
    private_channel = bot.get_channel(private_channel_id)
    if not private_channel or not isinstance(private_channel, discord.TextChannel):
        logger.warning("私密頻道不可用或類型錯誤: %s", private_channel_id)
        return None
    import io

    temp_image_file = discord.File(
        io.BytesIO(image_bytes), filename=f"profile_{user_id}.png"
    )
    temp_message = await private_channel.send(file=temp_image_file)
    cdn_url = temp_message.attachments[0].url
    logger.info("圖片已上傳到CDN: %s (user_id=%s)", cdn_url, user_id)
    return cdn_url


async def get_profile_settings_view_data(user_id: int) -> Dict[str, Any]:
    """獲取用於 ProfileSettingsView 的輕量級數據，避免昂貴的查詢。"""
    # 根據規範，移除 try-except，讓資料庫錯誤自然冒泡
    # 1. 獲取用戶的基本檔案設定 (get_or_create ensures a profile is returned or an error is raised)
    profile_settings = await profile_repository.get_or_create_user_profile(user_id)

    # 2. 收集所有需要查詢的卡片 collection_id
    collection_ids_to_fetch = []
    if profile_settings.showcased_card_collection_id:
        collection_ids_to_fetch.append(profile_settings.showcased_card_collection_id)

    for i in range(1, 5):
        sub_card_id = getattr(profile_settings, f"sub_card_{i}_collection_id")
        if sub_card_id:
            collection_ids_to_fetch.append(sub_card_id)

    # 3. 批量獲取卡片資訊
    card_details = await profile_repository.get_showcased_cards_for_settings(
        list(set(collection_ids_to_fetch))
    )

    # 4. 組合回傳的數據
    main_card_info = None
    if profile_settings.showcased_card_collection_id:
        main_card_info = card_details.get(profile_settings.showcased_card_collection_id)

    sub_cards_info = {}
    for i in range(1, 5):
        collection_id = getattr(profile_settings, f"sub_card_{i}_collection_id")
        if collection_id and collection_id in card_details:
            sub_cards_info[i] = card_details[collection_id]

    return {
        "profile_settings": profile_settings,
        "main_card": main_card_info,
        "sub_cards": sub_cards_info,
        "like_count": profile_settings.like_count,
        "background_image_url": profile_settings.background_image_url,
        "user_status": profile_settings.user_status,
    }
