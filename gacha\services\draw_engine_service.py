from __future__ import annotations

import asyncio
import random
import time
from typing import Any, Dict, List, Optional, Tuple

from config.app_config import get_all_pool_rarity_configs, get_mixed_pool_draw_config
from gacha.constants import RarityLevel
from gacha.exceptions import UserNotFoundError
from gacha.repositories.card import master_card_repository
from gacha.repositories.collection import user_wish_repository
from gacha.services import card_pool_cache, user_service, wish_service
from gacha.utils.random_utils import select_from_weighted_list
from utils.logger import logger

"""
Gacha系統抽卡決策引擎服務

負責處理抽卡過程中「決定抽取哪張卡片」的核心演算法和邏輯。專注於：
- 根據卡池配置和稀有度確率決定抽取的卡片
- 處理許願邏輯和權重計算
- 提供隨機選擇卡片的功能

此服務不處理用戶數據、收藏或數據庫事務，僅專注於抽卡決策邏輯。

優化特性：
- ✅ 修復緩存導致的固定順序問題
- ✅ 改進隨機性質量
- ✅ 優化數據庫索引性能
- ✅ 添加雙重隨機化保險機制
"""


async def _get_all_card_pools(
    pool_types: List[str],
) -> Dict[str, Dict[RarityLevel, List[int]]]:
    """
    【快取優化】從全局卡片主快取中篩選出所需的卡池資料。
    """
    # 1. 從快取獲取所有卡片的主資料
    master_cache = await card_pool_cache.get_master_card_cache()
    if not master_cache:
        logger.error("[DRAW_ENGINE] 無法獲取卡片主資料快取，抽卡流程可能失敗。")
        return {}

    # 2. 在記憶體中根據 pool_types 進行篩選和組織
    # 使用 set 進行高效查找
    target_pool_types = set(pool_types)
    card_pools: Dict[str, Dict[RarityLevel, List[int]]] = {
        pt: {} for pt in target_pool_types
    }

    for card_record in master_cache:
        pool_type = card_record.get("pool_type")
        if pool_type in target_pool_types:
            try:
                rarity = RarityLevel(card_record.get("rarity"))
                card_id = card_record.get("card_id")

                if isinstance(card_id, int):  # 確保 card_id 是整數
                    if rarity not in card_pools[pool_type]:
                        card_pools[pool_type][rarity] = []
                    card_pools[pool_type][rarity].append(card_id)
                else:
                    logger.warning(
                        f"[DRAW_ENGINE] 發現無效的 card_id: {card_id}，記錄: {card_record}"
                    )
            except (ValueError, TypeError) as e:
                logger.warning(
                    f"[DRAW_ENGINE] 處理卡片記錄時出錯: {card_record}, 錯誤: {e}"
                )
                continue

    return card_pools


async def _select_card_from_prefetched_pool(
    user_id: int,
    rarity: RarityLevel,
    pool_type: str,
    wish_power_level: int,
    wished_card_ids: List[int],
    prefetched_pools: Dict[str, Dict[RarityLevel, List[int]]],
) -> Tuple[Optional[int], bool]:
    """從預先獲取的卡池中選擇一張卡片"""
    all_card_ids = prefetched_pools.get(pool_type, {}).get(rarity, [])
    if not all_card_ids:
        logger.warning(
            "[DRAW_ENGINE] 卡池 %s 中沒有預取到稀有度為 %s 的卡片", pool_type, rarity
        )
        return None, False

    # 如果沒有許願服務或許願卡片，直接隨機選擇
    if not hasattr(wish_service, "apply_wish_weights") or not wished_card_ids:
        return (_randomly_select_card(all_card_ids), False)

    # 檢查許願卡片是否在可用卡片池中
    available_cards_set = set(all_card_ids)
    valid_wish_cards = [cid for cid in wished_card_ids if cid in available_cards_set]
    if not valid_wish_cards:
        return (_randomly_select_card(all_card_ids), False)

    # 將許願權重計算和抽卡邏輯打包成同步函式，使用 asyncio.to_thread 避免阻塞主執行緒
    def draw_with_wishes_sync(items, wishes, power_level):
        """同步執行許願抽卡邏輯，包含權重計算和卡片選擇"""
        # 步驟 1: 應用權重 (使用優化後的列表推導式版本)
        weighted_items = wish_service.apply_wish_weights(items, wishes, power_level, lambda x: x)
        # 步驟 2: 根據權重選擇
        return wish_service.select_with_weights(weighted_items)

    # 使用 to_thread 執行這個打包好的同步函式，避免阻塞事件循環
    try:
        selected_card = await asyncio.to_thread(
            draw_with_wishes_sync,
            all_card_ids,
            valid_wish_cards,
            wish_power_level
        )
        is_wish_card = selected_card in valid_wish_cards if selected_card else False
        
        # 如果許願選擇失敗，回退到隨機選擇
        if selected_card is None:
            logger.warning(
                "[DRAW_ENGINE] 用戶 %s 的許願選擇邏輯失敗，回退到隨機選擇", user_id
            )
            return (_randomly_select_card(all_card_ids), False)

        return selected_card, is_wish_card
        
    except Exception as e:
        logger.error(
            "[DRAW_ENGINE] 用戶 %s 的許願抽卡處理發生異常: %s，回退到隨機選擇", 
            user_id, e, exc_info=True
        )
        return (_randomly_select_card(all_card_ids), False)


async def determine_cards_to_draw(
    user_id: int, pool_types: List[str], count: int
) -> Tuple[List[Dict[str, Any]], List[int]]:
    """
    決定要抽取的卡片列表 (優化版)
    """
    start_time = time.time()

    if not pool_types or count <= 0:
        logger.error(
            "[DRAW_ENGINE] 用戶 %s 抽卡時提供了無效的卡池或數量: %s, %s",
            user_id,
            pool_types,
            count,
        )
        return [], []

    # 1. 一次性獲取許願信息
    wish_start = time.time()
    user_wish_info = await _get_user_wish_info(user_id)
    user_wish_power_level = user_wish_info.get("power_level", 0)
    wish_cards_by_rarity = user_wish_info.get("wish_cards_by_rarity", {})
    wish_time = time.time() - wish_start

    # 2. 【優化核心】一次性獲取所有需要的卡池
    pool_fetch_start = time.time()
    prefetched_card_pools = await _get_all_card_pools(pool_types)
    pool_fetch_time = time.time() - pool_fetch_start

    # 3. 在記憶體中執行抽卡決策
    draw_start = time.time()
    draw_decisions = []
    all_drawn_card_ids = []
    for _ in range(count):
        # 3.1. 選擇卡池類型
        selected_pool = _select_pool_type(pool_types)

        # 3.2. 選擇稀有度
        selected_rarity = _select_rarity(selected_pool)

        # 3.3. 從預取池中選擇卡片
        wished_card_ids = wish_cards_by_rarity.get(selected_rarity, [])

        # 直接在此處實現卡片選擇邏輯
        card_id, is_wish = await _select_card_from_prefetched_pool(
            user_id=user_id,
            rarity=selected_rarity,
            pool_type=selected_pool,
            wish_power_level=user_wish_power_level,
            wished_card_ids=wished_card_ids,
            prefetched_pools=prefetched_card_pools,
        )

        if card_id:
            all_drawn_card_ids.append(card_id)
            draw_decisions.append(
                {"card_id": card_id, "pool_type": selected_pool, "is_wish": is_wish}
            )
    draw_time = time.time() - draw_start

    # 5. 批次獲取卡片詳情
    details_start = time.time()
    final_results = await _get_cards_details(draw_decisions)
    details_time = time.time() - details_start

    total_time = time.time() - start_time
    if total_time > 0.5:
        logger.warning(
            "[PERF] 抽卡決策耗時較長: 用戶=%s, 數量=%d, 耗時=%.3fs (許願=%.3fs, 卡池=%.3fs, 抽卡=%.3fs, 詳情=%.3fs)",
            user_id,
            count,
            total_time,
            wish_time,
            pool_fetch_time,
            draw_time,
            details_time,
        )

    return final_results, all_drawn_card_ids


async def _get_user_wish_info(user_id: int) -> Dict[str, Any]:
    """獲取用戶許願信息"""
    result = {"power_level": 0, "wish_cards_by_rarity": {}}
    try:
        # 直接使用導入的模組，而不是檢查 wish_service 的屬性
        # 獲取用戶許願力度（從用戶表中讀取）
        user = await user_service.get_user(user_id, create_if_missing=False)
        if user and user.wish_power_level:
            result["power_level"] = user.wish_power_level
        else:
            result["power_level"] = 1  # 默認為1而不是0

        # 獲取許願卡片列表（按稀有度分組）
        wish_cards = await user_wish_repository.get_all_user_wishes_with_rarity_grouped(
            user_id
        )
        result["wish_cards_by_rarity"] = wish_cards

    except UserNotFoundError:
        # 這是正常情況，例如新用戶第一次抽卡
        logger.info("[DRAW_ENGINE] 找不到用戶 %s 的許願信息，可能為新用戶。", user_id)
        result["power_level"] = 1
    except Exception as e:
        logger.warning(
            "[DRAW_ENGINE] 獲取用戶 %s 許願信息失敗: %s", user_id, e, exc_info=True
        )
        # 發生錯誤時，至少保證許願力度為1而不是0
        result["power_level"] = 1
    return result


async def _get_cards_details(
    draw_decisions: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """獲取卡片詳細信息"""
    if not draw_decisions:
        return []
    unique_card_ids = list(
        {decision["card_id"] for decision in draw_decisions if decision["card_id"]}
    )
    cards_map = {}
    if unique_card_ids:
        cards = await master_card_repository.get_cards_details_by_ids(unique_card_ids)
        cards_map = {card.card_id: card for card in cards}
    final_results = []
    for decision in draw_decisions:
        card_id = decision["card_id"]
        card = cards_map.get(card_id)
        if card:
            final_results.append(
                {
                    "card": card,
                    "pool_type": decision["pool_type"],
                    "is_wish": decision.get("is_wish", False),
                }
            )
    return final_results


def _select_pool_type(valid_pools: List[str]) -> str:
    """
    從有效卡池列表中根據權重選擇一個卡池類型

    Args:
        valid_pools: 有效卡池列表

    Returns:
        選中的卡池類型
    """
    if not valid_pools:
        logger.warning("[DRAW_ENGINE] 未提供有效卡池，默認使用'main'")
        return "main"
    if len(valid_pools) == 1:
        return valid_pools[0]
    mixed_pool_config = get_mixed_pool_draw_config()
    if not mixed_pool_config:
        logger.warning("[DRAW_ENGINE] 無法獲取混合池配置，使用第一個有效卡池")
        return valid_pools[0]
    valid_pool_configs = []
    for item in mixed_pool_config:
        pool_name = getattr(item, "pool_name", None)
        weight = getattr(item, "probability", None)
        if pool_name and weight and (pool_name in valid_pools):
            valid_pool_configs.append((pool_name, float(weight)))
    if not valid_pool_configs:
        logger.warning("[DRAW_ENGINE] 混合池配置中沒有匹配的有效卡池: %s", valid_pools)
        return valid_pools[0]
    selected_pool = select_from_weighted_list(valid_pool_configs)
    return selected_pool if selected_pool else valid_pools[0]


def _select_rarity(pool_type: str) -> RarityLevel:
    """
    從指定卡池的稀有度配置中選擇一個稀有度

    Args:
        pool_type: 卡池類型

    Returns:
        選中的稀有度
    """
    pool_rarity_config = get_all_pool_rarity_configs().get(pool_type)
    if not pool_rarity_config:
        logger.warning(
            "[DRAW_ENGINE] 卡池 '%s' 沒有稀有度配置，默認使用COMMON", pool_type
        )
        return RarityLevel.COMMON
    weighted_items = []
    for item in pool_rarity_config:
        rarity_val = getattr(item, "rarity_level", None)
        weight = getattr(item, "probability", None)
        if rarity_val is not None and weight is not None:
            try:
                weighted_items.append((RarityLevel(rarity_val), float(weight)))
            except (ValueError, TypeError) as e:
                logger.error(
                    "[DRAW_ENGINE] 卡池 '%s' 的稀有度配置無效: %s", pool_type, e
                )
                continue
    if not weighted_items:
        logger.error(
            "[DRAW_ENGINE] 卡池 '%s' 沒有有效的稀有度配置項，默認使用COMMON", pool_type
        )
        return RarityLevel.COMMON
    selected_rarity = select_from_weighted_list(weighted_items)
    return selected_rarity if selected_rarity else RarityLevel.COMMON


def _randomly_select_card(card_ids: List[int]) -> Optional[int]:
    """
    從卡片ID列表中隨機選擇一張卡片

    Args:
        card_ids: 卡片ID列表

    Returns:
        選中的卡片ID，如果列表為空則返回None
    """
    if not card_ids:
        return None

    # 🚀 優化：使用 random.choice() 直接選擇，O(1)時間複雜度，更高效且隨機性更好
    return random.choice(card_ids)
