import heapq
import math
import random
from typing import Any, Dict, List, Tuple

from database.postgresql.async_manager import get_pool
from gacha.constants import RarityLevel
from gacha.exceptions import (
    CardAlreadyInWishListError,
    CardNotInWishListError,
    InvalidOperationError,
    WishListFullError,
)
from gacha.repositories.collection import user_wish_repository
from gacha.services import (
    economy_service,
    user_service,
    validation_service,
)
from utils.logger import logger

# --- 模組級函數 ---


async def add_wish(user_id: int, card_id: int) -> Dict[str, Any]:
    """將卡片添加到用戶的許願列表"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    await validation_service.ensure_user_exists(user_id, create_if_missing=False)
    await validation_service.ensure_card_exists(card_id)
    inserted_id_val = None
    slot_index_val = -1
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        async with conn.transaction():
            wishes = await user_wish_repository.get_user_wishes(
                user_id, connection=conn
            )
            if any((wish["card_id"] == card_id for wish in wishes)):
                raise CardAlreadyInWishListError(card_id=card_id)

            user_for_slots = await user_service.get_user(user_id, connection=conn)
            if not user_for_slots:
                logger.error(
                    "[wish_service] add_wish: User %s not found via user_service.get_user within transaction after initial validation.",
                    user_id,
                )
                raise RuntimeError("無法在事務中獲取用戶信息以檢查許願槽位")

            wish_slots = user_for_slots.wish_slots
            if len(wishes) >= wish_slots:
                raise WishListFullError(current_slots=wish_slots)

            used_slots = {wish["slot_index"] for wish in wishes}
            current_slot_index = 0
            while current_slot_index in used_slots:
                current_slot_index += 1
            slot_index_val = current_slot_index
            inserted_id_val = await user_wish_repository.add_user_wish(
                user_id, card_id, slot_index_val, connection=conn
            )
            if inserted_id_val is None:
                raise RuntimeError("添加許願失敗，倉庫未能成功插入記錄")

            # 【新增】在同一事務中更新市場統計
            from gacha.services.direct_market_stats_updater import (
                update_market_stats_in_transaction,
            )

            wishlist_changes = [(card_id, 1)]
            await update_market_stats_in_transaction(
                conn=conn,
                drawn_card_ids=[],
                owner_changes=[],
                wishlist_changes=wishlist_changes,
            )

    return {"slot_index": slot_index_val}


async def remove_wish(user_id: int, card_id: int) -> Dict[str, Any]:
    """從用戶的許願列表中移除卡片"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    await validation_service.ensure_user_exists(user_id, create_if_missing=False)
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    rows_affected = 0
    async with pool.acquire() as conn:
        async with conn.transaction():
            rows_affected = await user_wish_repository.remove_user_wish(
                user_id, card_id, connection=conn
            )
            if rows_affected == 0:
                raise CardNotInWishListError(card_id=card_id)
            elif rows_affected != 1:
                raise RuntimeError(f"移除許願時發生意外，影響行數: {rows_affected}")

            # 【新增】在同一事務中更新市場統計
            from gacha.services.direct_market_stats_updater import (
                update_market_stats_in_transaction,
            )

            wishlist_changes = [(card_id, -1)]
            await update_market_stats_in_transaction(
                conn=conn,
                drawn_card_ids=[],
                owner_changes=[],
                wishlist_changes=wishlist_changes,
            )

    return {}


async def get_wish_list(user_id: int) -> Dict[str, Any]:
    """獲取用戶的許願列表及相關信息"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    # get_user(create_if_missing=True) 永遠不會返回 None，失敗時會拋出異常
    user = await user_service.get_user(user_id, create_if_missing=True)
    from config.app_config import (
        get_default_wish_power_level,
        get_default_wish_slots,
    )

    default_wish_slots = get_default_wish_slots(default_value=1)
    default_wish_power_level = get_default_wish_power_level(default_value=1)

    wish_slots = user.wish_slots if user.wish_slots is not None else default_wish_slots
    wish_power_level = (
        user.wish_power_level
        if user.wish_power_level is not None
        else default_wish_power_level
    )
    nickname = user.nickname

    wishes_details_list = await user_wish_repository.get_user_wishes_with_details(
        user_id
    )
    wish_list = []
    if wishes_details_list:
        for wish_detail in wishes_details_list:
            card_info_raw = wish_detail.get("card_info")
            if not card_info_raw:
                logger.warning(
                    "Wish item for user %s missing card_info: %s",
                    user_id,
                    wish_detail,
                )
                continue
            raw_rarity = card_info_raw.get("rarity")
            try:
                rarity_enum = (
                    RarityLevel(int(raw_rarity))
                    if raw_rarity is not None
                    else RarityLevel.COMMON
                )
            except ValueError:
                logger.warning(
                    "Invalid rarity value '%s' for card_id %s. Defaulting to COMMON.",
                    raw_rarity,
                    card_info_raw.get("card_id"),
                )
                rarity_enum = RarityLevel.COMMON
            card_info_formatted = {
                "card_id": card_info_raw.get("card_id"),
                "name": card_info_raw.get("name"),
                "series": card_info_raw.get("series"),
                "rarity": rarity_enum,
                "image_url": card_info_raw.get("image_url"),
                "pool_type": card_info_raw.get("pool_type"),
            }
            wish_item = {
                "card_id": wish_detail.get("card_id"),
                "slot_index": wish_detail.get("slot_index"),
                "card_info": card_info_formatted,
            }
            wish_list.append(wish_item)

    used_slots_count = len(wish_list)

    return {
        "wishes": wish_list,
        "wish_slots": wish_slots,
        "wish_power_level": wish_power_level,
        "used_slots": used_slots_count,
        "user_nickname": nickname,
    }


def calculate_next_slot_cost(current_slots: int) -> int:
    """計算下一個許願槽位的成本"""
    from config.app_config import get_wish_max_slots, get_wish_slot_costs

    max_wish_slots = get_wish_max_slots(default_value=10)
    if current_slots >= max_wish_slots:
        return -1
    slot_costs = get_wish_slot_costs()
    return slot_costs.get(current_slots, -1)


def calculate_next_power_cost(current_level: int) -> int:
    """計算下一個許願力度等級的成本"""
    from config.app_config import get_wish_max_power_level, get_wish_power_costs

    max_wish_power_level = get_wish_max_power_level(default_value=10)
    if current_level >= max_wish_power_level:
        return -1
    power_costs = get_wish_power_costs()
    return power_costs.get(current_level, -1)


async def expand_slot(user_id: int) -> Dict[str, Any]:
    """為用戶擴充許願槽位"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        async with conn.transaction():
            await validation_service.ensure_user_exists(
                user_id, create_if_missing=False, connection=conn
            )
            user = await user_service.user_repo.get_user_for_update(
                user_id, connection=conn
            )
            if not user:
                logger.error(
                    "[wish_service] expand_slot: User %s not found with get_user_for_update after ensure_user_exists. UNEXPECTED.",
                    user_id,
                )
                raise RuntimeError("獲取用戶以鎖定記錄時發生意外錯誤")

            current_slots = user.wish_slots
            from config.app_config import get_wish_max_slots

            max_wish_slots = get_wish_max_slots(default_value=10)
            if current_slots >= max_wish_slots:
                raise InvalidOperationError(f"已達到許願槽位上限（{max_wish_slots}）")

            cost = calculate_next_slot_cost(current_slots)
            if cost == -1:
                raise InvalidOperationError(f"已達到許願槽位上限（{max_wish_slots}）")

            new_balance = await economy_service.award_oil(
                user_id=user_id,
                amount=-cost,
                transaction_type="wish:expand_slot",
                reason="Expand wish slot",
                connection=conn,
            )

            new_slots = current_slots + 1
            await user_service.user_repo.update_wish_slots(
                user_id, new_slots, connection=conn
            )

    return {"new_slots": new_slots, "cost": cost, "new_balance": new_balance}


async def power_up(user_id: int) -> Dict[str, Any]:
    """為用戶提升許願力度等級"""
    # 根據規範，移除 try...except，讓異常自然冒泡
    pool = get_pool()
    assert pool is not None, "Database connection pool is not initialized"
    async with pool.acquire() as conn:
        async with conn.transaction():
            await validation_service.ensure_user_exists(
                user_id, create_if_missing=False, connection=conn
            )
            user = await user_service.user_repo.get_user_for_update(
                user_id, connection=conn
            )
            if not user:
                logger.error(
                    "[wish_service] power_up: User %s not found with get_user_for_update after ensure_user_exists. UNEXPECTED.",
                    user_id,
                )
                raise RuntimeError("獲取用戶以鎖定記錄時發生意外錯誤")

            current_level = user.wish_power_level
            from config.app_config import get_wish_max_power_level

            max_wish_power_level = get_wish_max_power_level(default_value=10)
            if current_level >= max_wish_power_level:
                raise InvalidOperationError(
                    f"已達到許願力度上限（{max_wish_power_level}）"
                )

            cost = calculate_next_power_cost(current_level)
            if cost == -1:
                raise InvalidOperationError(
                    f"已達到許願力度上限（{max_wish_power_level}）"
                )

            new_balance = await economy_service.award_oil(
                user_id=user_id,
                amount=-cost,
                transaction_type="wish:upgrade_power",
                reason="Upgrade wish power",
                connection=conn,
            )

            new_level = current_level + 1
            await user_service.user_repo.update_wish_power_level(
                user_id, new_level, connection=conn
            )

    return {"new_level": new_level, "cost": cost, "new_balance": new_balance}


def get_wish_chance_multiplier(power_level: int) -> float:
    """根據用戶的許願力度等級，計算抽中許願卡片的概率提升倍數。"""
    from config.app_config import (
        get_default_wish_multiplier,
        get_wish_max_power_level,
        get_wish_power_multipliers,
    )

    max_wish_power_level = get_wish_max_power_level(default_value=10)
    default_wish_multiplier = get_default_wish_multiplier(default_value=3.0)

    power_level = max(1, min(power_level, max_wish_power_level))
    multipliers = get_wish_power_multipliers()
    return multipliers.get(power_level, default_wish_multiplier)


def select_with_weights(weighted_items: List[Tuple[Any, float]]) -> Any:
    """從帶權重的項目列表中隨機選擇一個項目。"""
    if not weighted_items:
        raise InvalidOperationError("無法從空列表中選擇項目。")

    positive_weighted_items = [
        (item, weight) for item, weight in weighted_items if weight > 0
    ]
    if not positive_weighted_items:
        raise InvalidOperationError("沒有帶有正權重的項目可供選擇。")

    total_weight = sum((weight for _, weight in positive_weighted_items))
    if total_weight <= 0:
        # 這種情況理論上不應該發生，因為上面已經過濾了正權重
        raise InvalidOperationError(f"項目權重總和為非正數: {total_weight}")

    r = random.uniform(0, total_weight)
    current_weight_sum = 0
    for item, weight in positive_weighted_items:
        current_weight_sum += weight
        if r < current_weight_sum:
            return item
    logger.warning(
        "[許願服務] select_with_weights: 循環意外結束 (r=%s, total_weight=%s)。返回最後一個帶有正權重的項目。",
        r,
        total_weight,
    )
    return positive_weighted_items[-1][0]


def select_multiple_with_weights(
    weighted_items: List[Tuple[Any, float]], k: int
) -> List[Any]:
    """使用 A* 加權隨機抽樣算法 (不放回) 從帶權重的列表中選擇 k 個唯一的項目。"""
    if not weighted_items:
        logger.debug(
            "[許願服務] select_multiple_with_weights: 輸入的 weighted_items 為空。"
        )
        return []
    if k <= 0:
        logger.debug("[許願服務] select_multiple_with_weights: k <= 0，返回空列表。")
        return []
    keyed_items = []
    positive_weight_items_count = 0
    for item, weight in weighted_items:
        if weight <= 0:
            logger.debug(
                "[許願服務] select_multiple_with_weights: 忽略權重非正的項目，權重值: %s。",
                weight,
            )
            continue
        positive_weight_items_count += 1
        u = random.uniform(1e-12, 1.0)
        log_key = 1.0 / weight * math.log(u)
        keyed_items.append((log_key, item))
    n = positive_weight_items_count
    if k >= n:
        logger.debug(
            "[許願服務] select_multiple_with_weights: k (%s) >= 帶正權重的可用項目數 (%s)。返回所有 %s 個可用項目並打亂順序。",
            k,
            n,
            n,
        )
        items = [item for _, item in keyed_items]
        random.shuffle(items)
        return items
    if n == 0:
        logger.warning(
            "[許願服務] select_multiple_with_weights: 未找到帶有正權重的項目。"
        )
        return []
    top_k_keyed = heapq.nlargest(k, keyed_items, key=lambda x: x[0])
    selected_items = [item for _, item in top_k_keyed]
    logger.debug(
        "[許願服務] select_multiple_with_weights: 從 %s 個帶正權重的可用項目中選擇了 %s 個項目 (k=%s)。",
        n,
        len(selected_items),
        k,
    )
    return selected_items


def apply_wish_weights(
    items: List[Any],
    wished_item_ids: List[int],
    wish_power_level: int = 1,
    id_extractor=lambda x: x["card_id"],
) -> List[Tuple[Any, float]]:
    """將許願權重應用於一個項目列表，增加許願列表中項目的相對權重。"""
    weight_multiplier = get_wish_chance_multiplier(wish_power_level)
    logger.debug(
        "[許願服務] 應用許願權重，倍數: %s (許願強度: %s)",
        weight_multiplier,
        wish_power_level,
    )
    
    wished_items_set = set(wished_item_ids)
    
    def safe_get_weight(item):
        """安全地提取項目權重，出錯時返回默認權重 1.0"""
        try:
            item_id = id_extractor(item)
            return weight_multiplier if item_id in wished_items_set else 1.0
        except Exception as e:
            logger.error(
                "[許願服務] 在 apply_wish_weights 中提取 ID 或處理項目時出錯: %s。錯誤: %s",
                item,
                e,
                exc_info=False,
            )
            return 1.0
    
    # 使用列表推導式進行優化
    weighted_results = [(item, safe_get_weight(item)) for item in items]
    
    logger.debug(
        "[許願服務] 完成權重應用。原始項目數: %s，許願 ID 數: %s，結果加權項目數: %s",
        len(items),
        len(wished_item_ids),
        len(weighted_results),
    )
    boosted_count = sum((1 for _, w in weighted_results if w == weight_multiplier))
    logger.debug("[許願服務] 獲得權重提升的項目數量: %s", boosted_count)
    return weighted_results


async def get_wishes_status_batch(user_id: int, card_ids: List[int]) -> Dict[int, bool]:
    """批量獲取多張卡片的許願狀態"""
    if not card_ids:
        return {}
    # 根據規範，移除 try...except，讓異常自然冒泡
    # 直接查詢許願狀態，不檢查用戶是否存在
    # 如果用戶不存在，wished_card_ids_set 會是空集合，返回全 False
    wished_card_ids_set = await user_wish_repository.get_wished_card_ids_for_user(
        user_id, card_ids
    )
    return {card_id: card_id in wished_card_ids_set for card_id in card_ids}
