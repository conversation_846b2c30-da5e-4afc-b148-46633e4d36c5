"""
Profile 圖片生成器 - 使用 HTML 模板和 Selenium 生成用戶檔案圖片
"""

import asyncio
import base64
import io
import os

# Import the unified PlaywrightManager
# Ensure 'utils' directory is accessible
import sys
from pathlib import Path
from typing import Dict, Optional
from urllib.parse import urljoin

import aiohttp
from PIL import Image
from playwright.async_api import Page

# Add project root to path before importing local modules
project_root = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)  # Adjust path if needed
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Now import local modules after path setup
from gacha.models.profile_models import (  # noqa: E402
    ProfileData,  # ProfileImageGenerationData seems unused
)
from utils.logger import logger  # noqa: E402  # Keep using the existing logger setup
from utils.playwright_manager import (  # noqa: E402  # 直接導入函數
    acquire_page,
    release_page,
)

# Define the base directory for the project
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", ".."))

# 模組級別的初始化
template_path = os.path.join(
    os.path.dirname(__file__), "templates", "profile_template.html"
)
injector_script_path = os.path.join(
    os.path.dirname(__file__),
    "profile_injector.js",  # 相對於當前文件
)
try:
    with open(injector_script_path, "r", encoding="utf-8") as f:
        injector_script = f.read()
    logger.info(
        "Profile圖片生成器模組初始化 (Playwright)，模板路徑: %s, JS注入腳本已載入.",
        template_path,
    )
except FileNotFoundError:
    logger.error(
        "JS注入腳本 %s 未找到! Profile圖片生成可能失敗。", injector_script_path
    )
    injector_script = (
        ""  # Fallback to prevent crashes, though functionality will be broken
    )


async def _get_image_as_base64(url: str) -> Optional[str]:
    """從 URL 獲取圖片並轉換為 base64 data URI"""
    if not url or not url.startswith(("http://", "https://")):
        logger.warning("Invalid or empty URL provided for base64 conversion: %s", url)
        return None

    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    image_data = await response.read()
                    content_type = response.headers.get("Content-Type", "image/png")
                    if not content_type.startswith("image/"):
                        logger.warning(
                            "URL %s did not return an image. Content-Type: %s",
                            url,
                            content_type,
                        )
                        return None
                    base64_encoded_data = base64.b64encode(image_data).decode("utf-8")
                    return f"data:{content_type};base64,{base64_encoded_data}"
                else:
                    logger.error(
                        "Failed to fetch image from %s. Status: %s",
                        url,
                        response.status,
                    )
                    return None
    except Exception as e:
        logger.error("Error fetching image from %s: %s", url, e)
        return None


def _generate_stars_html(star_level: int) -> str:
    """生成星級HTML (Unchanged)

    實現每5星循環顯示星星數量，並在每組使用不同顏色：
    - 第1組5星(1-5星)：金色，顯示1-5顆星
    - 第2組5星(6-10星)：橙色，顯示1-5顆星
    - 第3組5星(11-15星)：紅橙色，顯示1-5顆星
    - 第4組5星(16-20星)：品紅色，顯示1-5顆星
    - 第5組5星(21-25星)：紫色，顯示1-5顆星
    - 第6組5星(26-30星)：藍色，顯示1-5顆星
    - 第7組5星(31-35星)：綠色，顯示1-5顆星

    例如：3星顯示3顆金色星，7星顯示2顆橙色星，12星顯示2顆紅橙色星
    """
    if star_level <= 0:
        return ""

    # 定義每組5星的顏色類別
    star_colors = [
        "star-normal",  # 1-5星: 金色
        "star-special",  # 6-10星: 橙色
        "star-rare",  # 11-15星: 紅橙色
        "star-epic",  # 16-20星: 品紅色
        "star-legendary",  # 21-25星: 紫色
        "star-mythic",  # 26-30星: 藍色
        "star-ultimate",  # 31-35星: 綠色
    ]

    # 計算當前所在組和在該組內の位置
    group_index = (star_level - 1) // 5
    position_in_group = (star_level - 1) % 5 + 1  # 在組內的位置 (1-5)

    # 獲取顏色類別，避免索引超出範圍
    color_class = star_colors[min(group_index, len(star_colors) - 1)]

    # 生成對應數量的星星
    stars_html = "".join(
        [f'<span class="{color_class}">★</span>' for _ in range(position_in_group)]
    )

    return stars_html


async def generate_profile_image(profile_data: ProfileData) -> bytes:
    """使用 Playwright 生成檔案圖片 - 優先使用頁面池以提高速度"""
    # 使用頁面池以獲得最佳性能
    page = await acquire_page(use_pool=True)
    logger.info("使用高速模式為用戶 %s 生成檔案圖片", profile_data.user_id)

    try:
        return await _generate_with_page(page, profile_data)
    finally:
        # 確保頁面被正確釋放
        await release_page(page)


async def _generate_with_page(page: Page, profile_data: ProfileData) -> bytes:
    """使用指定頁面生成檔案圖片的核心邏輯"""
    try:
        image_urls_to_fetch_map: Dict[str, str] = {}
        if profile_data.avatar_url and profile_data.avatar_url.startswith("http"):
            image_urls_to_fetch_map["avatar_url"] = profile_data.avatar_url
        # 背景圖片現在都是本地文件，不需要轉換為 Base64

        if profile_data.main_card:
            card = profile_data.main_card
            if not (
                card.local_image_path
                and os.path.exists(card.local_image_path)
                and os.path.getsize(card.local_image_path) > 0
            ):
                if card.image_url and card.image_url.startswith("http"):
                    image_urls_to_fetch_map[f"main_card_image_url_{card.card_id}"] = (
                        card.image_url
                    )

        for slot, card in profile_data.sub_cards.items():
            if not (
                card.local_image_path
                and os.path.exists(card.local_image_path)
                and os.path.getsize(card.local_image_path) > 0
            ):
                if card.image_url and card.image_url.startswith("http"):
                    image_urls_to_fetch_map[
                        f"sub_card_image_url_{slot}_{card.card_id}"
                    ] = card.image_url

        if image_urls_to_fetch_map:
            logger.info(
                "檢測到 %s 個網絡圖片需要預加载為 Base64。",
                len(image_urls_to_fetch_map),
            )

        base64_encoded_images: Dict[str, Optional[str]] = {}
        if image_urls_to_fetch_map:
            fetch_tasks = [
                _get_image_as_base64(url) for url in image_urls_to_fetch_map.values()
            ]
            results = await asyncio.gather(*fetch_tasks, return_exceptions=True)

            for i, key in enumerate(image_urls_to_fetch_map.keys()):
                result = results[i]
                if isinstance(result, Exception):
                    logger.warning(
                        "Base64 encoding task for image key '%s' failed: %s",
                        key,
                        result,
                    )
                    base64_encoded_images[key] = None
                elif result is None:
                    logger.warning(
                        "Base64 encoding for image key '%s' (%s) returned None.",
                        key,
                        image_urls_to_fetch_map[key],
                    )
                    base64_encoded_images[key] = None
                else:
                    base64_encoded_images[key] = str(result)

        if "avatar_url" in image_urls_to_fetch_map:
            fetched_b64 = base64_encoded_images.get("avatar_url")
            if fetched_b64:
                profile_data.avatar_url = fetched_b64

        # 背景圖片現在都是本地文件，不需要 Base64 處理

        if profile_data.main_card:
            card = profile_data.main_card
            key = f"main_card_image_url_{card.card_id}"
            if key in image_urls_to_fetch_map:
                fetched_b64 = base64_encoded_images.get(key)
                if fetched_b64:
                    card.image_url = fetched_b64
                    card.local_image_path = None

        for slot, card in profile_data.sub_cards.items():
            key = f"sub_card_image_url_{slot}_{card.card_id}"
            if key in image_urls_to_fetch_map:
                fetched_b64 = base64_encoded_images.get(key)
                if fetched_b64:
                    card.image_url = fetched_b64
                    card.local_image_path = None

        # 設置和載入模板（每次都是新頁面）
        # 設置視口大小以匹配模板固定尺寸 (1080x720px)
        await page.set_viewport_size({"width": 1080, "height": 720})

        # 載入HTML模板
        abs_template_path = os.path.abspath(template_path)
        template_file_uri = urljoin("file:", abs_template_path.replace("\\", "/"))
        if not template_file_uri.startswith("file:///"):
            template_file_uri = template_file_uri.replace("file:/", "file:///", 1)
        logger.debug("Loading template from URI: %s", template_file_uri)
        await page.goto(template_file_uri, wait_until="domcontentloaded", timeout=10000)

        # 載入JS腳本
        if injector_script:
            await page.add_script_tag(content=injector_script)

        # 新頁面不需要清理狀態，直接注入用戶資料
        # 注入用戶資料
        await _inject_profile_data(page, profile_data)

        # 等待網絡空閒，確保所有資源（尤其是背景圖）已加載
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 新頁面使用標準超時時間
                timeout = 15000
                await page.wait_for_load_state("networkidle", timeout=timeout)
                logger.info(
                    "Network idle state reached after injecting data (attempt %s).",
                    attempt + 1,
                )
                break
            except Exception as wait_error:
                if attempt < max_retries - 1:
                    logger.warning(
                        "Network idle wait failed (attempt %s): %s. Retrying...",
                        attempt + 1,
                        wait_error,
                    )
                    await asyncio.sleep(1)  # 等待1秒後重試
                else:
                    # 最後一次嘗試失敗，記錄警告但繼續截圖
                    logger.warning(
                        "Network idle wait timed out after %s attempts: %s. Proceeding with screenshot anyway.",
                        max_retries,
                        wait_error,
                    )

        # 優化字體載入等待
        try:
            # 等待字體載入完成，但設定較短的超時
            await page.evaluate(
                """
                () => {
                    return new Promise((resolve) => {
                        if (document.fonts && document.fonts.ready) {
                            document.fonts.ready.then(() => resolve());
                            // 設定 2 秒超時，避免字體載入卡住
                            setTimeout(() => resolve(), 2000);
                        } else {
                            // 如果不支援 document.fonts，直接等待 0.5 秒
                            setTimeout(() => resolve(), 500);
                        }
                    });
                }
            """
            )
            logger.debug("字體載入完成")
        except Exception as font_error:
            logger.warning("字體載入檢查失敗，繼續截圖: %s", font_error)

        # 截圖整個頁面 - 增加超時設定和優化選項
        screenshot_bytes = await page.screenshot(
            type="png",
            full_page=False,
            timeout=15000,  # 減少超時時間到 15 秒
            animations="disabled",  # 禁用動畫
            caret="hide",  # 隱藏游標
        )

        # 使用 PIL 處理圖片 (移到線程中避免阻塞)
        def _process_image():
            img = Image.open(io.BytesIO(screenshot_bytes))

            # (可選) 驗證尺寸，如果 viewport 未完全控制輸出
            # if img.size != (1536, 864):
            #     logger.warning("Screenshot size %s differs from viewport. Resizing to 1536x864.", img.size)
            #     img = img.resize((1536, 864), Image.LANCZOS)

            # 轉換為 WebP bytes
            img_bytes_io = io.BytesIO()
            # WebP 支持的選項: quality (0-100, default 80 for lossy), lossless (True/False)
            # method (0-6, 控制編碼速度和壓縮比的平衡，6最慢但壓縮最好)
            # alpha_quality (0-100, 透明通道質量)
            # 品質設為 100，method 保持為 6 以獲得最佳壓縮
            img.save(img_bytes_io, format="WEBP", quality=100, method=6)
            return img_bytes_io.getvalue()

        result_bytes = await asyncio.to_thread(_process_image)

        logger.info(
            "成功生成檔案圖片 (Playwright, WebP, Q:100)，大小: %s bytes",
            len(result_bytes),
        )
        return result_bytes

    except Exception as e:
        logger.error("生成檔案圖片失敗 (Playwright): %s", e, exc_info=True)
        raise
    # 頁面釋放已在 generate_profile_image 的 finally 中處理


async def _inject_profile_data(page: Page, profile_data: ProfileData):
    """使用 Playwright 注入檔案資料到HTML模板 (已优化 - 直接使用圖片URL 或 Base64)"""
    try:

        def format_number(number):
            if number >= 1000:
                return f"{number / 1000:.1f}k".replace(".0k", "k")
            return f"{number:,}"

        payload = {
            "texts": {},
            "attributes": {},
            "inner_htmls": {},
            "styles": {},
            "display_toggles": {},
        }

        # 基本信息
        if profile_data.nickname:
            payload["texts"]["#username"] = profile_data.nickname
            payload["texts"]["#username-initial"] = profile_data.nickname[
                0
            ].upper()  # JS會處理是否顯示
        else:
            payload["texts"]["#username"] = "Unknown User"
            payload["texts"]["#username-initial"] = "U"

        # 頭像處理 (修改為設置 background-image)
        if profile_data.avatar_url and profile_data.avatar_url.startswith(
            ("http://", "https://", "data:image")
        ):
            payload["styles"]["#user-avatar"] = {
                "background-image": f"url('{profile_data.avatar_url}')"
            }
            payload["display_toggles"]["#username-initial"] = (
                "none"  # 有頭像則隱藏首字母
            )
        else:
            payload["display_toggles"]["#username-initial"] = (
                "block"  # 無頭像則顯示首字母
            )

        payload["texts"][".ml-6 p.text-gray-400"] = f"ID: #{profile_data.user_id}"
        payload["texts"]["#user-status"] = getattr(
            profile_data, "user_status", "「保持熱愛，奔赴下一個SSR！」"
        )
        payload["texts"]["#oil-balance"] = format_number(profile_data.oil_balance)
        payload["texts"]["#oil-ticket-balance"] = format_number(
            profile_data.oil_ticket_balance
        )
        payload["texts"]["#total-draws"] = str(profile_data.total_draws)
        payload["texts"]["#like-count"] = format_number(profile_data.like_count)
        payload["texts"]["#completion-rate"] = (
            f"{profile_data.collection_completion_rate}%"
        )
        payload["texts"]["#total-owned"] = format_number(profile_data.total_owned_cards)

        # 稀有度统计 - 使用格式化数值
        for rarity_id, code in enumerate(["C", "R", "SR", "SSR", "UR", "LR", "EX"], 1):
            count = profile_data.rarity_counts.get(rarity_id, 0)
            payload["texts"][f"#rarity-{code.lower()}-count"] = format_number(count)

        # 主卡片
        if profile_data.main_card:
            payload["display_toggles"]["#main-card-section"] = "flex"
            card = profile_data.main_card

            image_source_set = False
            # 優先使用靜態預覽圖
            if card.static_preview_path:
                full_path = os.path.abspath(
                    os.path.join(BASE_DIR, card.static_preview_path)
                )
                if os.path.exists(full_path):
                    file_uri = Path(full_path).as_uri()
                    payload["attributes"]["#main-card-img"] = {
                        "src": file_uri,
                        "alt": f"{card.name or ''} (Preview)",
                    }
                    image_source_set = True

            # 如果沒有預覽圖，再使用本地圖片
            elif card.local_image_path:
                full_path = os.path.abspath(
                    os.path.join(BASE_DIR, card.local_image_path)
                )
                if os.path.exists(full_path) and os.path.getsize(full_path) > 0:
                    file_uri = Path(full_path).as_uri()
                    payload["attributes"]["#main-card-img"] = {
                        "src": file_uri,
                        "alt": card.name or "",
                    }
                    image_source_set = True

            # 如果以上都沒有，才使用網路 URL
            elif card.image_url:
                payload["attributes"]["#main-card-img"] = {
                    "src": card.image_url,
                    "alt": card.name or "",
                }
                image_source_set = True

            if not image_source_set:
                payload["attributes"]["#main-card-img"] = {
                    "alt": "主卡片圖片無效或缺失",
                    "src": "",
                }
                logger.warning(
                    "Main card '%s' 缺少有效圖片源 (本地路徑或URL/Base64)", card.name
                )

            payload["texts"]["#main-card-name"] = card.name or ""
            payload["texts"]["#main-card-series"] = card.series or ""
            payload["inner_htmls"]["#main-card-stars"] = _generate_stars_html(
                card.star_level
            )
        else:
            payload["display_toggles"]["#main-card-section"] = "none"
            payload["attributes"]["#main-card-img"] = {"alt": "", "src": ""}
            payload["texts"]["#main-card-name"] = ""
            payload["texts"]["#main-card-series"] = ""
            payload["inner_htmls"]["#main-card-stars"] = ""

        # 副卡片
        active_sub_cards_count = 0
        for slot in range(1, 5):
            card_data = profile_data.sub_cards.get(slot)
            card_section_selector = f"#sub-card-container-{slot}"
            card_img_selector = f"#sub-card-img-{slot}"
            card_name_selector = f"#sub-card-name-{slot}"
            # card_series_selector = f"#sub-card-series-{slot}" # Not used in template for subcards
            card_stars_selector = f"#sub-card-stars-{slot}"

            if card_data:
                active_sub_cards_count += 1
                payload["display_toggles"][card_section_selector] = "block"

                sub_image_source_set = False
                # 優先使用靜態預覽圖
                if card_data.static_preview_path:
                    full_path = os.path.abspath(
                        os.path.join(BASE_DIR, card_data.static_preview_path)
                    )
                    if os.path.exists(full_path):
                        file_uri = Path(full_path).as_uri()
                        payload["attributes"][card_img_selector] = {
                            "src": file_uri,
                            "alt": f"{card_data.name or ''} (Preview)",
                        }
                        sub_image_source_set = True

                # 如果沒有預覽圖，再使用本地圖片
                elif card_data.local_image_path:
                    full_path = os.path.abspath(
                        os.path.join(BASE_DIR, card_data.local_image_path)
                    )
                    if os.path.exists(full_path) and os.path.getsize(full_path) > 0:
                        file_uri = Path(full_path).as_uri()
                        payload["attributes"][card_img_selector] = {
                            "src": file_uri,
                            "alt": card_data.name or "",
                        }
                        sub_image_source_set = True

                # 如果以上都沒有，才使用網路 URL
                elif card_data.image_url:
                    payload["attributes"][card_img_selector] = {
                        "src": card_data.image_url,
                        "alt": card_data.name or "",
                    }
                    sub_image_source_set = True

                if not sub_image_source_set:
                    payload["attributes"][card_img_selector] = {
                        "alt": f"副卡片 {slot} 圖片無效或缺失",
                        "src": "",
                    }
                    logger.warning(
                        "Sub card slot %s ('%s') 缺少有效圖片源", slot, card_data.name
                    )

                payload["texts"][card_name_selector] = card_data.name or ""
                # Series name for sub-cards is not in the provided HTML structure for sub-cards, so no injection for it.
                if card_data.star_level > 0:
                    payload["inner_htmls"][card_stars_selector] = _generate_stars_html(
                        card_data.star_level
                    )
                else:
                    payload["inner_htmls"][card_stars_selector] = ""
            else:
                payload["display_toggles"][card_section_selector] = "none"
                payload["attributes"][card_img_selector] = {"alt": "", "src": ""}
                payload["texts"][card_name_selector] = ""
                payload["inner_htmls"][card_stars_selector] = ""

        if active_sub_cards_count == 0:
            payload["display_toggles"]["#sub-cards-section"] = "none"
        else:
            payload["display_toggles"]["#sub-cards-section"] = "flex"
            if active_sub_cards_count < 4:
                payload["styles"]["#sub-cards-row"] = {"justifyContent": "flex-start"}
            else:
                payload["styles"]["#sub-cards-row"] = {
                    "justifyContent": "space-between"
                }

        # 背景圖片 (修改選擇器為 #profile-background)
        # 重要：每次都重置背景樣式，避免共享頁面的狀態污染
        if profile_data.background_image_url:
            if profile_data.background_image_url.startswith(
                ("http://", "https://", "data:image")
            ):
                # Case 1: 網絡URL或已是Base64，直接使用
                payload["styles"]["#profile-background"] = {
                    "background-image": f"url('{profile_data.background_image_url}')"
                }
            else:
                # Case 2: 本地文件路徑
                path_to_check = profile_data.background_image_url
                full_path = os.path.abspath(os.path.join(BASE_DIR, path_to_check))

                if os.path.exists(full_path):
                    try:
                        with open(full_path, "rb") as image_file:
                            encoded_string = base64.b64encode(image_file.read()).decode(
                                "utf-8"
                            )

                        ext = os.path.splitext(full_path)[1].lower()
                        mime_type = {
                            ".png": "image/png",
                            ".jpg": "image/jpeg",
                            ".jpeg": "image/jpeg",
                            ".gif": "image/gif",
                            ".webp": "image/webp",
                        }.get(ext, "image/png")

                        data_uri = f"data:{mime_type};base64,{encoded_string}"
                        payload["styles"]["#profile-background"] = {
                            "background-image": f"url('{data_uri}')"
                        }
                        logger.debug(
                            "成功將本地背景圖片 %s 轉換為 Base64 Data URI。", full_path
                        )
                    except Exception as e:
                        logger.error(
                            "讀取或編碼本地背景圖片失敗: %s - %s",
                            full_path,
                            e,
                            exc_info=True,
                        )
                        payload["styles"]["#profile-background"] = {
                            "background-image": ""
                        }
                else:
                    logger.warning(
                        "Background: 無效的背景圖片路徑 (檔案不存在): %s. 恢復默認背景.",
                        full_path,
                    )
                    payload["styles"]["#profile-background"] = {"background-image": ""}
        else:
            # 沒有背景圖片時，清除內聯樣式以恢復CSS默認背景（解決共享頁面狀態污染問題）
            # 使用空字符串而不是 "none"，這樣會移除內聯樣式，恢復到CSS默認的background.png
            payload["styles"]["#profile-background"] = {"background-image": ""}
            logger.debug("用戶 %s 沒有背景圖片，恢復默認背景", profile_data.user_id)

        # 3. 執行注入腳本
        if not injector_script:
            logger.error("JS注入腳本未載入，無法注入檔案資料。")
            raise RuntimeError(
                "JS injector script not loaded for ProfileImageGenerator"
            )

        await page.add_script_tag(content=injector_script)
        await page.evaluate("data => injectProfileData(data)", payload)

        logger.info(
            "檔案資料注入完成 (Playwright via add_script_tag and evaluate call - 使用直接URL)"
        )

    except Exception as e:
        logger.error("注入檔案資料失敗 (Playwright): %s", e, exc_info=True)
        raise
