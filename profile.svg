<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg version="1.1" width="1200" height="746" onload="init(evt)" viewBox="0 0 1200 746" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:fg="http://github.com/jonhoo/inferno"><!--Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples.--><!--NOTES: --><defs><linearGradient id="background" y1="0" y2="1" x1="0" x2="0"><stop stop-color="#eeeeee" offset="5%"/><stop stop-color="#eeeeb0" offset="95%"/></linearGradient></defs><style type="text/css">
text { font-family:monospace; font-size:12px }
#title { text-anchor:middle; font-size:17px; }
#matched { text-anchor:end; }
#search { text-anchor:end; opacity:0.1; cursor:pointer; }
#search:hover, #search.show { opacity:1; }
#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
#unzoom { cursor:pointer; }
#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
.hide { display:none; }
.parent { opacity:0.5; }
</style><script type="text/ecmascript"><![CDATA[
        var nametype = 'Function:';
        var fontsize = 12;
        var fontwidth = 0.59;
        var xpad = 10;
        var inverted = true;
        var searchcolor = 'rgb(230,0,230)';
        var fluiddrawing = true;
        var truncate_text_right = false;
    ]]><![CDATA["use strict";
var details, searchbtn, unzoombtn, matchedtxt, svg, searching, frames, known_font_width;
function init(evt) {
    details = document.getElementById("details").firstChild;
    searchbtn = document.getElementById("search");
    unzoombtn = document.getElementById("unzoom");
    matchedtxt = document.getElementById("matched");
    svg = document.getElementsByTagName("svg")[0];
    frames = document.getElementById("frames");
    known_font_width = get_monospace_width(frames);
    total_samples = parseInt(frames.attributes.total_samples.value);
    searching = 0;

    // Use GET parameters to restore a flamegraph's state.
    var restore_state = function() {
        var params = get_params();
        if (params.x && params.y)
            zoom(find_group(document.querySelector('[*|x="' + params.x + '"][y="' + params.y + '"]')));
        if (params.s)
            search(params.s);
    };

    if (fluiddrawing) {
        // Make width dynamic so the SVG fits its parent's width.
        svg.removeAttribute("width");
        // Edge requires us to have a viewBox that gets updated with size changes.
        var isEdge = /Edge\/\d./i.test(navigator.userAgent);
        if (!isEdge) {
            svg.removeAttribute("viewBox");
        }
        var update_for_width_change = function() {
            if (isEdge) {
                svg.attributes.viewBox.value = "0 0 " + svg.width.baseVal.value + " " + svg.height.baseVal.value;
            }

            // Keep consistent padding on left and right of frames container.
            frames.attributes.width.value = svg.width.baseVal.value - xpad * 2;

            // Text truncation needs to be adjusted for the current width.
            update_text_for_elements(frames.children);

            // Keep search elements at a fixed distance from right edge.
            var svgWidth = svg.width.baseVal.value;
            searchbtn.attributes.x.value = svgWidth - xpad;
            matchedtxt.attributes.x.value = svgWidth - xpad;
        };
        window.addEventListener('resize', function() {
            update_for_width_change();
        });
        // This needs to be done asynchronously for Safari to work.
        setTimeout(function() {
            unzoom();
            update_for_width_change();
            restore_state();
        }, 0);
    } else {
        restore_state();
    }
}
// event listeners
window.addEventListener("click", function(e) {
    var target = find_group(e.target);
    if (target) {
        if (target.nodeName == "a") {
            if (e.ctrlKey === false) return;
            e.preventDefault();
        }
        if (target.classList.contains("parent")) unzoom();
        zoom(target);

        // set parameters for zoom state
        var el = target.querySelector("rect");
        if (el && el.attributes && el.attributes.y && el.attributes["fg:x"]) {
            var params = get_params()
            params.x = el.attributes["fg:x"].value;
            params.y = el.attributes.y.value;
            history.replaceState(null, null, parse_params(params));
        }
    }
    else if (e.target.id == "unzoom") {
        unzoom();

        // remove zoom state
        var params = get_params();
        if (params.x) delete params.x;
        if (params.y) delete params.y;
        history.replaceState(null, null, parse_params(params));
    }
    else if (e.target.id == "search") search_prompt();
}, false)
// mouse-over for info
// show
window.addEventListener("mouseover", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = nametype + " " + g_to_text(target);
}, false)
// clear
window.addEventListener("mouseout", function(e) {
    var target = find_group(e.target);
    if (target) details.nodeValue = ' ';
}, false)
// ctrl-F for search
window.addEventListener("keydown",function (e) {
    if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
        e.preventDefault();
        search_prompt();
    }
}, false)
// functions
function get_params() {
    var params = {};
    var paramsarr = window.location.search.substr(1).split('&');
    for (var i = 0; i < paramsarr.length; ++i) {
        var tmp = paramsarr[i].split("=");
        if (!tmp[0] || !tmp[1]) continue;
        params[tmp[0]]  = decodeURIComponent(tmp[1]);
    }
    return params;
}
function parse_params(params) {
    var uri = "?";
    for (var key in params) {
        uri += key + '=' + encodeURIComponent(params[key]) + '&';
    }
    if (uri.slice(-1) == "&")
        uri = uri.substring(0, uri.length - 1);
    if (uri == '?')
        uri = window.location.href.split('?')[0];
    return uri;
}
function find_child(node, selector) {
    var children = node.querySelectorAll(selector);
    if (children.length) return children[0];
    return;
}
function find_group(node) {
    var parent = node.parentElement;
    if (!parent) return;
    if (parent.id == "frames") return node;
    return find_group(parent);
}
function orig_save(e, attr, val) {
    if (e.attributes["fg:orig_" + attr] != undefined) return;
    if (e.attributes[attr] == undefined) return;
    if (val == undefined) val = e.attributes[attr].value;
    e.setAttribute("fg:orig_" + attr, val);
}
function orig_load(e, attr) {
    if (e.attributes["fg:orig_"+attr] == undefined) return;
    e.attributes[attr].value = e.attributes["fg:orig_" + attr].value;
    e.removeAttribute("fg:orig_" + attr);
}
function g_to_text(e) {
    var text = find_child(e, "title").firstChild.nodeValue;
    return (text)
}
function g_to_func(e) {
    var func = g_to_text(e);
    // if there's any manipulation we want to do to the function
    // name before it's searched, do it here before returning.
    return (func);
}
function get_monospace_width(frames) {
    // Given the id="frames" element, return the width of text characters if
    // this is a monospace font, otherwise return 0.
    text = find_child(frames.children[0], "text");
    originalContent = text.textContent;
    text.textContent = "!";
    bangWidth = text.getComputedTextLength();
    text.textContent = "W";
    wWidth = text.getComputedTextLength();
    text.textContent = originalContent;
    if (bangWidth === wWidth) {
        return bangWidth;
    } else {
        return 0;
    }
}
function update_text_for_elements(elements) {
    // In order to render quickly in the browser, you want to do one pass of
    // reading attributes, and one pass of mutating attributes. See
    // https://web.dev/avoid-large-complex-layouts-and-layout-thrashing/ for details.

    // Fall back to inefficient calculation, if we're variable-width font.
    // TODO This should be optimized somehow too.
    if (known_font_width === 0) {
        for (var i = 0; i < elements.length; i++) {
            update_text(elements[i]);
        }
        return;
    }

    var textElemNewAttributes = [];
    for (var i = 0; i < elements.length; i++) {
        var e = elements[i];
        var r = find_child(e, "rect");
        var t = find_child(e, "text");
        var w = parseFloat(r.attributes.width.value) * frames.attributes.width.value / 100 - 3;
        var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
        var newX = format_percent((parseFloat(r.attributes.x.value) + (100 * 3 / frames.attributes.width.value)));

        // Smaller than this size won't fit anything
        if (w < 2 * known_font_width) {
            textElemNewAttributes.push([newX, ""]);
            continue;
        }

        // Fit in full text width
        if (txt.length * known_font_width < w) {
            textElemNewAttributes.push([newX, txt]);
            continue;
        }

        var substringLength = Math.floor(w / known_font_width) - 2;
        if (truncate_text_right) {
            // Truncate the right side of the text.
            textElemNewAttributes.push([newX, txt.substring(0, substringLength) + ".."]);
            continue;
        } else {
            // Truncate the left side of the text.
            textElemNewAttributes.push([newX, ".." + txt.substring(txt.length - substringLength, txt.length)]);
            continue;
        }
    }

    console.assert(textElemNewAttributes.length === elements.length, "Resize failed, please file a bug at https://github.com/jonhoo/inferno/");

    // Now that we know new textContent, set it all in one go so we don't refresh a bazillion times.
    for (var i = 0; i < elements.length; i++) {
        var e = elements[i];
        var values = textElemNewAttributes[i];
        var t = find_child(e, "text");
        t.attributes.x.value = values[0];
        t.textContent = values[1];
    }
}

function update_text(e) {
    var r = find_child(e, "rect");
    var t = find_child(e, "text");
    var w = parseFloat(r.attributes.width.value) * frames.attributes.width.value / 100 - 3;
    var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
    t.attributes.x.value = format_percent((parseFloat(r.attributes.x.value) + (100 * 3 / frames.attributes.width.value)));

    // Smaller than this size won't fit anything
    if (w < 2 * fontsize * fontwidth) {
        t.textContent = "";
        return;
    }
    t.textContent = txt;
    // Fit in full text width
    if (t.getComputedTextLength() < w)
        return;
    if (truncate_text_right) {
        // Truncate the right side of the text.
        for (var x = txt.length - 2; x > 0; x--) {
            if (t.getSubStringLength(0, x + 2) <= w) {
                t.textContent = txt.substring(0, x) + "..";
                return;
            }
        }
    } else {
        // Truncate the left side of the text.
        for (var x = 2; x < txt.length; x++) {
            if (t.getSubStringLength(x - 2, txt.length) <= w) {
                t.textContent = ".." + txt.substring(x, txt.length);
                return;
            }
        }
    }
    t.textContent = "";
}
// zoom
function zoom_reset(e) {
    if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * parseInt(e.attributes["fg:x"].value) / total_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / total_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_reset(c[i]);
    }
}
function zoom_child(e, x, zoomed_width_samples) {
    if (e.tagName == "text") {
        var parent_x = parseFloat(find_child(e.parentNode, "rect[x]").attributes.x.value);
        e.attributes.x.value = format_percent(parent_x + (100 * 3 / frames.attributes.width.value));
    } else if (e.tagName == "rect") {
        e.attributes.x.value = format_percent(100 * (parseInt(e.attributes["fg:x"].value) - x) / zoomed_width_samples);
        e.attributes.width.value = format_percent(100 * parseInt(e.attributes["fg:w"].value) / zoomed_width_samples);
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_child(c[i], x, zoomed_width_samples);
    }
}
function zoom_parent(e) {
    if (e.attributes) {
        if (e.attributes.x != undefined) {
            e.attributes.x.value = "0.0%";
        }
        if (e.attributes.width != undefined) {
            e.attributes.width.value = "100.0%";
        }
    }
    if (e.childNodes == undefined) return;
    for(var i = 0, c = e.childNodes; i < c.length; i++) {
        zoom_parent(c[i]);
    }
}
function zoom(node) {
    var attr = find_child(node, "rect").attributes;
    var width = parseInt(attr["fg:w"].value);
    var xmin = parseInt(attr["fg:x"].value);
    var xmax = xmin + width;
    var ymin = parseFloat(attr.y.value);
    unzoombtn.classList.remove("hide");
    var el = frames.children;
    var to_update_text = [];
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        var a = find_child(e, "rect").attributes;
        var ex = parseInt(a["fg:x"].value);
        var ew = parseInt(a["fg:w"].value);
        // Is it an ancestor
        if (!inverted) {
            var upstack = parseFloat(a.y.value) > ymin;
        } else {
            var upstack = parseFloat(a.y.value) < ymin;
        }
        if (upstack) {
            // Direct ancestor
            if (ex <= xmin && (ex+ew) >= xmax) {
                e.classList.add("parent");
                zoom_parent(e);
                to_update_text.push(e);
            }
            // not in current path
            else
                e.classList.add("hide");
        }
        // Children maybe
        else {
            // no common path
            if (ex < xmin || ex >= xmax) {
                e.classList.add("hide");
            }
            else {
                zoom_child(e, xmin, width);
                to_update_text.push(e);
            }
        }
    }
    update_text_for_elements(to_update_text);
}
function unzoom() {
    unzoombtn.classList.add("hide");
    var el = frames.children;
    for(var i = 0; i < el.length; i++) {
        el[i].classList.remove("parent");
        el[i].classList.remove("hide");
        zoom_reset(el[i]);
    }
    update_text_for_elements(el);
}
// search
function reset_search() {
    var el = document.querySelectorAll("#frames rect");
    for (var i = 0; i < el.length; i++) {
        orig_load(el[i], "fill")
    }
    var params = get_params();
    delete params.s;
    history.replaceState(null, null, parse_params(params));
}
function search_prompt() {
    if (!searching) {
        var term = prompt("Enter a search term (regexp " +
            "allowed, eg: ^ext4_)", "");
        if (term != null) {
            search(term)
        }
    } else {
        reset_search();
        searching = 0;
        searchbtn.classList.remove("show");
        searchbtn.firstChild.nodeValue = "Search"
        matchedtxt.classList.add("hide");
        matchedtxt.firstChild.nodeValue = ""
    }
}
function search(term) {
    var re = new RegExp(term);
    var el = frames.children;
    var matches = new Object();
    var maxwidth = 0;
    for (var i = 0; i < el.length; i++) {
        var e = el[i];
        // Skip over frames which are either not visible, or below the zoomed-to frame
        if (e.classList.contains("hide") || e.classList.contains("parent")) {
            continue;
        }
        var func = g_to_func(e);
        var rect = find_child(e, "rect");
        if (func == null || rect == null)
            continue;
        // Save max width. Only works as we have a root frame
        var w = parseInt(rect.attributes["fg:w"].value);
        if (w > maxwidth)
            maxwidth = w;
        if (func.match(re)) {
            // highlight
            var x = parseInt(rect.attributes["fg:x"].value);
            orig_save(rect, "fill");
            rect.attributes.fill.value = searchcolor;
            // remember matches
            if (matches[x] == undefined) {
                matches[x] = w;
            } else {
                if (w > matches[x]) {
                    // overwrite with parent
                    matches[x] = w;
                }
            }
            searching = 1;
        }
    }
    if (!searching)
        return;
    var params = get_params();
    params.s = term;
    history.replaceState(null, null, parse_params(params));

    searchbtn.classList.add("show");
    searchbtn.firstChild.nodeValue = "Reset Search";
    // calculate percent matched, excluding vertical overlap
    var count = 0;
    var lastx = -1;
    var lastw = 0;
    var keys = Array();
    for (k in matches) {
        if (matches.hasOwnProperty(k))
            keys.push(k);
    }
    // sort the matched frames by their x location
    // ascending, then width descending
    keys.sort(function(a, b){
        return a - b;
    });
    // Step through frames saving only the biggest bottom-up frames
    // thanks to the sort order. This relies on the tree property
    // where children are always smaller than their parents.
    for (var k in keys) {
        var x = parseInt(keys[k]);
        var w = matches[keys[k]];
        if (x >= lastx + lastw) {
            count += w;
            lastx = x;
            lastw = w;
        }
    }
    // display matched percent
    matchedtxt.classList.remove("hide");
    var pct = 100 * count / maxwidth;
    if (pct != 100) pct = pct.toFixed(1);
    matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
}
function format_percent(n) {
    return n.toFixed(4) + "%";
}
]]></script><rect x="0" y="0" width="100%" height="746" fill="url(#background)"/><text id="title" fill="rgb(0,0,0)" x="50.0000%" y="24.00">C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\py-spy.exe record -o profile.svg --pid 42944 --duration 60</text><text id="details" fill="rgb(0,0,0)" x="10" y="40.00"> </text><text id="unzoom" class="hide" fill="rgb(0,0,0)" x="10" y="24.00">Reset Zoom</text><text id="search" fill="rgb(0,0,0)" x="1190" y="24.00">Search</text><text id="matched" fill="rgb(0,0,0)" x="1190" y="735.00"> </text><svg id="frames" x="10" width="1180" total_samples="441"><g><title>_poll (asyncio\windows_events.py:775) (104 samples, 23.58%)</title><rect x="0.2268%" y="196" width="23.5828%" height="15" fill="rgb(227,0,7)" fg:x="1" fg:w="104"/><text x="0.4768%" y="206.50">_poll (asyncio\windows_events.py:775)</text></g><g><title>call_soon (asyncio\base_events.py:836) (1 samples, 0.23%)</title><rect x="23.8095%" y="228" width="0.2268%" height="15" fill="rgb(217,0,24)" fg:x="105" fg:w="1"/><text x="24.0595%" y="238.50"></text></g><g><title>_check_callback (asyncio\base_events.py:844) (1 samples, 0.23%)</title><rect x="23.8095%" y="244" width="0.2268%" height="15" fill="rgb(221,193,54)" fg:x="105" fg:w="1"/><text x="24.0595%" y="254.50"></text></g><g><title>iscoroutinefunction (asyncio\coroutines.py:22) (1 samples, 0.23%)</title><rect x="23.8095%" y="260" width="0.2268%" height="15" fill="rgb(248,212,6)" fg:x="105" fg:w="1"/><text x="24.0595%" y="270.50"></text></g><g><title>iscoroutinefunction (inspect.py:443) (1 samples, 0.23%)</title><rect x="23.8095%" y="276" width="0.2268%" height="15" fill="rgb(208,68,35)" fg:x="105" fg:w="1"/><text x="24.0595%" y="286.50"></text></g><g><title>_has_code_flag (inspect.py:410) (1 samples, 0.23%)</title><rect x="23.8095%" y="292" width="0.2268%" height="15" fill="rgb(232,128,0)" fg:x="105" fg:w="1"/><text x="24.0595%" y="302.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:481) (1 samples, 0.23%)</title><rect x="24.2630%" y="308" width="0.2268%" height="15" fill="rgb(207,160,47)" fg:x="107" fg:w="1"/><text x="24.5130%" y="318.50"></text></g><g><title>lazycache (linecache.py:184) (1 samples, 0.23%)</title><rect x="24.2630%" y="324" width="0.2268%" height="15" fill="rgb(228,23,34)" fg:x="107" fg:w="1"/><text x="24.5130%" y="334.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:488) (2 samples, 0.45%)</title><rect x="24.4898%" y="308" width="0.4535%" height="15" fill="rgb(218,30,26)" fg:x="108" fg:w="2"/><text x="24.7398%" y="318.50"></text></g><g><title>__init__ (traceback.py:313) (1 samples, 0.23%)</title><rect x="24.7166%" y="324" width="0.2268%" height="15" fill="rgb(220,122,19)" fg:x="109" fg:w="1"/><text x="24.9666%" y="334.50"></text></g><g><title>_run_once (asyncio\base_events.py:2004) (119 samples, 26.98%)</title><rect x="0.0000%" y="164" width="26.9841%" height="15" fill="rgb(250,228,42)" fg:x="0" fg:w="119"/><text x="0.2500%" y="174.50">_run_once (asyncio\base_events.py:2004)</text></g><g><title>select (asyncio\windows_events.py:446) (119 samples, 26.98%)</title><rect x="0.0000%" y="180" width="26.9841%" height="15" fill="rgb(240,193,28)" fg:x="0" fg:w="119"/><text x="0.2500%" y="190.50">select (asyncio\windows_events.py:446)</text></g><g><title>_poll (asyncio\windows_events.py:809) (14 samples, 3.17%)</title><rect x="23.8095%" y="196" width="3.1746%" height="15" fill="rgb(216,20,37)" fg:x="105" fg:w="14"/><text x="24.0595%" y="206.50">_po..</text></g><g><title>set_result (asyncio\windows_events.py:93) (14 samples, 3.17%)</title><rect x="23.8095%" y="212" width="3.1746%" height="15" fill="rgb(206,188,39)" fg:x="105" fg:w="14"/><text x="24.0595%" y="222.50">set..</text></g><g><title>call_soon (asyncio\base_events.py:837) (13 samples, 2.95%)</title><rect x="24.0363%" y="228" width="2.9478%" height="15" fill="rgb(217,207,13)" fg:x="106" fg:w="13"/><text x="24.2863%" y="238.50">ca..</text></g><g><title>_call_soon (asyncio\base_events.py:853) (13 samples, 2.95%)</title><rect x="24.0363%" y="244" width="2.9478%" height="15" fill="rgb(231,73,38)" fg:x="106" fg:w="13"/><text x="24.2863%" y="254.50">_c..</text></g><g><title>__init__ (asyncio\events.py:47) (13 samples, 2.95%)</title><rect x="24.0363%" y="260" width="2.9478%" height="15" fill="rgb(225,20,46)" fg:x="106" fg:w="13"/><text x="24.2863%" y="270.50">__..</text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (13 samples, 2.95%)</title><rect x="24.0363%" y="276" width="2.9478%" height="15" fill="rgb(210,31,41)" fg:x="106" fg:w="13"/><text x="24.2863%" y="286.50">ex..</text></g><g><title>extract (traceback.py:449) (12 samples, 2.72%)</title><rect x="24.2630%" y="292" width="2.7211%" height="15" fill="rgb(221,200,47)" fg:x="107" fg:w="12"/><text x="24.5130%" y="302.50">ex..</text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (9 samples, 2.04%)</title><rect x="24.9433%" y="308" width="2.0408%" height="15" fill="rgb(226,26,5)" fg:x="110" fg:w="9"/><text x="25.1933%" y="318.50">_..</text></g><g><title>checkcache (linecache.py:94) (9 samples, 2.04%)</title><rect x="24.9433%" y="324" width="2.0408%" height="15" fill="rgb(249,33,26)" fg:x="110" fg:w="9"/><text x="25.1933%" y="334.50">c..</text></g><g><title>_run_once (asyncio\base_events.py:2015) (1 samples, 0.23%)</title><rect x="26.9841%" y="164" width="0.2268%" height="15" fill="rgb(235,183,28)" fg:x="119" fg:w="1"/><text x="27.2341%" y="174.50"></text></g><g><title>__lt__ (asyncio\events.py:131) (1 samples, 0.23%)</title><rect x="26.9841%" y="180" width="0.2268%" height="15" fill="rgb(221,5,38)" fg:x="119" fg:w="1"/><text x="27.2341%" y="190.50"></text></g><g><title>_run_once (asyncio\base_events.py:2027) (3 samples, 0.68%)</title><rect x="27.2109%" y="164" width="0.6803%" height="15" fill="rgb(247,18,42)" fg:x="120" fg:w="3"/><text x="27.4609%" y="174.50"></text></g><g><title>_run_once (asyncio\base_events.py:2033) (1 samples, 0.23%)</title><rect x="27.8912%" y="164" width="0.2268%" height="15" fill="rgb(241,131,45)" fg:x="123" fg:w="1"/><text x="28.1412%" y="174.50"></text></g><g><title>time (asyncio\base_events.py:779) (1 samples, 0.23%)</title><rect x="27.8912%" y="180" width="0.2268%" height="15" fill="rgb(249,31,29)" fg:x="123" fg:w="1"/><text x="28.1412%" y="190.50"></text></g><g><title>__timeout_task_impl (discord\ui\view.py:215) (1 samples, 0.23%)</title><rect x="28.1179%" y="196" width="0.2268%" height="15" fill="rgb(225,111,53)" fg:x="124" fg:w="1"/><text x="28.3679%" y="206.50"></text></g><g><title>sleep (asyncio\tasks.py:713) (1 samples, 0.23%)</title><rect x="28.1179%" y="212" width="0.2268%" height="15" fill="rgb(238,160,17)" fg:x="124" fg:w="1"/><text x="28.3679%" y="222.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="28.1179%" y="228" width="0.2268%" height="15" fill="rgb(214,148,48)" fg:x="124" fg:w="1"/><text x="28.3679%" y="238.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="28.1179%" y="244" width="0.2268%" height="15" fill="rgb(232,36,49)" fg:x="124" fg:w="1"/><text x="28.3679%" y="254.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="28.1179%" y="260" width="0.2268%" height="15" fill="rgb(209,103,24)" fg:x="124" fg:w="1"/><text x="28.3679%" y="270.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="28.1179%" y="276" width="0.2268%" height="15" fill="rgb(229,88,8)" fg:x="124" fg:w="1"/><text x="28.3679%" y="286.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="28.1179%" y="292" width="0.2268%" height="15" fill="rgb(213,181,19)" fg:x="124" fg:w="1"/><text x="28.3679%" y="302.50"></text></g><g><title>_flush_batch_records (auxiliary\services\db_command_usage_service.py:147) (3 samples, 0.68%)</title><rect x="28.3447%" y="212" width="0.6803%" height="15" fill="rgb(254,191,54)" fg:x="125" fg:w="3"/><text x="28.5947%" y="222.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (3 samples, 0.68%)</title><rect x="28.3447%" y="228" width="0.6803%" height="15" fill="rgb(241,83,37)" fg:x="125" fg:w="3"/><text x="28.5947%" y="238.50"></text></g><g><title>release (asyncpg\pool.py:905) (3 samples, 0.68%)</title><rect x="28.3447%" y="244" width="0.6803%" height="15" fill="rgb(233,36,39)" fg:x="125" fg:w="3"/><text x="28.5947%" y="254.50"></text></g><g><title>shield (asyncio\tasks.py:951) (3 samples, 0.68%)</title><rect x="28.3447%" y="260" width="0.6803%" height="15" fill="rgb(226,3,54)" fg:x="125" fg:w="3"/><text x="28.5947%" y="270.50"></text></g><g><title>ensure_future (asyncio\tasks.py:748) (3 samples, 0.68%)</title><rect x="28.3447%" y="276" width="0.6803%" height="15" fill="rgb(245,192,40)" fg:x="125" fg:w="3"/><text x="28.5947%" y="286.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (3 samples, 0.68%)</title><rect x="28.3447%" y="292" width="0.6803%" height="15" fill="rgb(238,167,29)" fg:x="125" fg:w="3"/><text x="28.5947%" y="302.50"></text></g><g><title>extract_stack (traceback.py:260) (3 samples, 0.68%)</title><rect x="28.3447%" y="308" width="0.6803%" height="15" fill="rgb(232,182,51)" fg:x="125" fg:w="3"/><text x="28.5947%" y="318.50"></text></g><g><title>extract (traceback.py:449) (3 samples, 0.68%)</title><rect x="28.3447%" y="324" width="0.6803%" height="15" fill="rgb(231,60,39)" fg:x="125" fg:w="3"/><text x="28.5947%" y="334.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (3 samples, 0.68%)</title><rect x="28.3447%" y="340" width="0.6803%" height="15" fill="rgb(208,69,12)" fg:x="125" fg:w="3"/><text x="28.5947%" y="350.50"></text></g><g><title>checkcache (linecache.py:94) (3 samples, 0.68%)</title><rect x="28.3447%" y="356" width="0.6803%" height="15" fill="rgb(235,93,37)" fg:x="125" fg:w="3"/><text x="28.5947%" y="366.50"></text></g><g><title>emit (logging\__init__.py:1151) (1 samples, 0.23%)</title><rect x="29.0249%" y="308" width="0.2268%" height="15" fill="rgb(213,116,39)" fg:x="128" fg:w="1"/><text x="29.2749%" y="318.50"></text></g><g><title>format (logging\__init__.py:999) (1 samples, 0.23%)</title><rect x="29.0249%" y="324" width="0.2268%" height="15" fill="rgb(222,207,29)" fg:x="128" fg:w="1"/><text x="29.2749%" y="334.50"></text></g><g><title>format (colored_formatter.py:90) (1 samples, 0.23%)</title><rect x="29.0249%" y="340" width="0.2268%" height="15" fill="rgb(206,96,30)" fg:x="128" fg:w="1"/><text x="29.2749%" y="350.50"></text></g><g><title>format (logging\__init__.py:715) (1 samples, 0.23%)</title><rect x="29.0249%" y="356" width="0.2268%" height="15" fill="rgb(218,138,4)" fg:x="128" fg:w="1"/><text x="29.2749%" y="366.50"></text></g><g><title>formatMessage (logging\__init__.py:683) (1 samples, 0.23%)</title><rect x="29.0249%" y="372" width="0.2268%" height="15" fill="rgb(250,191,14)" fg:x="128" fg:w="1"/><text x="29.2749%" y="382.50"></text></g><g><title>emit (logging\__init__.py:1151) (1 samples, 0.23%)</title><rect x="29.2517%" y="324" width="0.2268%" height="15" fill="rgb(239,60,40)" fg:x="129" fg:w="1"/><text x="29.5017%" y="334.50"></text></g><g><title>format (logging\__init__.py:999) (1 samples, 0.23%)</title><rect x="29.2517%" y="340" width="0.2268%" height="15" fill="rgb(206,27,48)" fg:x="129" fg:w="1"/><text x="29.5017%" y="350.50"></text></g><g><title>format (logging\__init__.py:714) (1 samples, 0.23%)</title><rect x="29.2517%" y="356" width="0.2268%" height="15" fill="rgb(225,35,8)" fg:x="129" fg:w="1"/><text x="29.5017%" y="366.50"></text></g><g><title>formatTime (logging\__init__.py:653) (1 samples, 0.23%)</title><rect x="29.2517%" y="372" width="0.2268%" height="15" fill="rgb(250,213,24)" fg:x="129" fg:w="1"/><text x="29.5017%" y="382.50"></text></g><g><title>_background_flush_loop (auxiliary\services\db_command_usage_service.py:99) (6 samples, 1.36%)</title><rect x="28.3447%" y="196" width="1.3605%" height="15" fill="rgb(247,123,22)" fg:x="125" fg:w="6"/><text x="28.5947%" y="206.50"></text></g><g><title>_flush_batch_records (auxiliary\services\db_command_usage_service.py:174) (3 samples, 0.68%)</title><rect x="29.0249%" y="212" width="0.6803%" height="15" fill="rgb(231,138,38)" fg:x="128" fg:w="3"/><text x="29.2749%" y="222.50"></text></g><g><title>debug (logging\__init__.py:1510) (3 samples, 0.68%)</title><rect x="29.0249%" y="228" width="0.6803%" height="15" fill="rgb(231,145,46)" fg:x="128" fg:w="3"/><text x="29.2749%" y="238.50"></text></g><g><title>_log (logging\__init__.py:1667) (3 samples, 0.68%)</title><rect x="29.0249%" y="244" width="0.6803%" height="15" fill="rgb(251,118,11)" fg:x="128" fg:w="3"/><text x="29.2749%" y="254.50"></text></g><g><title>handle (logging\__init__.py:1686) (3 samples, 0.68%)</title><rect x="29.0249%" y="260" width="0.6803%" height="15" fill="rgb(217,147,25)" fg:x="128" fg:w="3"/><text x="29.2749%" y="270.50"></text></g><g><title>callHandlers (logging\__init__.py:1744) (3 samples, 0.68%)</title><rect x="29.0249%" y="276" width="0.6803%" height="15" fill="rgb(247,81,37)" fg:x="128" fg:w="3"/><text x="29.2749%" y="286.50"></text></g><g><title>handle (logging\__init__.py:1027) (3 samples, 0.68%)</title><rect x="29.0249%" y="292" width="0.6803%" height="15" fill="rgb(209,12,38)" fg:x="128" fg:w="3"/><text x="29.2749%" y="302.50"></text></g><g><title>emit (logging\__init__.py:1265) (2 samples, 0.45%)</title><rect x="29.2517%" y="308" width="0.4535%" height="15" fill="rgb(227,1,9)" fg:x="129" fg:w="2"/><text x="29.5017%" y="318.50"></text></g><g><title>emit (logging\__init__.py:1155) (1 samples, 0.23%)</title><rect x="29.4785%" y="324" width="0.2268%" height="15" fill="rgb(248,47,43)" fg:x="130" fg:w="1"/><text x="29.7285%" y="334.50"></text></g><g><title>flush (logging\__init__.py:1137) (1 samples, 0.23%)</title><rect x="29.4785%" y="340" width="0.2268%" height="15" fill="rgb(221,10,30)" fg:x="130" fg:w="1"/><text x="29.7285%" y="350.50"></text></g><g><title>_inner_done_callback (asyncio\tasks.py:972) (5 samples, 1.13%)</title><rect x="29.7052%" y="196" width="1.1338%" height="15" fill="rgb(210,229,1)" fg:x="131" fg:w="5"/><text x="29.9552%" y="206.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (5 samples, 1.13%)</title><rect x="29.7052%" y="212" width="1.1338%" height="15" fill="rgb(222,148,37)" fg:x="131" fg:w="5"/><text x="29.9552%" y="222.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (5 samples, 1.13%)</title><rect x="29.7052%" y="228" width="1.1338%" height="15" fill="rgb(234,67,33)" fg:x="131" fg:w="5"/><text x="29.9552%" y="238.50"></text></g><g><title>__init__ (asyncio\events.py:47) (5 samples, 1.13%)</title><rect x="29.7052%" y="244" width="1.1338%" height="15" fill="rgb(247,98,35)" fg:x="131" fg:w="5"/><text x="29.9552%" y="254.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (5 samples, 1.13%)</title><rect x="29.7052%" y="260" width="1.1338%" height="15" fill="rgb(247,138,52)" fg:x="131" fg:w="5"/><text x="29.9552%" y="270.50"></text></g><g><title>extract (traceback.py:449) (5 samples, 1.13%)</title><rect x="29.7052%" y="276" width="1.1338%" height="15" fill="rgb(213,79,30)" fg:x="131" fg:w="5"/><text x="29.9552%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (5 samples, 1.13%)</title><rect x="29.7052%" y="292" width="1.1338%" height="15" fill="rgb(246,177,23)" fg:x="131" fg:w="5"/><text x="29.9552%" y="302.50"></text></g><g><title>checkcache (linecache.py:94) (5 samples, 1.13%)</title><rect x="29.7052%" y="308" width="1.1338%" height="15" fill="rgb(230,62,27)" fg:x="131" fg:w="5"/><text x="29.9552%" y="318.50"></text></g><g><title>execute_command (redis\asyncio\client.py:672) (1 samples, 0.23%)</title><rect x="30.8390%" y="260" width="0.2268%" height="15" fill="rgb(216,154,8)" fg:x="136" fg:w="1"/><text x="31.0890%" y="270.50"></text></g><g><title>get_connection (redis\asyncio\connection.py:1141) (1 samples, 0.23%)</title><rect x="30.8390%" y="276" width="0.2268%" height="15" fill="rgb(244,35,45)" fg:x="136" fg:w="1"/><text x="31.0890%" y="286.50"></text></g><g><title>ensure_connection (redis\asyncio\connection.py:1180) (1 samples, 0.23%)</title><rect x="30.8390%" y="292" width="0.2268%" height="15" fill="rgb(251,115,12)" fg:x="136" fg:w="1"/><text x="31.0890%" y="302.50"></text></g><g><title>can_read_destructive (redis\asyncio\connection.py:561) (1 samples, 0.23%)</title><rect x="30.8390%" y="308" width="0.2268%" height="15" fill="rgb(240,54,50)" fg:x="136" fg:w="1"/><text x="31.0890%" y="318.50"></text></g><g><title>can_read_destructive (redis\_parsers\hiredis.py:240) (1 samples, 0.23%)</title><rect x="30.8390%" y="324" width="0.2268%" height="15" fill="rgb(233,84,52)" fg:x="136" fg:w="1"/><text x="31.0890%" y="334.50"></text></g><g><title>read_from_socket (redis\_parsers\hiredis.py:245) (1 samples, 0.23%)</title><rect x="30.8390%" y="340" width="0.2268%" height="15" fill="rgb(207,117,47)" fg:x="136" fg:w="1"/><text x="31.0890%" y="350.50"></text></g><g><title>read (asyncio\streams.py:730) (1 samples, 0.23%)</title><rect x="30.8390%" y="356" width="0.2268%" height="15" fill="rgb(249,43,39)" fg:x="136" fg:w="1"/><text x="31.0890%" y="366.50"></text></g><g><title>_wait_for_data (asyncio\streams.py:537) (1 samples, 0.23%)</title><rect x="30.8390%" y="372" width="0.2268%" height="15" fill="rgb(209,38,44)" fg:x="136" fg:w="1"/><text x="31.0890%" y="382.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="30.8390%" y="388" width="0.2268%" height="15" fill="rgb(236,212,23)" fg:x="136" fg:w="1"/><text x="31.0890%" y="398.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="30.8390%" y="404" width="0.2268%" height="15" fill="rgb(242,79,21)" fg:x="136" fg:w="1"/><text x="31.0890%" y="414.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="30.8390%" y="420" width="0.2268%" height="15" fill="rgb(211,96,35)" fg:x="136" fg:w="1"/><text x="31.0890%" y="430.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="30.8390%" y="436" width="0.2268%" height="15" fill="rgb(253,215,40)" fg:x="136" fg:w="1"/><text x="31.0890%" y="446.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="30.8390%" y="452" width="0.2268%" height="15" fill="rgb(211,81,21)" fg:x="136" fg:w="1"/><text x="31.0890%" y="462.50"></text></g><g><title>check_and_update_all_stocks_lifecycle (gacha\services\stock_lifecycle_service.py:712) (2 samples, 0.45%)</title><rect x="30.8390%" y="228" width="0.4535%" height="15" fill="rgb(208,190,38)" fg:x="136" fg:w="2"/><text x="31.0890%" y="238.50"></text></g><g><title>_reset_consecutive_checks_data (gacha\services\stock_lifecycle_service.py:91) (2 samples, 0.45%)</title><rect x="30.8390%" y="244" width="0.4535%" height="15" fill="rgb(235,213,38)" fg:x="136" fg:w="2"/><text x="31.0890%" y="254.50"></text></g><g><title>execute_command (redis\asyncio\client.py:677) (1 samples, 0.23%)</title><rect x="31.0658%" y="260" width="0.2268%" height="15" fill="rgb(237,122,38)" fg:x="137" fg:w="1"/><text x="31.3158%" y="270.50"></text></g><g><title>call_with_retry (redis\asyncio\retry.py:71) (1 samples, 0.23%)</title><rect x="31.0658%" y="276" width="0.2268%" height="15" fill="rgb(244,218,35)" fg:x="137" fg:w="1"/><text x="31.3158%" y="286.50"></text></g><g><title>_send_command_parse_response (redis\asyncio\client.py:651) (1 samples, 0.23%)</title><rect x="31.0658%" y="292" width="0.2268%" height="15" fill="rgb(240,68,47)" fg:x="137" fg:w="1"/><text x="31.3158%" y="302.50"></text></g><g><title>send_command (redis\asyncio\connection.py:554) (1 samples, 0.23%)</title><rect x="31.0658%" y="308" width="0.2268%" height="15" fill="rgb(210,16,53)" fg:x="137" fg:w="1"/><text x="31.3158%" y="318.50"></text></g><g><title>send_packed_command (redis\asyncio\connection.py:529) (1 samples, 0.23%)</title><rect x="31.0658%" y="324" width="0.2268%" height="15" fill="rgb(235,124,12)" fg:x="137" fg:w="1"/><text x="31.3158%" y="334.50"></text></g><g><title>writelines (asyncio\streams.py:343) (1 samples, 0.23%)</title><rect x="31.0658%" y="340" width="0.2268%" height="15" fill="rgb(224,169,11)" fg:x="137" fg:w="1"/><text x="31.3158%" y="350.50"></text></g><g><title>writelines (asyncio\transports.py:123) (1 samples, 0.23%)</title><rect x="31.0658%" y="356" width="0.2268%" height="15" fill="rgb(250,166,2)" fg:x="137" fg:w="1"/><text x="31.3158%" y="366.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="31.0658%" y="372" width="0.2268%" height="15" fill="rgb(242,216,29)" fg:x="137" fg:w="1"/><text x="31.3158%" y="382.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="31.0658%" y="388" width="0.2268%" height="15" fill="rgb(230,116,27)" fg:x="137" fg:w="1"/><text x="31.3158%" y="398.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="31.0658%" y="404" width="0.2268%" height="15" fill="rgb(228,99,48)" fg:x="137" fg:w="1"/><text x="31.3158%" y="414.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="31.0658%" y="420" width="0.2268%" height="15" fill="rgb(253,11,6)" fg:x="137" fg:w="1"/><text x="31.3158%" y="430.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="31.0658%" y="436" width="0.2268%" height="15" fill="rgb(247,143,39)" fg:x="137" fg:w="1"/><text x="31.3158%" y="446.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="31.0658%" y="452" width="0.2268%" height="15" fill="rgb(236,97,10)" fg:x="137" fg:w="1"/><text x="31.3158%" y="462.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="31.0658%" y="468" width="0.2268%" height="15" fill="rgb(233,208,19)" fg:x="137" fg:w="1"/><text x="31.3158%" y="478.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="31.0658%" y="484" width="0.2268%" height="15" fill="rgb(216,164,2)" fg:x="137" fg:w="1"/><text x="31.3158%" y="494.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="31.0658%" y="500" width="0.2268%" height="15" fill="rgb(220,129,5)" fg:x="137" fg:w="1"/><text x="31.3158%" y="510.50"></text></g><g><title>execute_command (redis\asyncio\client.py:672) (4 samples, 0.91%)</title><rect x="31.2925%" y="260" width="0.9070%" height="15" fill="rgb(242,17,10)" fg:x="138" fg:w="4"/><text x="31.5425%" y="270.50"></text></g><g><title>get_connection (redis\asyncio\connection.py:1141) (4 samples, 0.91%)</title><rect x="31.2925%" y="276" width="0.9070%" height="15" fill="rgb(242,107,0)" fg:x="138" fg:w="4"/><text x="31.5425%" y="286.50"></text></g><g><title>ensure_connection (redis\asyncio\connection.py:1180) (4 samples, 0.91%)</title><rect x="31.2925%" y="292" width="0.9070%" height="15" fill="rgb(251,28,31)" fg:x="138" fg:w="4"/><text x="31.5425%" y="302.50"></text></g><g><title>can_read_destructive (redis\asyncio\connection.py:561) (4 samples, 0.91%)</title><rect x="31.2925%" y="308" width="0.9070%" height="15" fill="rgb(233,223,10)" fg:x="138" fg:w="4"/><text x="31.5425%" y="318.50"></text></g><g><title>can_read_destructive (redis\_parsers\hiredis.py:240) (4 samples, 0.91%)</title><rect x="31.2925%" y="324" width="0.9070%" height="15" fill="rgb(215,21,27)" fg:x="138" fg:w="4"/><text x="31.5425%" y="334.50"></text></g><g><title>read_from_socket (redis\_parsers\hiredis.py:245) (4 samples, 0.91%)</title><rect x="31.2925%" y="340" width="0.9070%" height="15" fill="rgb(232,23,21)" fg:x="138" fg:w="4"/><text x="31.5425%" y="350.50"></text></g><g><title>read (asyncio\streams.py:730) (4 samples, 0.91%)</title><rect x="31.2925%" y="356" width="0.9070%" height="15" fill="rgb(244,5,23)" fg:x="138" fg:w="4"/><text x="31.5425%" y="366.50"></text></g><g><title>_wait_for_data (asyncio\streams.py:537) (4 samples, 0.91%)</title><rect x="31.2925%" y="372" width="0.9070%" height="15" fill="rgb(226,81,46)" fg:x="138" fg:w="4"/><text x="31.5425%" y="382.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (4 samples, 0.91%)</title><rect x="31.2925%" y="388" width="0.9070%" height="15" fill="rgb(247,70,30)" fg:x="138" fg:w="4"/><text x="31.5425%" y="398.50"></text></g><g><title>extract_stack (traceback.py:260) (4 samples, 0.91%)</title><rect x="31.2925%" y="404" width="0.9070%" height="15" fill="rgb(212,68,19)" fg:x="138" fg:w="4"/><text x="31.5425%" y="414.50"></text></g><g><title>extract (traceback.py:449) (4 samples, 0.91%)</title><rect x="31.2925%" y="420" width="0.9070%" height="15" fill="rgb(240,187,13)" fg:x="138" fg:w="4"/><text x="31.5425%" y="430.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (4 samples, 0.91%)</title><rect x="31.2925%" y="436" width="0.9070%" height="15" fill="rgb(223,113,26)" fg:x="138" fg:w="4"/><text x="31.5425%" y="446.50"></text></g><g><title>checkcache (linecache.py:94) (4 samples, 0.91%)</title><rect x="31.2925%" y="452" width="0.9070%" height="15" fill="rgb(206,192,2)" fg:x="138" fg:w="4"/><text x="31.5425%" y="462.50"></text></g><g><title>check_and_update_all_stocks_lifecycle (gacha\services\stock_lifecycle_service.py:721) (6 samples, 1.36%)</title><rect x="31.2925%" y="228" width="1.3605%" height="15" fill="rgb(241,108,4)" fg:x="138" fg:w="6"/><text x="31.5425%" y="238.50"></text></g><g><title>_reset_consecutive_checks_data (gacha\services\stock_lifecycle_service.py:91) (6 samples, 1.36%)</title><rect x="31.2925%" y="244" width="1.3605%" height="15" fill="rgb(247,173,49)" fg:x="138" fg:w="6"/><text x="31.5425%" y="254.50"></text></g><g><title>execute_command (redis\asyncio\client.py:677) (2 samples, 0.45%)</title><rect x="32.1995%" y="260" width="0.4535%" height="15" fill="rgb(224,114,35)" fg:x="142" fg:w="2"/><text x="32.4495%" y="270.50"></text></g><g><title>call_with_retry (redis\asyncio\retry.py:71) (2 samples, 0.45%)</title><rect x="32.1995%" y="276" width="0.4535%" height="15" fill="rgb(245,159,27)" fg:x="142" fg:w="2"/><text x="32.4495%" y="286.50"></text></g><g><title>_send_command_parse_response (redis\asyncio\client.py:651) (2 samples, 0.45%)</title><rect x="32.1995%" y="292" width="0.4535%" height="15" fill="rgb(245,172,44)" fg:x="142" fg:w="2"/><text x="32.4495%" y="302.50"></text></g><g><title>send_command (redis\asyncio\connection.py:554) (2 samples, 0.45%)</title><rect x="32.1995%" y="308" width="0.4535%" height="15" fill="rgb(236,23,11)" fg:x="142" fg:w="2"/><text x="32.4495%" y="318.50"></text></g><g><title>send_packed_command (redis\asyncio\connection.py:529) (2 samples, 0.45%)</title><rect x="32.1995%" y="324" width="0.4535%" height="15" fill="rgb(205,117,38)" fg:x="142" fg:w="2"/><text x="32.4495%" y="334.50"></text></g><g><title>writelines (asyncio\streams.py:343) (2 samples, 0.45%)</title><rect x="32.1995%" y="340" width="0.4535%" height="15" fill="rgb(237,72,25)" fg:x="142" fg:w="2"/><text x="32.4495%" y="350.50"></text></g><g><title>writelines (asyncio\transports.py:123) (2 samples, 0.45%)</title><rect x="32.1995%" y="356" width="0.4535%" height="15" fill="rgb(244,70,9)" fg:x="142" fg:w="2"/><text x="32.4495%" y="366.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (2 samples, 0.45%)</title><rect x="32.1995%" y="372" width="0.4535%" height="15" fill="rgb(217,125,39)" fg:x="142" fg:w="2"/><text x="32.4495%" y="382.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (2 samples, 0.45%)</title><rect x="32.1995%" y="388" width="0.4535%" height="15" fill="rgb(235,36,10)" fg:x="142" fg:w="2"/><text x="32.4495%" y="398.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (2 samples, 0.45%)</title><rect x="32.1995%" y="404" width="0.4535%" height="15" fill="rgb(251,123,47)" fg:x="142" fg:w="2"/><text x="32.4495%" y="414.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (2 samples, 0.45%)</title><rect x="32.1995%" y="420" width="0.4535%" height="15" fill="rgb(221,13,13)" fg:x="142" fg:w="2"/><text x="32.4495%" y="430.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="32.1995%" y="436" width="0.4535%" height="15" fill="rgb(238,131,9)" fg:x="142" fg:w="2"/><text x="32.4495%" y="446.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="32.1995%" y="452" width="0.4535%" height="15" fill="rgb(211,50,8)" fg:x="142" fg:w="2"/><text x="32.4495%" y="462.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="32.1995%" y="468" width="0.4535%" height="15" fill="rgb(245,182,24)" fg:x="142" fg:w="2"/><text x="32.4495%" y="478.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="32.1995%" y="484" width="0.4535%" height="15" fill="rgb(242,14,37)" fg:x="142" fg:w="2"/><text x="32.4495%" y="494.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="32.1995%" y="500" width="0.4535%" height="15" fill="rgb(246,228,12)" fg:x="142" fg:w="2"/><text x="32.4495%" y="510.50"></text></g><g><title>execute_command (redis\asyncio\client.py:672) (1 samples, 0.23%)</title><rect x="32.6531%" y="260" width="0.2268%" height="15" fill="rgb(213,55,15)" fg:x="144" fg:w="1"/><text x="32.9031%" y="270.50"></text></g><g><title>get_connection (redis\asyncio\connection.py:1137) (1 samples, 0.23%)</title><rect x="32.6531%" y="276" width="0.2268%" height="15" fill="rgb(209,9,3)" fg:x="144" fg:w="1"/><text x="32.9031%" y="286.50"></text></g><g><title>__aenter__ (asyncio\locks.py:14) (1 samples, 0.23%)</title><rect x="32.6531%" y="292" width="0.2268%" height="15" fill="rgb(230,59,30)" fg:x="144" fg:w="1"/><text x="32.9031%" y="302.50"></text></g><g><title>acquire (asyncio\locks.py:90) (1 samples, 0.23%)</title><rect x="32.6531%" y="308" width="0.2268%" height="15" fill="rgb(209,121,21)" fg:x="144" fg:w="1"/><text x="32.9031%" y="318.50"></text></g><g><title>_send_command_parse_response (redis\asyncio\client.py:651) (1 samples, 0.23%)</title><rect x="32.8798%" y="292" width="0.2268%" height="15" fill="rgb(220,109,13)" fg:x="145" fg:w="1"/><text x="33.1298%" y="302.50"></text></g><g><title>send_command (redis\asyncio\connection.py:554) (1 samples, 0.23%)</title><rect x="32.8798%" y="308" width="0.2268%" height="15" fill="rgb(232,18,1)" fg:x="145" fg:w="1"/><text x="33.1298%" y="318.50"></text></g><g><title>send_packed_command (redis\asyncio\connection.py:529) (1 samples, 0.23%)</title><rect x="32.8798%" y="324" width="0.2268%" height="15" fill="rgb(215,41,42)" fg:x="145" fg:w="1"/><text x="33.1298%" y="334.50"></text></g><g><title>writelines (asyncio\streams.py:343) (1 samples, 0.23%)</title><rect x="32.8798%" y="340" width="0.2268%" height="15" fill="rgb(224,123,36)" fg:x="145" fg:w="1"/><text x="33.1298%" y="350.50"></text></g><g><title>writelines (asyncio\transports.py:123) (1 samples, 0.23%)</title><rect x="32.8798%" y="356" width="0.2268%" height="15" fill="rgb(240,125,3)" fg:x="145" fg:w="1"/><text x="33.1298%" y="366.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="32.8798%" y="372" width="0.2268%" height="15" fill="rgb(205,98,50)" fg:x="145" fg:w="1"/><text x="33.1298%" y="382.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="32.8798%" y="388" width="0.2268%" height="15" fill="rgb(205,185,37)" fg:x="145" fg:w="1"/><text x="33.1298%" y="398.50"></text></g><g><title>send (asyncio\windows_events.py:543) (1 samples, 0.23%)</title><rect x="32.8798%" y="404" width="0.2268%" height="15" fill="rgb(238,207,15)" fg:x="145" fg:w="1"/><text x="33.1298%" y="414.50"></text></g><g><title>execute_command (redis\asyncio\client.py:677) (2 samples, 0.45%)</title><rect x="32.8798%" y="260" width="0.4535%" height="15" fill="rgb(213,199,42)" fg:x="145" fg:w="2"/><text x="33.1298%" y="270.50"></text></g><g><title>call_with_retry (redis\asyncio\retry.py:71) (2 samples, 0.45%)</title><rect x="32.8798%" y="276" width="0.4535%" height="15" fill="rgb(235,201,11)" fg:x="145" fg:w="2"/><text x="33.1298%" y="286.50"></text></g><g><title>_send_command_parse_response (redis\asyncio\client.py:652) (1 samples, 0.23%)</title><rect x="33.1066%" y="292" width="0.2268%" height="15" fill="rgb(207,46,11)" fg:x="146" fg:w="1"/><text x="33.3566%" y="302.50"></text></g><g><title>parse_response (redis\asyncio\client.py:698) (1 samples, 0.23%)</title><rect x="33.1066%" y="308" width="0.2268%" height="15" fill="rgb(241,35,35)" fg:x="146" fg:w="1"/><text x="33.3566%" y="318.50"></text></g><g><title>read_response (redis\asyncio\connection.py:594) (1 samples, 0.23%)</title><rect x="33.1066%" y="324" width="0.2268%" height="15" fill="rgb(243,32,47)" fg:x="146" fg:w="1"/><text x="33.3566%" y="334.50"></text></g><g><title>read_response (redis\_parsers\hiredis.py:268) (1 samples, 0.23%)</title><rect x="33.1066%" y="340" width="0.2268%" height="15" fill="rgb(247,202,23)" fg:x="146" fg:w="1"/><text x="33.3566%" y="350.50"></text></g><g><title>read_from_socket (redis\_parsers\hiredis.py:245) (1 samples, 0.23%)</title><rect x="33.1066%" y="356" width="0.2268%" height="15" fill="rgb(219,102,11)" fg:x="146" fg:w="1"/><text x="33.3566%" y="366.50"></text></g><g><title>read (asyncio\streams.py:730) (1 samples, 0.23%)</title><rect x="33.1066%" y="372" width="0.2268%" height="15" fill="rgb(243,110,44)" fg:x="146" fg:w="1"/><text x="33.3566%" y="382.50"></text></g><g><title>_wait_for_data (asyncio\streams.py:537) (1 samples, 0.23%)</title><rect x="33.1066%" y="388" width="0.2268%" height="15" fill="rgb(222,74,54)" fg:x="146" fg:w="1"/><text x="33.3566%" y="398.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="33.1066%" y="404" width="0.2268%" height="15" fill="rgb(216,99,12)" fg:x="146" fg:w="1"/><text x="33.3566%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="33.1066%" y="420" width="0.2268%" height="15" fill="rgb(226,22,26)" fg:x="146" fg:w="1"/><text x="33.3566%" y="430.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="33.1066%" y="436" width="0.2268%" height="15" fill="rgb(217,163,10)" fg:x="146" fg:w="1"/><text x="33.3566%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="33.1066%" y="452" width="0.2268%" height="15" fill="rgb(213,25,53)" fg:x="146" fg:w="1"/><text x="33.3566%" y="462.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="33.1066%" y="468" width="0.2268%" height="15" fill="rgb(252,105,26)" fg:x="146" fg:w="1"/><text x="33.3566%" y="478.50"></text></g><g><title>check_and_update_all_stocks_lifecycle (gacha\services\stock_lifecycle_service.py:722) (4 samples, 0.91%)</title><rect x="32.6531%" y="228" width="0.9070%" height="15" fill="rgb(220,39,43)" fg:x="144" fg:w="4"/><text x="32.9031%" y="238.50"></text></g><g><title>_get_consecutive_checks_data (gacha\services\stock_lifecycle_service.py:43) (4 samples, 0.91%)</title><rect x="32.6531%" y="244" width="0.9070%" height="15" fill="rgb(229,68,48)" fg:x="144" fg:w="4"/><text x="32.9031%" y="254.50"></text></g><g><title>execute_command (redis\asyncio\client.py:681) (1 samples, 0.23%)</title><rect x="33.3333%" y="260" width="0.2268%" height="15" fill="rgb(252,8,32)" fg:x="147" fg:w="1"/><text x="33.5833%" y="270.50"></text></g><g><title>call_with_retry (redis\asyncio\retry.py:58) (1 samples, 0.23%)</title><rect x="33.3333%" y="276" width="0.2268%" height="15" fill="rgb(223,20,43)" fg:x="147" fg:w="1"/><text x="33.5833%" y="286.50"></text></g><g><title>execute_command (redis\asyncio\client.py:672) (1 samples, 0.23%)</title><rect x="33.5601%" y="260" width="0.2268%" height="15" fill="rgb(229,81,49)" fg:x="148" fg:w="1"/><text x="33.8101%" y="270.50"></text></g><g><title>get_connection (redis\asyncio\connection.py:1141) (1 samples, 0.23%)</title><rect x="33.5601%" y="276" width="0.2268%" height="15" fill="rgb(236,28,36)" fg:x="148" fg:w="1"/><text x="33.8101%" y="286.50"></text></g><g><title>ensure_connection (redis\asyncio\connection.py:1180) (1 samples, 0.23%)</title><rect x="33.5601%" y="292" width="0.2268%" height="15" fill="rgb(249,185,26)" fg:x="148" fg:w="1"/><text x="33.8101%" y="302.50"></text></g><g><title>can_read_destructive (redis\asyncio\connection.py:561) (1 samples, 0.23%)</title><rect x="33.5601%" y="308" width="0.2268%" height="15" fill="rgb(249,174,33)" fg:x="148" fg:w="1"/><text x="33.8101%" y="318.50"></text></g><g><title>can_read_destructive (redis\_parsers\hiredis.py:240) (1 samples, 0.23%)</title><rect x="33.5601%" y="324" width="0.2268%" height="15" fill="rgb(233,201,37)" fg:x="148" fg:w="1"/><text x="33.8101%" y="334.50"></text></g><g><title>read_from_socket (redis\_parsers\hiredis.py:245) (1 samples, 0.23%)</title><rect x="33.5601%" y="340" width="0.2268%" height="15" fill="rgb(221,78,26)" fg:x="148" fg:w="1"/><text x="33.8101%" y="350.50"></text></g><g><title>read (asyncio\streams.py:730) (1 samples, 0.23%)</title><rect x="33.5601%" y="356" width="0.2268%" height="15" fill="rgb(250,127,30)" fg:x="148" fg:w="1"/><text x="33.8101%" y="366.50"></text></g><g><title>_wait_for_data (asyncio\streams.py:537) (1 samples, 0.23%)</title><rect x="33.5601%" y="372" width="0.2268%" height="15" fill="rgb(230,49,44)" fg:x="148" fg:w="1"/><text x="33.8101%" y="382.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="33.5601%" y="388" width="0.2268%" height="15" fill="rgb(229,67,23)" fg:x="148" fg:w="1"/><text x="33.8101%" y="398.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="33.5601%" y="404" width="0.2268%" height="15" fill="rgb(249,83,47)" fg:x="148" fg:w="1"/><text x="33.8101%" y="414.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="33.5601%" y="420" width="0.2268%" height="15" fill="rgb(215,43,3)" fg:x="148" fg:w="1"/><text x="33.8101%" y="430.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="33.5601%" y="436" width="0.2268%" height="15" fill="rgb(238,154,13)" fg:x="148" fg:w="1"/><text x="33.8101%" y="446.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="33.5601%" y="452" width="0.2268%" height="15" fill="rgb(219,56,2)" fg:x="148" fg:w="1"/><text x="33.8101%" y="462.50"></text></g><g><title>_loop (tasks\__init__.py:246) (15 samples, 3.40%)</title><rect x="30.8390%" y="196" width="3.4014%" height="15" fill="rgb(233,0,4)" fg:x="136" fg:w="15"/><text x="31.0890%" y="206.50">_lo..</text></g><g><title>check_stock_lifecycle_loop (gacha\services\scheduled_task_orchestrator.py:104) (15 samples, 3.40%)</title><rect x="30.8390%" y="212" width="3.4014%" height="15" fill="rgb(235,30,7)" fg:x="136" fg:w="15"/><text x="31.0890%" y="222.50">che..</text></g><g><title>check_and_update_all_stocks_lifecycle (gacha\services\stock_lifecycle_service.py:725) (3 samples, 0.68%)</title><rect x="33.5601%" y="228" width="0.6803%" height="15" fill="rgb(250,79,13)" fg:x="148" fg:w="3"/><text x="33.8101%" y="238.50"></text></g><g><title>_get_consecutive_checks_data (gacha\services\stock_lifecycle_service.py:43) (3 samples, 0.68%)</title><rect x="33.5601%" y="244" width="0.6803%" height="15" fill="rgb(211,146,34)" fg:x="148" fg:w="3"/><text x="33.8101%" y="254.50"></text></g><g><title>execute_command (redis\asyncio\client.py:677) (2 samples, 0.45%)</title><rect x="33.7868%" y="260" width="0.4535%" height="15" fill="rgb(228,22,38)" fg:x="149" fg:w="2"/><text x="34.0368%" y="270.50"></text></g><g><title>call_with_retry (redis\asyncio\retry.py:71) (2 samples, 0.45%)</title><rect x="33.7868%" y="276" width="0.4535%" height="15" fill="rgb(235,168,5)" fg:x="149" fg:w="2"/><text x="34.0368%" y="286.50"></text></g><g><title>_send_command_parse_response (redis\asyncio\client.py:652) (2 samples, 0.45%)</title><rect x="33.7868%" y="292" width="0.4535%" height="15" fill="rgb(221,155,16)" fg:x="149" fg:w="2"/><text x="34.0368%" y="302.50"></text></g><g><title>parse_response (redis\asyncio\client.py:698) (2 samples, 0.45%)</title><rect x="33.7868%" y="308" width="0.4535%" height="15" fill="rgb(215,215,53)" fg:x="149" fg:w="2"/><text x="34.0368%" y="318.50"></text></g><g><title>read_response (redis\asyncio\connection.py:594) (2 samples, 0.45%)</title><rect x="33.7868%" y="324" width="0.4535%" height="15" fill="rgb(223,4,10)" fg:x="149" fg:w="2"/><text x="34.0368%" y="334.50"></text></g><g><title>read_response (redis\_parsers\hiredis.py:268) (2 samples, 0.45%)</title><rect x="33.7868%" y="340" width="0.4535%" height="15" fill="rgb(234,103,6)" fg:x="149" fg:w="2"/><text x="34.0368%" y="350.50"></text></g><g><title>read_from_socket (redis\_parsers\hiredis.py:245) (2 samples, 0.45%)</title><rect x="33.7868%" y="356" width="0.4535%" height="15" fill="rgb(227,97,0)" fg:x="149" fg:w="2"/><text x="34.0368%" y="366.50"></text></g><g><title>read (asyncio\streams.py:730) (2 samples, 0.45%)</title><rect x="33.7868%" y="372" width="0.4535%" height="15" fill="rgb(234,150,53)" fg:x="149" fg:w="2"/><text x="34.0368%" y="382.50"></text></g><g><title>_wait_for_data (asyncio\streams.py:537) (2 samples, 0.45%)</title><rect x="33.7868%" y="388" width="0.4535%" height="15" fill="rgb(228,201,54)" fg:x="149" fg:w="2"/><text x="34.0368%" y="398.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (2 samples, 0.45%)</title><rect x="33.7868%" y="404" width="0.4535%" height="15" fill="rgb(222,22,37)" fg:x="149" fg:w="2"/><text x="34.0368%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (2 samples, 0.45%)</title><rect x="33.7868%" y="420" width="0.4535%" height="15" fill="rgb(237,53,32)" fg:x="149" fg:w="2"/><text x="34.0368%" y="430.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="33.7868%" y="436" width="0.4535%" height="15" fill="rgb(233,25,53)" fg:x="149" fg:w="2"/><text x="34.0368%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="33.7868%" y="452" width="0.4535%" height="15" fill="rgb(210,40,34)" fg:x="149" fg:w="2"/><text x="34.0368%" y="462.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="33.7868%" y="468" width="0.4535%" height="15" fill="rgb(241,220,44)" fg:x="149" fg:w="2"/><text x="34.0368%" y="478.50"></text></g><g><title>_loop (tasks\__init__.py:259) (1 samples, 0.23%)</title><rect x="34.2404%" y="196" width="0.2268%" height="15" fill="rgb(235,28,35)" fg:x="151" fg:w="1"/><text x="34.4904%" y="206.50"></text></g><g><title>_try_sleep_until (tasks\__init__.py:198) (1 samples, 0.23%)</title><rect x="34.2404%" y="212" width="0.2268%" height="15" fill="rgb(210,56,17)" fg:x="151" fg:w="1"/><text x="34.4904%" y="222.50"></text></g><g><title>__init__ (tasks\__init__.py:112) (1 samples, 0.23%)</title><rect x="34.2404%" y="228" width="0.2268%" height="15" fill="rgb(224,130,29)" fg:x="151" fg:w="1"/><text x="34.4904%" y="238.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="34.2404%" y="244" width="0.2268%" height="15" fill="rgb(235,212,8)" fg:x="151" fg:w="1"/><text x="34.4904%" y="254.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="34.2404%" y="260" width="0.2268%" height="15" fill="rgb(223,33,50)" fg:x="151" fg:w="1"/><text x="34.4904%" y="270.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="34.2404%" y="276" width="0.2268%" height="15" fill="rgb(219,149,13)" fg:x="151" fg:w="1"/><text x="34.4904%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="34.2404%" y="292" width="0.2268%" height="15" fill="rgb(250,156,29)" fg:x="151" fg:w="1"/><text x="34.4904%" y="302.50"></text></g><g><title>line (traceback.py:373) (1 samples, 0.23%)</title><rect x="34.2404%" y="308" width="0.2268%" height="15" fill="rgb(216,193,19)" fg:x="151" fg:w="1"/><text x="34.4904%" y="318.50"></text></g><g><title>_set_lines (traceback.py:351) (1 samples, 0.23%)</title><rect x="34.2404%" y="324" width="0.2268%" height="15" fill="rgb(216,135,14)" fg:x="151" fg:w="1"/><text x="34.4904%" y="334.50"></text></g><g><title>getline (linecache.py:28) (1 samples, 0.23%)</title><rect x="34.2404%" y="340" width="0.2268%" height="15" fill="rgb(241,47,5)" fg:x="151" fg:w="1"/><text x="34.4904%" y="350.50"></text></g><g><title>extract_stack (traceback.py:258) (1 samples, 0.23%)</title><rect x="34.4671%" y="260" width="0.2268%" height="15" fill="rgb(233,42,35)" fg:x="152" fg:w="1"/><text x="34.7171%" y="270.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:488) (1 samples, 0.23%)</title><rect x="34.6939%" y="292" width="0.2268%" height="15" fill="rgb(231,13,6)" fg:x="153" fg:w="1"/><text x="34.9439%" y="302.50"></text></g><g><title>checkcache (linecache.py:76) (1 samples, 0.23%)</title><rect x="35.1474%" y="308" width="0.2268%" height="15" fill="rgb(207,181,40)" fg:x="155" fg:w="1"/><text x="35.3974%" y="318.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (23 samples, 5.22%)</title><rect x="34.9206%" y="292" width="5.2154%" height="15" fill="rgb(254,173,49)" fg:x="154" fg:w="23"/><text x="35.1706%" y="302.50">_extra..</text></g><g><title>checkcache (linecache.py:94) (21 samples, 4.76%)</title><rect x="35.3741%" y="308" width="4.7619%" height="15" fill="rgb(221,1,38)" fg:x="156" fg:w="21"/><text x="35.6241%" y="318.50">checkc..</text></g><g><title>_set_lines (traceback.py:349) (1 samples, 0.23%)</title><rect x="40.3628%" y="324" width="0.2268%" height="15" fill="rgb(206,124,46)" fg:x="178" fg:w="1"/><text x="40.6128%" y="334.50"></text></g><g><title>line (traceback.py:373) (2 samples, 0.45%)</title><rect x="40.3628%" y="308" width="0.4535%" height="15" fill="rgb(249,21,11)" fg:x="178" fg:w="2"/><text x="40.6128%" y="318.50"></text></g><g><title>_set_lines (traceback.py:351) (1 samples, 0.23%)</title><rect x="40.5896%" y="324" width="0.2268%" height="15" fill="rgb(222,201,40)" fg:x="179" fg:w="1"/><text x="40.8396%" y="334.50"></text></g><g><title>_loop_reading (asyncio\proactor_events.py:306) (29 samples, 6.58%)</title><rect x="34.4671%" y="196" width="6.5760%" height="15" fill="rgb(235,61,29)" fg:x="152" fg:w="29"/><text x="34.7171%" y="206.50">_loop_rea..</text></g><g><title>recv_into (asyncio\windows_events.py:507) (29 samples, 6.58%)</title><rect x="34.4671%" y="212" width="6.5760%" height="15" fill="rgb(219,207,3)" fg:x="152" fg:w="29"/><text x="34.7171%" y="222.50">recv_into..</text></g><g><title>_register (asyncio\windows_events.py:721) (29 samples, 6.58%)</title><rect x="34.4671%" y="228" width="6.5760%" height="15" fill="rgb(222,56,46)" fg:x="152" fg:w="29"/><text x="34.7171%" y="238.50">_register..</text></g><g><title>__init__ (asyncio\windows_events.py:56) (29 samples, 6.58%)</title><rect x="34.4671%" y="244" width="6.5760%" height="15" fill="rgb(239,76,54)" fg:x="152" fg:w="29"/><text x="34.7171%" y="254.50">__init__ ..</text></g><g><title>extract_stack (traceback.py:260) (28 samples, 6.35%)</title><rect x="34.6939%" y="260" width="6.3492%" height="15" fill="rgb(231,124,27)" fg:x="153" fg:w="28"/><text x="34.9439%" y="270.50">extract_..</text></g><g><title>extract (traceback.py:449) (28 samples, 6.35%)</title><rect x="34.6939%" y="276" width="6.3492%" height="15" fill="rgb(249,195,6)" fg:x="153" fg:w="28"/><text x="34.9439%" y="286.50">extract ..</text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (4 samples, 0.91%)</title><rect x="40.1361%" y="292" width="0.9070%" height="15" fill="rgb(237,174,47)" fg:x="177" fg:w="4"/><text x="40.3861%" y="302.50"></text></g><g><title>line (traceback.py:377) (1 samples, 0.23%)</title><rect x="40.8163%" y="308" width="0.2268%" height="15" fill="rgb(206,201,31)" fg:x="180" fg:w="1"/><text x="41.0663%" y="318.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:476) (1 samples, 0.23%)</title><rect x="41.0431%" y="292" width="0.2268%" height="15" fill="rgb(231,57,52)" fg:x="181" fg:w="1"/><text x="41.2931%" y="302.50"></text></g><g><title>extended_frame_gen (traceback.py:447) (1 samples, 0.23%)</title><rect x="41.0431%" y="308" width="0.2268%" height="15" fill="rgb(248,177,22)" fg:x="181" fg:w="1"/><text x="41.2931%" y="318.50"></text></g><g><title>_loop_reading (asyncio\proactor_events.py:322) (3 samples, 0.68%)</title><rect x="41.0431%" y="196" width="0.6803%" height="15" fill="rgb(215,211,37)" fg:x="181" fg:w="3"/><text x="41.2931%" y="206.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (3 samples, 0.68%)</title><rect x="41.0431%" y="212" width="0.6803%" height="15" fill="rgb(241,128,51)" fg:x="181" fg:w="3"/><text x="41.2931%" y="222.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (3 samples, 0.68%)</title><rect x="41.0431%" y="228" width="0.6803%" height="15" fill="rgb(227,165,31)" fg:x="181" fg:w="3"/><text x="41.2931%" y="238.50"></text></g><g><title>__init__ (asyncio\events.py:47) (3 samples, 0.68%)</title><rect x="41.0431%" y="244" width="0.6803%" height="15" fill="rgb(228,167,24)" fg:x="181" fg:w="3"/><text x="41.2931%" y="254.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (3 samples, 0.68%)</title><rect x="41.0431%" y="260" width="0.6803%" height="15" fill="rgb(228,143,12)" fg:x="181" fg:w="3"/><text x="41.2931%" y="270.50"></text></g><g><title>extract (traceback.py:449) (3 samples, 0.68%)</title><rect x="41.0431%" y="276" width="0.6803%" height="15" fill="rgb(249,149,8)" fg:x="181" fg:w="3"/><text x="41.2931%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="41.2698%" y="292" width="0.4535%" height="15" fill="rgb(243,35,44)" fg:x="182" fg:w="2"/><text x="41.5198%" y="302.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="41.2698%" y="308" width="0.4535%" height="15" fill="rgb(246,89,9)" fg:x="182" fg:w="2"/><text x="41.5198%" y="318.50"></text></g><g><title>_data_received (asyncio\proactor_events.py:263) (2 samples, 0.45%)</title><rect x="41.7234%" y="212" width="0.4535%" height="15" fill="rgb(233,213,13)" fg:x="184" fg:w="2"/><text x="41.9734%" y="222.50"></text></g><g><title>data_received (aiohttp\client_proto.py:299) (1 samples, 0.23%)</title><rect x="42.4036%" y="292" width="0.2268%" height="15" fill="rgb(233,141,41)" fg:x="187" fg:w="1"/><text x="42.6536%" y="302.50"></text></g><g><title>call_soon (asyncio\base_events.py:836) (1 samples, 0.23%)</title><rect x="42.4036%" y="308" width="0.2268%" height="15" fill="rgb(239,167,4)" fg:x="187" fg:w="1"/><text x="42.6536%" y="318.50"></text></g><g><title>_check_callback (asyncio\base_events.py:844) (1 samples, 0.23%)</title><rect x="42.4036%" y="324" width="0.2268%" height="15" fill="rgb(209,217,16)" fg:x="187" fg:w="1"/><text x="42.6536%" y="334.50"></text></g><g><title>iscoroutinefunction (asyncio\coroutines.py:22) (1 samples, 0.23%)</title><rect x="42.4036%" y="340" width="0.2268%" height="15" fill="rgb(219,88,35)" fg:x="187" fg:w="1"/><text x="42.6536%" y="350.50"></text></g><g><title>iscoroutinefunction (inspect.py:443) (1 samples, 0.23%)</title><rect x="42.4036%" y="356" width="0.2268%" height="15" fill="rgb(220,193,23)" fg:x="187" fg:w="1"/><text x="42.6536%" y="366.50"></text></g><g><title>_has_code_flag (inspect.py:399) (1 samples, 0.23%)</title><rect x="42.4036%" y="372" width="0.2268%" height="15" fill="rgb(230,90,52)" fg:x="187" fg:w="1"/><text x="42.6536%" y="382.50"></text></g><g><title>_do_read (asyncio\sslproto.py:740) (3 samples, 0.68%)</title><rect x="42.1769%" y="260" width="0.6803%" height="15" fill="rgb(252,106,19)" fg:x="186" fg:w="3"/><text x="42.4269%" y="270.50"></text></g><g><title>_do_read__copied (asyncio\sslproto.py:800) (2 samples, 0.45%)</title><rect x="42.4036%" y="276" width="0.4535%" height="15" fill="rgb(206,74,20)" fg:x="187" fg:w="2"/><text x="42.6536%" y="286.50"></text></g><g><title>data_received (aiohttp\client_proto.py:346) (1 samples, 0.23%)</title><rect x="42.6304%" y="292" width="0.2268%" height="15" fill="rgb(230,138,44)" fg:x="188" fg:w="1"/><text x="42.8804%" y="302.50"></text></g><g><title>feed_data (aiohttp\streams.py:659) (1 samples, 0.23%)</title><rect x="42.6304%" y="308" width="0.2268%" height="15" fill="rgb(235,182,43)" fg:x="188" fg:w="1"/><text x="42.8804%" y="318.50"></text></g><g><title>set_result (aiohttp\helpers.py:759) (1 samples, 0.23%)</title><rect x="42.6304%" y="324" width="0.2268%" height="15" fill="rgb(242,16,51)" fg:x="188" fg:w="1"/><text x="42.8804%" y="334.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="42.6304%" y="340" width="0.2268%" height="15" fill="rgb(248,9,4)" fg:x="188" fg:w="1"/><text x="42.8804%" y="350.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="42.6304%" y="356" width="0.2268%" height="15" fill="rgb(210,31,22)" fg:x="188" fg:w="1"/><text x="42.8804%" y="366.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="42.6304%" y="372" width="0.2268%" height="15" fill="rgb(239,54,39)" fg:x="188" fg:w="1"/><text x="42.8804%" y="382.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="42.6304%" y="388" width="0.2268%" height="15" fill="rgb(230,99,41)" fg:x="188" fg:w="1"/><text x="42.8804%" y="398.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="42.6304%" y="404" width="0.2268%" height="15" fill="rgb(253,106,12)" fg:x="188" fg:w="1"/><text x="42.8804%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:488) (1 samples, 0.23%)</title><rect x="42.6304%" y="420" width="0.2268%" height="15" fill="rgb(213,46,41)" fg:x="188" fg:w="1"/><text x="42.8804%" y="430.50"></text></g><g><title>_data_received (asyncio\proactor_events.py:265) (4 samples, 0.91%)</title><rect x="42.1769%" y="212" width="0.9070%" height="15" fill="rgb(215,133,35)" fg:x="186" fg:w="4"/><text x="42.4269%" y="222.50"></text></g><g><title>_feed_data_to_buffered_proto (asyncio\protocols.py:210) (4 samples, 0.91%)</title><rect x="42.1769%" y="228" width="0.9070%" height="15" fill="rgb(213,28,5)" fg:x="186" fg:w="4"/><text x="42.4269%" y="238.50"></text></g><g><title>buffer_updated (asyncio\sslproto.py:445) (4 samples, 0.91%)</title><rect x="42.1769%" y="244" width="0.9070%" height="15" fill="rgb(215,77,49)" fg:x="186" fg:w="4"/><text x="42.4269%" y="254.50"></text></g><g><title>_do_read (asyncio\sslproto.py:744) (1 samples, 0.23%)</title><rect x="42.8571%" y="260" width="0.2268%" height="15" fill="rgb(248,100,22)" fg:x="189" fg:w="1"/><text x="43.1071%" y="270.50"></text></g><g><title>_process_outgoing (asyncio\sslproto.py:720) (1 samples, 0.23%)</title><rect x="42.8571%" y="276" width="0.2268%" height="15" fill="rgb(208,67,9)" fg:x="189" fg:w="1"/><text x="43.1071%" y="286.50"></text></g><g><title>_has_code_flag (inspect.py:399) (1 samples, 0.23%)</title><rect x="44.4444%" y="292" width="0.2268%" height="15" fill="rgb(219,133,21)" fg:x="196" fg:w="1"/><text x="44.6944%" y="302.50"></text></g><g><title>_has_code_flag (inspect.py:404) (1 samples, 0.23%)</title><rect x="44.6712%" y="292" width="0.2268%" height="15" fill="rgb(246,46,29)" fg:x="197" fg:w="1"/><text x="44.9212%" y="302.50"></text></g><g><title>_unwrap_partialmethod (functools.py:444) (1 samples, 0.23%)</title><rect x="44.6712%" y="308" width="0.2268%" height="15" fill="rgb(246,185,52)" fg:x="197" fg:w="1"/><text x="44.9212%" y="318.50"></text></g><g><title>_has_code_flag (inspect.py:406) (1 samples, 0.23%)</title><rect x="44.8980%" y="292" width="0.2268%" height="15" fill="rgb(252,136,11)" fg:x="198" fg:w="1"/><text x="45.1480%" y="302.50"></text></g><g><title>call_soon (asyncio\base_events.py:836) (4 samples, 0.91%)</title><rect x="44.4444%" y="228" width="0.9070%" height="15" fill="rgb(219,138,53)" fg:x="196" fg:w="4"/><text x="44.6944%" y="238.50"></text></g><g><title>_check_callback (asyncio\base_events.py:844) (4 samples, 0.91%)</title><rect x="44.4444%" y="244" width="0.9070%" height="15" fill="rgb(211,51,23)" fg:x="196" fg:w="4"/><text x="44.6944%" y="254.50"></text></g><g><title>iscoroutinefunction (asyncio\coroutines.py:22) (4 samples, 0.91%)</title><rect x="44.4444%" y="260" width="0.9070%" height="15" fill="rgb(247,221,28)" fg:x="196" fg:w="4"/><text x="44.6944%" y="270.50"></text></g><g><title>iscoroutinefunction (inspect.py:443) (4 samples, 0.91%)</title><rect x="44.4444%" y="276" width="0.9070%" height="15" fill="rgb(251,222,45)" fg:x="196" fg:w="4"/><text x="44.6944%" y="286.50"></text></g><g><title>_has_code_flag (inspect.py:408) (1 samples, 0.23%)</title><rect x="45.1247%" y="292" width="0.2268%" height="15" fill="rgb(217,162,53)" fg:x="199" fg:w="1"/><text x="45.3747%" y="302.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:488) (1 samples, 0.23%)</title><rect x="45.3515%" y="308" width="0.2268%" height="15" fill="rgb(229,93,14)" fg:x="200" fg:w="1"/><text x="45.6015%" y="318.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (15 samples, 3.40%)</title><rect x="45.3515%" y="228" width="3.4014%" height="15" fill="rgb(209,67,49)" fg:x="200" fg:w="15"/><text x="45.6015%" y="238.50">cal..</text></g><g><title>_call_soon (asyncio\base_events.py:853) (15 samples, 3.40%)</title><rect x="45.3515%" y="244" width="3.4014%" height="15" fill="rgb(213,87,29)" fg:x="200" fg:w="15"/><text x="45.6015%" y="254.50">_ca..</text></g><g><title>__init__ (asyncio\events.py:47) (15 samples, 3.40%)</title><rect x="45.3515%" y="260" width="3.4014%" height="15" fill="rgb(205,151,52)" fg:x="200" fg:w="15"/><text x="45.6015%" y="270.50">__i..</text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (15 samples, 3.40%)</title><rect x="45.3515%" y="276" width="3.4014%" height="15" fill="rgb(253,215,39)" fg:x="200" fg:w="15"/><text x="45.6015%" y="286.50">ext..</text></g><g><title>extract (traceback.py:449) (15 samples, 3.40%)</title><rect x="45.3515%" y="292" width="3.4014%" height="15" fill="rgb(221,220,41)" fg:x="200" fg:w="15"/><text x="45.6015%" y="302.50">ext..</text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (14 samples, 3.17%)</title><rect x="45.5782%" y="308" width="3.1746%" height="15" fill="rgb(218,133,21)" fg:x="201" fg:w="14"/><text x="45.8282%" y="318.50">_ex..</text></g><g><title>checkcache (linecache.py:94) (14 samples, 3.17%)</title><rect x="45.5782%" y="324" width="3.1746%" height="15" fill="rgb(221,193,43)" fg:x="201" fg:w="14"/><text x="45.8282%" y="334.50">che..</text></g><g><title>_loop_reading (asyncio\proactor_events.py:325) (32 samples, 7.26%)</title><rect x="41.7234%" y="196" width="7.2562%" height="15" fill="rgb(240,128,52)" fg:x="184" fg:w="32"/><text x="41.9734%" y="206.50">_loop_read..</text></g><g><title>_data_received (asyncio\proactor_events.py:274) (26 samples, 5.90%)</title><rect x="43.0839%" y="212" width="5.8957%" height="15" fill="rgb(253,114,12)" fg:x="190" fg:w="26"/><text x="43.3339%" y="222.50">_data_r..</text></g><g><title>data_received (asyncio\streams.py:281) (1 samples, 0.23%)</title><rect x="48.7528%" y="228" width="0.2268%" height="15" fill="rgb(215,223,47)" fg:x="215" fg:w="1"/><text x="49.0028%" y="238.50"></text></g><g><title>feed_data (asyncio\streams.py:500) (1 samples, 0.23%)</title><rect x="48.7528%" y="244" width="0.2268%" height="15" fill="rgb(248,225,23)" fg:x="215" fg:w="1"/><text x="49.0028%" y="254.50"></text></g><g><title>_wakeup_waiter (asyncio\streams.py:474) (1 samples, 0.23%)</title><rect x="48.7528%" y="260" width="0.2268%" height="15" fill="rgb(250,108,0)" fg:x="215" fg:w="1"/><text x="49.0028%" y="270.50"></text></g><g><title>call_soon (asyncio\base_events.py:835) (1 samples, 0.23%)</title><rect x="48.7528%" y="276" width="0.2268%" height="15" fill="rgb(228,208,7)" fg:x="215" fg:w="1"/><text x="49.0028%" y="286.50"></text></g><g><title>_check_thread (asyncio\base_events.py:870) (1 samples, 0.23%)</title><rect x="48.7528%" y="292" width="0.2268%" height="15" fill="rgb(244,45,10)" fg:x="215" fg:w="1"/><text x="49.0028%" y="302.50"></text></g><g><title>_loop_self_reading (asyncio\proactor_events.py:801) (1 samples, 0.23%)</title><rect x="48.9796%" y="196" width="0.2268%" height="15" fill="rgb(207,125,25)" fg:x="216" fg:w="1"/><text x="49.2296%" y="206.50"></text></g><g><title>recv (asyncio\windows_events.py:494) (1 samples, 0.23%)</title><rect x="48.9796%" y="212" width="0.2268%" height="15" fill="rgb(210,195,18)" fg:x="216" fg:w="1"/><text x="49.2296%" y="222.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="48.9796%" y="228" width="0.2268%" height="15" fill="rgb(249,80,12)" fg:x="216" fg:w="1"/><text x="49.2296%" y="238.50"></text></g><g><title>call_at (asyncio\base_events.py:815) (1 samples, 0.23%)</title><rect x="49.2063%" y="260" width="0.2268%" height="15" fill="rgb(221,65,9)" fg:x="217" fg:w="1"/><text x="49.4563%" y="270.50"></text></g><g><title>_check_callback (asyncio\base_events.py:844) (1 samples, 0.23%)</title><rect x="49.2063%" y="276" width="0.2268%" height="15" fill="rgb(235,49,36)" fg:x="217" fg:w="1"/><text x="49.4563%" y="286.50"></text></g><g><title>iscoroutinefunction (asyncio\coroutines.py:22) (1 samples, 0.23%)</title><rect x="49.2063%" y="292" width="0.2268%" height="15" fill="rgb(225,32,20)" fg:x="217" fg:w="1"/><text x="49.4563%" y="302.50"></text></g><g><title>iscoroutinefunction (inspect.py:443) (1 samples, 0.23%)</title><rect x="49.2063%" y="308" width="0.2268%" height="15" fill="rgb(215,141,46)" fg:x="217" fg:w="1"/><text x="49.4563%" y="318.50"></text></g><g><title>_has_code_flag (inspect.py:408) (1 samples, 0.23%)</title><rect x="49.2063%" y="324" width="0.2268%" height="15" fill="rgb(250,160,47)" fg:x="217" fg:w="1"/><text x="49.4563%" y="334.50"></text></g><g><title>isfunction (inspect.py:397) (1 samples, 0.23%)</title><rect x="49.2063%" y="340" width="0.2268%" height="15" fill="rgb(216,222,40)" fg:x="217" fg:w="1"/><text x="49.4563%" y="350.50"></text></g><g><title>__aenter__ (asyncio\timeouts.py:94) (3 samples, 0.68%)</title><rect x="49.2063%" y="228" width="0.6803%" height="15" fill="rgb(234,217,39)" fg:x="217" fg:w="3"/><text x="49.4563%" y="238.50"></text></g><g><title>reschedule (asyncio\timeouts.py:71) (3 samples, 0.68%)</title><rect x="49.2063%" y="244" width="0.6803%" height="15" fill="rgb(207,178,40)" fg:x="217" fg:w="3"/><text x="49.4563%" y="254.50"></text></g><g><title>call_at (asyncio\base_events.py:816) (2 samples, 0.45%)</title><rect x="49.4331%" y="260" width="0.4535%" height="15" fill="rgb(221,136,13)" fg:x="218" fg:w="2"/><text x="49.6831%" y="270.50"></text></g><g><title>__init__ (asyncio\events.py:114) (2 samples, 0.45%)</title><rect x="49.4331%" y="276" width="0.4535%" height="15" fill="rgb(249,199,10)" fg:x="218" fg:w="2"/><text x="49.6831%" y="286.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="49.4331%" y="292" width="0.4535%" height="15" fill="rgb(249,222,13)" fg:x="218" fg:w="2"/><text x="49.6831%" y="302.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="49.4331%" y="308" width="0.4535%" height="15" fill="rgb(244,185,38)" fg:x="218" fg:w="2"/><text x="49.6831%" y="318.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="49.4331%" y="324" width="0.4535%" height="15" fill="rgb(236,202,9)" fg:x="218" fg:w="2"/><text x="49.6831%" y="334.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="49.4331%" y="340" width="0.4535%" height="15" fill="rgb(250,229,37)" fg:x="218" fg:w="2"/><text x="49.6831%" y="350.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="49.4331%" y="356" width="0.4535%" height="15" fill="rgb(206,174,23)" fg:x="218" fg:w="2"/><text x="49.6831%" y="366.50"></text></g><g><title>wait_for (asyncio\tasks.py:506) (4 samples, 0.91%)</title><rect x="49.2063%" y="212" width="0.9070%" height="15" fill="rgb(211,33,43)" fg:x="217" fg:w="4"/><text x="49.4563%" y="222.50"></text></g><g><title>__aexit__ (asyncio\timeouts.py:97) (1 samples, 0.23%)</title><rect x="49.8866%" y="228" width="0.2268%" height="15" fill="rgb(245,58,50)" fg:x="220" fg:w="1"/><text x="50.1366%" y="238.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (5 samples, 1.13%)</title><rect x="50.1134%" y="292" width="1.1338%" height="15" fill="rgb(244,68,36)" fg:x="221" fg:w="5"/><text x="50.3634%" y="302.50"></text></g><g><title>checkcache (linecache.py:94) (5 samples, 1.13%)</title><rect x="50.1134%" y="308" width="1.1338%" height="15" fill="rgb(232,229,15)" fg:x="221" fg:w="5"/><text x="50.3634%" y="318.50"></text></g><g><title>get (asyncio\queues.py:183) (6 samples, 1.36%)</title><rect x="50.1134%" y="228" width="1.3605%" height="15" fill="rgb(254,30,23)" fg:x="221" fg:w="6"/><text x="50.3634%" y="238.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (6 samples, 1.36%)</title><rect x="50.1134%" y="244" width="1.3605%" height="15" fill="rgb(235,160,14)" fg:x="221" fg:w="6"/><text x="50.3634%" y="254.50"></text></g><g><title>extract_stack (traceback.py:260) (6 samples, 1.36%)</title><rect x="50.1134%" y="260" width="1.3605%" height="15" fill="rgb(212,155,44)" fg:x="221" fg:w="6"/><text x="50.3634%" y="270.50"></text></g><g><title>extract (traceback.py:449) (6 samples, 1.36%)</title><rect x="50.1134%" y="276" width="1.3605%" height="15" fill="rgb(226,2,50)" fg:x="221" fg:w="6"/><text x="50.3634%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="51.2472%" y="292" width="0.2268%" height="15" fill="rgb(234,177,6)" fg:x="226" fg:w="1"/><text x="51.4972%" y="302.50"></text></g><g><title>line (traceback.py:377) (1 samples, 0.23%)</title><rect x="51.2472%" y="308" width="0.2268%" height="15" fill="rgb(217,24,9)" fg:x="226" fg:w="1"/><text x="51.4972%" y="318.50"></text></g><g><title>_maintenance_worker (gacha\services\market_stats_maintenance_service.py:217) (11 samples, 2.49%)</title><rect x="49.2063%" y="196" width="2.4943%" height="15" fill="rgb(220,13,46)" fg:x="217" fg:w="11"/><text x="49.4563%" y="206.50">_m..</text></g><g><title>wait_for (asyncio\tasks.py:507) (7 samples, 1.59%)</title><rect x="50.1134%" y="212" width="1.5873%" height="15" fill="rgb(239,221,27)" fg:x="221" fg:w="7"/><text x="50.3634%" y="222.50"></text></g><g><title>get (asyncio\queues.py:201) (1 samples, 0.23%)</title><rect x="51.4739%" y="228" width="0.2268%" height="15" fill="rgb(222,198,25)" fg:x="227" fg:w="1"/><text x="51.7239%" y="238.50"></text></g><g><title>_has_code_flag (inspect.py:404) (1 samples, 0.23%)</title><rect x="51.9274%" y="276" width="0.2268%" height="15" fill="rgb(211,99,13)" fg:x="229" fg:w="1"/><text x="52.1774%" y="286.50"></text></g><g><title>_unwrap_partialmethod (functools.py:444) (1 samples, 0.23%)</title><rect x="51.9274%" y="292" width="0.2268%" height="15" fill="rgb(232,111,31)" fg:x="229" fg:w="1"/><text x="52.1774%" y="302.50"></text></g><g><title>_has_code_flag (inspect.py:405) (1 samples, 0.23%)</title><rect x="52.1542%" y="276" width="0.2268%" height="15" fill="rgb(245,82,37)" fg:x="230" fg:w="1"/><text x="52.4042%" y="286.50"></text></g><g><title>ismethod (inspect.py:308) (1 samples, 0.23%)</title><rect x="52.1542%" y="292" width="0.2268%" height="15" fill="rgb(227,149,46)" fg:x="230" fg:w="1"/><text x="52.4042%" y="302.50"></text></g><g><title>call_soon (asyncio\base_events.py:836) (3 samples, 0.68%)</title><rect x="51.9274%" y="212" width="0.6803%" height="15" fill="rgb(218,36,50)" fg:x="229" fg:w="3"/><text x="52.1774%" y="222.50"></text></g><g><title>_check_callback (asyncio\base_events.py:844) (3 samples, 0.68%)</title><rect x="51.9274%" y="228" width="0.6803%" height="15" fill="rgb(226,80,48)" fg:x="229" fg:w="3"/><text x="52.1774%" y="238.50"></text></g><g><title>iscoroutinefunction (asyncio\coroutines.py:22) (3 samples, 0.68%)</title><rect x="51.9274%" y="244" width="0.6803%" height="15" fill="rgb(238,224,15)" fg:x="229" fg:w="3"/><text x="52.1774%" y="254.50"></text></g><g><title>iscoroutinefunction (inspect.py:443) (3 samples, 0.68%)</title><rect x="51.9274%" y="260" width="0.6803%" height="15" fill="rgb(241,136,10)" fg:x="229" fg:w="3"/><text x="52.1774%" y="270.50"></text></g><g><title>_has_code_flag (inspect.py:408) (1 samples, 0.23%)</title><rect x="52.3810%" y="276" width="0.2268%" height="15" fill="rgb(208,32,45)" fg:x="231" fg:w="1"/><text x="52.6310%" y="286.50"></text></g><g><title>_signature_is_functionlike (inspect.py:2161) (1 samples, 0.23%)</title><rect x="52.3810%" y="292" width="0.2268%" height="15" fill="rgb(207,135,9)" fg:x="231" fg:w="1"/><text x="52.6310%" y="302.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:79) (1 samples, 0.23%)</title><rect x="52.6077%" y="260" width="0.2268%" height="15" fill="rgb(206,86,44)" fg:x="232" fg:w="1"/><text x="52.8577%" y="270.50"></text></g><g><title>extract (traceback.py:445) (1 samples, 0.23%)</title><rect x="52.8345%" y="276" width="0.2268%" height="15" fill="rgb(245,177,15)" fg:x="233" fg:w="1"/><text x="53.0845%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:476) (1 samples, 0.23%)</title><rect x="53.0612%" y="292" width="0.2268%" height="15" fill="rgb(206,64,50)" fg:x="234" fg:w="1"/><text x="53.3112%" y="302.50"></text></g><g><title>extended_frame_gen (traceback.py:446) (1 samples, 0.23%)</title><rect x="53.0612%" y="308" width="0.2268%" height="15" fill="rgb(234,36,40)" fg:x="234" fg:w="1"/><text x="53.3112%" y="318.50"></text></g><g><title>walk_stack (traceback.py:389) (1 samples, 0.23%)</title><rect x="53.0612%" y="324" width="0.2268%" height="15" fill="rgb(213,64,8)" fg:x="234" fg:w="1"/><text x="53.3112%" y="334.50"></text></g><g><title>_on_timeout (asyncio\timeouts.py:129) (17 samples, 3.85%)</title><rect x="51.7007%" y="196" width="3.8549%" height="15" fill="rgb(210,75,36)" fg:x="228" fg:w="17"/><text x="51.9507%" y="206.50">_on_..</text></g><g><title>call_soon (asyncio\base_events.py:837) (13 samples, 2.95%)</title><rect x="52.6077%" y="212" width="2.9478%" height="15" fill="rgb(229,88,21)" fg:x="232" fg:w="13"/><text x="52.8577%" y="222.50">ca..</text></g><g><title>_call_soon (asyncio\base_events.py:853) (13 samples, 2.95%)</title><rect x="52.6077%" y="228" width="2.9478%" height="15" fill="rgb(252,204,47)" fg:x="232" fg:w="13"/><text x="52.8577%" y="238.50">_c..</text></g><g><title>__init__ (asyncio\events.py:47) (13 samples, 2.95%)</title><rect x="52.6077%" y="244" width="2.9478%" height="15" fill="rgb(208,77,27)" fg:x="232" fg:w="13"/><text x="52.8577%" y="254.50">__..</text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (12 samples, 2.72%)</title><rect x="52.8345%" y="260" width="2.7211%" height="15" fill="rgb(221,76,26)" fg:x="233" fg:w="12"/><text x="53.0845%" y="270.50">ex..</text></g><g><title>extract (traceback.py:449) (11 samples, 2.49%)</title><rect x="53.0612%" y="276" width="2.4943%" height="15" fill="rgb(225,139,18)" fg:x="234" fg:w="11"/><text x="53.3112%" y="286.50">ex..</text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (10 samples, 2.27%)</title><rect x="53.2880%" y="292" width="2.2676%" height="15" fill="rgb(230,137,11)" fg:x="235" fg:w="10"/><text x="53.5380%" y="302.50">_..</text></g><g><title>checkcache (linecache.py:94) (10 samples, 2.27%)</title><rect x="53.2880%" y="308" width="2.2676%" height="15" fill="rgb(212,28,1)" fg:x="235" fg:w="10"/><text x="53.5380%" y="318.50">c..</text></g><g><title>_run_event (discord\client.py:481) (1 samples, 0.23%)</title><rect x="55.5556%" y="196" width="0.2268%" height="15" fill="rgb(248,164,17)" fg:x="245" fg:w="1"/><text x="55.8056%" y="206.50"></text></g><g><title>on_app_command_completion (BOT.PY:363) (1 samples, 0.23%)</title><rect x="55.5556%" y="212" width="0.2268%" height="15" fill="rgb(222,171,42)" fg:x="245" fg:w="1"/><text x="55.8056%" y="222.50"></text></g><g><title>create_task (asyncio\tasks.py:410) (1 samples, 0.23%)</title><rect x="55.5556%" y="228" width="0.2268%" height="15" fill="rgb(243,84,45)" fg:x="245" fg:w="1"/><text x="55.8056%" y="238.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="55.5556%" y="244" width="0.2268%" height="15" fill="rgb(252,49,23)" fg:x="245" fg:w="1"/><text x="55.8056%" y="254.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="55.5556%" y="260" width="0.2268%" height="15" fill="rgb(215,19,7)" fg:x="245" fg:w="1"/><text x="55.8056%" y="270.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="55.5556%" y="276" width="0.2268%" height="15" fill="rgb(238,81,41)" fg:x="245" fg:w="1"/><text x="55.8056%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="55.5556%" y="292" width="0.2268%" height="15" fill="rgb(210,199,37)" fg:x="245" fg:w="1"/><text x="55.8056%" y="302.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="55.5556%" y="308" width="0.2268%" height="15" fill="rgb(244,192,49)" fg:x="245" fg:w="1"/><text x="55.8056%" y="318.50"></text></g><g><title>get_filtered_card_ids (gacha\services\collection_service.py:197) (1 samples, 0.23%)</title><rect x="55.7823%" y="244" width="0.2268%" height="15" fill="rgb(226,211,11)" fg:x="246" fg:w="1"/><text x="56.0323%" y="254.50"></text></g><g><title>get_user (gacha\services\user_service.py:46) (1 samples, 0.23%)</title><rect x="55.7823%" y="260" width="0.2268%" height="15" fill="rgb(236,162,54)" fg:x="246" fg:w="1"/><text x="56.0323%" y="270.50"></text></g><g><title>get_user_optional (gacha\repositories\user\user_repository.py:55) (1 samples, 0.23%)</title><rect x="55.7823%" y="276" width="0.2268%" height="15" fill="rgb(220,229,9)" fg:x="246" fg:w="1"/><text x="56.0323%" y="286.50"></text></g><g><title>get_user (gacha\repositories\user\user_repository.py:33) (1 samples, 0.23%)</title><rect x="55.7823%" y="292" width="0.2268%" height="15" fill="rgb(250,87,22)" fg:x="246" fg:w="1"/><text x="56.0323%" y="302.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:156) (1 samples, 0.23%)</title><rect x="55.7823%" y="308" width="0.2268%" height="15" fill="rgb(239,43,17)" fg:x="246" fg:w="1"/><text x="56.0323%" y="318.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="55.7823%" y="324" width="0.2268%" height="15" fill="rgb(231,177,25)" fg:x="246" fg:w="1"/><text x="56.0323%" y="334.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="55.7823%" y="340" width="0.2268%" height="15" fill="rgb(219,179,1)" fg:x="246" fg:w="1"/><text x="56.0323%" y="350.50"></text></g><g><title>shield (asyncio\tasks.py:956) (1 samples, 0.23%)</title><rect x="55.7823%" y="356" width="0.2268%" height="15" fill="rgb(238,219,53)" fg:x="246" fg:w="1"/><text x="56.0323%" y="366.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="55.7823%" y="372" width="0.2268%" height="15" fill="rgb(232,167,36)" fg:x="246" fg:w="1"/><text x="56.0323%" y="382.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="55.7823%" y="388" width="0.2268%" height="15" fill="rgb(244,19,51)" fg:x="246" fg:w="1"/><text x="56.0323%" y="398.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="55.7823%" y="404" width="0.2268%" height="15" fill="rgb(224,6,22)" fg:x="246" fg:w="1"/><text x="56.0323%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="55.7823%" y="420" width="0.2268%" height="15" fill="rgb(224,145,5)" fg:x="246" fg:w="1"/><text x="56.0323%" y="430.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="55.7823%" y="436" width="0.2268%" height="15" fill="rgb(234,130,49)" fg:x="246" fg:w="1"/><text x="56.0323%" y="446.50"></text></g><g><title>get_filtered_card_ids (gacha\repositories\collection\user_collection_repository.py:859) (1 samples, 0.23%)</title><rect x="56.0091%" y="260" width="0.2268%" height="15" fill="rgb(254,6,2)" fg:x="247" fg:w="1"/><text x="56.2591%" y="270.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:112) (1 samples, 0.23%)</title><rect x="56.0091%" y="276" width="0.2268%" height="15" fill="rgb(208,96,46)" fg:x="247" fg:w="1"/><text x="56.2591%" y="286.50"></text></g><g><title>fetch (asyncpg\connection.py:690) (1 samples, 0.23%)</title><rect x="56.0091%" y="292" width="0.2268%" height="15" fill="rgb(239,3,39)" fg:x="247" fg:w="1"/><text x="56.2591%" y="302.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="56.0091%" y="308" width="0.2268%" height="15" fill="rgb(233,210,1)" fg:x="247" fg:w="1"/><text x="56.2591%" y="318.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="56.0091%" y="324" width="0.2268%" height="15" fill="rgb(244,137,37)" fg:x="247" fg:w="1"/><text x="56.2591%" y="334.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="56.0091%" y="340" width="0.2268%" height="15" fill="rgb(240,136,2)" fg:x="247" fg:w="1"/><text x="56.2591%" y="350.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="56.0091%" y="356" width="0.2268%" height="15" fill="rgb(239,18,37)" fg:x="247" fg:w="1"/><text x="56.2591%" y="366.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (1 samples, 0.23%)</title><rect x="56.0091%" y="372" width="0.2268%" height="15" fill="rgb(218,185,22)" fg:x="247" fg:w="1"/><text x="56.2591%" y="382.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="56.0091%" y="388" width="0.2268%" height="15" fill="rgb(225,218,4)" fg:x="247" fg:w="1"/><text x="56.2591%" y="398.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="56.0091%" y="404" width="0.2268%" height="15" fill="rgb(230,182,32)" fg:x="247" fg:w="1"/><text x="56.2591%" y="414.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="56.0091%" y="420" width="0.2268%" height="15" fill="rgb(242,56,43)" fg:x="247" fg:w="1"/><text x="56.2591%" y="430.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="56.0091%" y="436" width="0.2268%" height="15" fill="rgb(233,99,24)" fg:x="247" fg:w="1"/><text x="56.2591%" y="446.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="56.0091%" y="452" width="0.2268%" height="15" fill="rgb(234,209,42)" fg:x="247" fg:w="1"/><text x="56.2591%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="56.0091%" y="468" width="0.2268%" height="15" fill="rgb(227,7,12)" fg:x="247" fg:w="1"/><text x="56.2591%" y="478.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="56.0091%" y="484" width="0.2268%" height="15" fill="rgb(245,203,43)" fg:x="247" fg:w="1"/><text x="56.2591%" y="494.50"></text></g><g><title>_batch_favorite_callback (gacha\views\collection\collection_view\card_view.py:441) (3 samples, 0.68%)</title><rect x="55.7823%" y="212" width="0.6803%" height="15" fill="rgb(238,205,33)" fg:x="246" fg:w="3"/><text x="56.0323%" y="222.50"></text></g><g><title>_handle_batch_operation (gacha\views\collection\collection_view\card_view.py:452) (3 samples, 0.68%)</title><rect x="55.7823%" y="228" width="0.6803%" height="15" fill="rgb(231,56,7)" fg:x="246" fg:w="3"/><text x="56.0323%" y="238.50"></text></g><g><title>get_filtered_card_ids (gacha\services\collection_service.py:202) (2 samples, 0.45%)</title><rect x="56.0091%" y="244" width="0.4535%" height="15" fill="rgb(244,186,29)" fg:x="247" fg:w="2"/><text x="56.2591%" y="254.50"></text></g><g><title>get_filtered_card_ids (gacha\repositories\collection\user_collection_repository.py:860) (1 samples, 0.23%)</title><rect x="56.2358%" y="260" width="0.2268%" height="15" fill="rgb(234,111,31)" fg:x="248" fg:w="1"/><text x="56.4858%" y="270.50"></text></g><g><title>_batch_unfavorite_callback (gacha\views\collection\collection_view\card_view.py:513) (1 samples, 0.23%)</title><rect x="56.4626%" y="212" width="0.2268%" height="15" fill="rgb(241,149,10)" fg:x="249" fg:w="1"/><text x="56.7126%" y="222.50"></text></g><g><title>defer (discord\interactions.py:844) (1 samples, 0.23%)</title><rect x="56.4626%" y="228" width="0.2268%" height="15" fill="rgb(249,206,44)" fg:x="249" fg:w="1"/><text x="56.7126%" y="238.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="56.4626%" y="244" width="0.2268%" height="15" fill="rgb(251,153,30)" fg:x="249" fg:w="1"/><text x="56.7126%" y="254.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="56.4626%" y="260" width="0.2268%" height="15" fill="rgb(239,152,38)" fg:x="249" fg:w="1"/><text x="56.7126%" y="270.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="56.4626%" y="276" width="0.2268%" height="15" fill="rgb(249,139,47)" fg:x="249" fg:w="1"/><text x="56.7126%" y="286.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:748) (1 samples, 0.23%)</title><rect x="56.4626%" y="292" width="0.2268%" height="15" fill="rgb(244,64,35)" fg:x="249" fg:w="1"/><text x="56.7126%" y="302.50"></text></g><g><title>start (aiohttp\client_reqrep.py:532) (1 samples, 0.23%)</title><rect x="56.4626%" y="308" width="0.2268%" height="15" fill="rgb(216,46,15)" fg:x="249" fg:w="1"/><text x="56.7126%" y="318.50"></text></g><g><title>read (aiohttp\streams.py:670) (1 samples, 0.23%)</title><rect x="56.4626%" y="324" width="0.2268%" height="15" fill="rgb(250,74,19)" fg:x="249" fg:w="1"/><text x="56.7126%" y="334.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="56.4626%" y="340" width="0.2268%" height="15" fill="rgb(249,42,33)" fg:x="249" fg:w="1"/><text x="56.7126%" y="350.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="56.4626%" y="356" width="0.2268%" height="15" fill="rgb(242,149,17)" fg:x="249" fg:w="1"/><text x="56.7126%" y="366.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="56.4626%" y="372" width="0.2268%" height="15" fill="rgb(244,29,21)" fg:x="249" fg:w="1"/><text x="56.7126%" y="382.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="56.4626%" y="388" width="0.2268%" height="15" fill="rgb(220,130,37)" fg:x="249" fg:w="1"/><text x="56.7126%" y="398.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="56.4626%" y="404" width="0.2268%" height="15" fill="rgb(211,67,2)" fg:x="249" fg:w="1"/><text x="56.7126%" y="414.50"></text></g><g><title>_handle_batch_operation (gacha\views\collection\collection_view\card_view.py:452) (1 samples, 0.23%)</title><rect x="56.6893%" y="228" width="0.2268%" height="15" fill="rgb(235,68,52)" fg:x="250" fg:w="1"/><text x="56.9393%" y="238.50"></text></g><g><title>get_filtered_card_ids (gacha\services\collection_service.py:197) (1 samples, 0.23%)</title><rect x="56.6893%" y="244" width="0.2268%" height="15" fill="rgb(246,142,3)" fg:x="250" fg:w="1"/><text x="56.9393%" y="254.50"></text></g><g><title>get_user (gacha\services\user_service.py:46) (1 samples, 0.23%)</title><rect x="56.6893%" y="260" width="0.2268%" height="15" fill="rgb(241,25,7)" fg:x="250" fg:w="1"/><text x="56.9393%" y="270.50"></text></g><g><title>get_user_optional (gacha\repositories\user\user_repository.py:55) (1 samples, 0.23%)</title><rect x="56.6893%" y="276" width="0.2268%" height="15" fill="rgb(242,119,39)" fg:x="250" fg:w="1"/><text x="56.9393%" y="286.50"></text></g><g><title>get_user (gacha\repositories\user\user_repository.py:33) (1 samples, 0.23%)</title><rect x="56.6893%" y="292" width="0.2268%" height="15" fill="rgb(241,98,45)" fg:x="250" fg:w="1"/><text x="56.9393%" y="302.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:156) (1 samples, 0.23%)</title><rect x="56.6893%" y="308" width="0.2268%" height="15" fill="rgb(254,28,30)" fg:x="250" fg:w="1"/><text x="56.9393%" y="318.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="56.6893%" y="324" width="0.2268%" height="15" fill="rgb(241,142,54)" fg:x="250" fg:w="1"/><text x="56.9393%" y="334.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="56.6893%" y="340" width="0.2268%" height="15" fill="rgb(222,85,15)" fg:x="250" fg:w="1"/><text x="56.9393%" y="350.50"></text></g><g><title>shield (asyncio\tasks.py:956) (1 samples, 0.23%)</title><rect x="56.6893%" y="356" width="0.2268%" height="15" fill="rgb(210,85,47)" fg:x="250" fg:w="1"/><text x="56.9393%" y="366.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="56.6893%" y="372" width="0.2268%" height="15" fill="rgb(224,206,25)" fg:x="250" fg:w="1"/><text x="56.9393%" y="382.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="56.6893%" y="388" width="0.2268%" height="15" fill="rgb(243,201,19)" fg:x="250" fg:w="1"/><text x="56.9393%" y="398.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="56.6893%" y="404" width="0.2268%" height="15" fill="rgb(236,59,4)" fg:x="250" fg:w="1"/><text x="56.9393%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="56.6893%" y="420" width="0.2268%" height="15" fill="rgb(254,179,45)" fg:x="250" fg:w="1"/><text x="56.9393%" y="430.50"></text></g><g><title>checkcache (linecache.py:90) (1 samples, 0.23%)</title><rect x="56.6893%" y="436" width="0.2268%" height="15" fill="rgb(226,14,10)" fg:x="250" fg:w="1"/><text x="56.9393%" y="446.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="56.9161%" y="308" width="0.2268%" height="15" fill="rgb(244,27,41)" fg:x="251" fg:w="1"/><text x="57.1661%" y="318.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1476) (1 samples, 0.23%)</title><rect x="56.9161%" y="324" width="0.2268%" height="15" fill="rgb(235,35,32)" fg:x="251" fg:w="1"/><text x="57.1661%" y="334.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="56.9161%" y="340" width="0.2268%" height="15" fill="rgb(218,68,31)" fg:x="251" fg:w="1"/><text x="57.1661%" y="350.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="56.9161%" y="356" width="0.2268%" height="15" fill="rgb(207,120,37)" fg:x="251" fg:w="1"/><text x="57.1661%" y="366.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="56.9161%" y="372" width="0.2268%" height="15" fill="rgb(227,98,0)" fg:x="251" fg:w="1"/><text x="57.1661%" y="382.50"></text></g><g><title>line (traceback.py:377) (1 samples, 0.23%)</title><rect x="56.9161%" y="388" width="0.2268%" height="15" fill="rgb(207,7,3)" fg:x="251" fg:w="1"/><text x="57.1661%" y="398.50"></text></g><g><title>_batch_unfavorite_callback (gacha\views\collection\collection_view\card_view.py:514) (3 samples, 0.68%)</title><rect x="56.6893%" y="212" width="0.6803%" height="15" fill="rgb(206,98,19)" fg:x="250" fg:w="3"/><text x="56.9393%" y="222.50"></text></g><g><title>_handle_batch_operation (gacha\views\collection\collection_view\card_view.py:494) (2 samples, 0.45%)</title><rect x="56.9161%" y="228" width="0.4535%" height="15" fill="rgb(217,5,26)" fg:x="251" fg:w="2"/><text x="57.1661%" y="238.50"></text></g><g><title>send (discord\webhook\async_.py:1857) (2 samples, 0.45%)</title><rect x="56.9161%" y="244" width="0.4535%" height="15" fill="rgb(235,190,38)" fg:x="251" fg:w="2"/><text x="57.1661%" y="254.50"></text></g><g><title>request (discord\webhook\async_.py:182) (2 samples, 0.45%)</title><rect x="56.9161%" y="260" width="0.4535%" height="15" fill="rgb(247,86,24)" fg:x="251" fg:w="2"/><text x="57.1661%" y="270.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (2 samples, 0.45%)</title><rect x="56.9161%" y="276" width="0.4535%" height="15" fill="rgb(205,101,16)" fg:x="251" fg:w="2"/><text x="57.1661%" y="286.50"></text></g><g><title>_request (aiohttp\client.py:770) (2 samples, 0.45%)</title><rect x="56.9161%" y="292" width="0.4535%" height="15" fill="rgb(246,168,33)" fg:x="251" fg:w="2"/><text x="57.1661%" y="302.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:748) (1 samples, 0.23%)</title><rect x="57.1429%" y="308" width="0.2268%" height="15" fill="rgb(231,114,1)" fg:x="252" fg:w="1"/><text x="57.3929%" y="318.50"></text></g><g><title>start (aiohttp\client_reqrep.py:532) (1 samples, 0.23%)</title><rect x="57.1429%" y="324" width="0.2268%" height="15" fill="rgb(207,184,53)" fg:x="252" fg:w="1"/><text x="57.3929%" y="334.50"></text></g><g><title>get_user (gacha\repositories\user\user_repository.py:33) (1 samples, 0.23%)</title><rect x="57.3696%" y="308" width="0.2268%" height="15" fill="rgb(224,95,51)" fg:x="253" fg:w="1"/><text x="57.6196%" y="318.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:156) (1 samples, 0.23%)</title><rect x="57.3696%" y="324" width="0.2268%" height="15" fill="rgb(212,188,45)" fg:x="253" fg:w="1"/><text x="57.6196%" y="334.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="57.3696%" y="340" width="0.2268%" height="15" fill="rgb(223,154,38)" fg:x="253" fg:w="1"/><text x="57.6196%" y="350.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="57.3696%" y="356" width="0.2268%" height="15" fill="rgb(251,22,52)" fg:x="253" fg:w="1"/><text x="57.6196%" y="366.50"></text></g><g><title>shield (asyncio\tasks.py:956) (1 samples, 0.23%)</title><rect x="57.3696%" y="372" width="0.2268%" height="15" fill="rgb(229,209,22)" fg:x="253" fg:w="1"/><text x="57.6196%" y="382.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="57.3696%" y="388" width="0.2268%" height="15" fill="rgb(234,138,34)" fg:x="253" fg:w="1"/><text x="57.6196%" y="398.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="57.3696%" y="404" width="0.2268%" height="15" fill="rgb(212,95,11)" fg:x="253" fg:w="1"/><text x="57.6196%" y="414.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="57.3696%" y="420" width="0.2268%" height="15" fill="rgb(240,179,47)" fg:x="253" fg:w="1"/><text x="57.6196%" y="430.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="57.3696%" y="436" width="0.2268%" height="15" fill="rgb(240,163,11)" fg:x="253" fg:w="1"/><text x="57.6196%" y="446.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="57.3696%" y="452" width="0.2268%" height="15" fill="rgb(236,37,12)" fg:x="253" fg:w="1"/><text x="57.6196%" y="462.50"></text></g><g><title>_handle_game_action (blackjack_cog.py:800) (2 samples, 0.45%)</title><rect x="57.3696%" y="228" width="0.4535%" height="15" fill="rgb(232,164,16)" fg:x="253" fg:w="2"/><text x="57.6196%" y="238.50"></text></g><g><title>get_balance (gacha\services\economy_service.py:48) (2 samples, 0.45%)</title><rect x="57.3696%" y="244" width="0.4535%" height="15" fill="rgb(244,205,15)" fg:x="253" fg:w="2"/><text x="57.6196%" y="254.50"></text></g><g><title>get_balance (gacha\services\user_service.py:90) (2 samples, 0.45%)</title><rect x="57.3696%" y="260" width="0.4535%" height="15" fill="rgb(223,117,47)" fg:x="253" fg:w="2"/><text x="57.6196%" y="270.50"></text></g><g><title>get_user (gacha\services\user_service.py:46) (2 samples, 0.45%)</title><rect x="57.3696%" y="276" width="0.4535%" height="15" fill="rgb(244,107,35)" fg:x="253" fg:w="2"/><text x="57.6196%" y="286.50"></text></g><g><title>get_user_optional (gacha\repositories\user\user_repository.py:55) (2 samples, 0.45%)</title><rect x="57.3696%" y="292" width="0.4535%" height="15" fill="rgb(205,140,8)" fg:x="253" fg:w="2"/><text x="57.6196%" y="302.50"></text></g><g><title>get_user (gacha\repositories\user\user_repository.py:36) (1 samples, 0.23%)</title><rect x="57.5964%" y="308" width="0.2268%" height="15" fill="rgb(228,84,46)" fg:x="254" fg:w="1"/><text x="57.8464%" y="318.50"></text></g><g><title>__init__ (&lt;string&gt;:2) (1 samples, 0.23%)</title><rect x="57.5964%" y="324" width="0.2268%" height="15" fill="rgb(254,188,9)" fg:x="254" fg:w="1"/><text x="57.8464%" y="334.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:156) (1 samples, 0.23%)</title><rect x="57.8231%" y="324" width="0.2268%" height="15" fill="rgb(206,112,54)" fg:x="255" fg:w="1"/><text x="58.0731%" y="334.50"></text></g><g><title>__aenter__ (asyncpg\pool.py:1024) (1 samples, 0.23%)</title><rect x="57.8231%" y="340" width="0.2268%" height="15" fill="rgb(216,84,49)" fg:x="255" fg:w="1"/><text x="58.0731%" y="350.50"></text></g><g><title>_acquire (asyncpg\pool.py:864) (1 samples, 0.23%)</title><rect x="57.8231%" y="356" width="0.2268%" height="15" fill="rgb(214,194,35)" fg:x="255" fg:w="1"/><text x="58.0731%" y="366.50"></text></g><g><title>_acquire_impl (asyncpg\pool.py:849) (1 samples, 0.23%)</title><rect x="57.8231%" y="372" width="0.2268%" height="15" fill="rgb(249,28,3)" fg:x="255" fg:w="1"/><text x="58.0731%" y="382.50"></text></g><g><title>acquire (asyncpg\pool.py:170) (1 samples, 0.23%)</title><rect x="57.8231%" y="388" width="0.2268%" height="15" fill="rgb(222,56,52)" fg:x="255" fg:w="1"/><text x="58.0731%" y="398.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="57.8231%" y="404" width="0.2268%" height="15" fill="rgb(245,217,50)" fg:x="255" fg:w="1"/><text x="58.0731%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="57.8231%" y="420" width="0.2268%" height="15" fill="rgb(213,201,24)" fg:x="255" fg:w="1"/><text x="58.0731%" y="430.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="57.8231%" y="436" width="0.2268%" height="15" fill="rgb(248,116,28)" fg:x="255" fg:w="1"/><text x="58.0731%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="57.8231%" y="452" width="0.2268%" height="15" fill="rgb(219,72,43)" fg:x="255" fg:w="1"/><text x="58.0731%" y="462.50"></text></g><g><title>line (traceback.py:373) (1 samples, 0.23%)</title><rect x="57.8231%" y="468" width="0.2268%" height="15" fill="rgb(209,138,14)" fg:x="255" fg:w="1"/><text x="58.0731%" y="478.50"></text></g><g><title>_set_lines (traceback.py:351) (1 samples, 0.23%)</title><rect x="57.8231%" y="484" width="0.2268%" height="15" fill="rgb(222,18,33)" fg:x="255" fg:w="1"/><text x="58.0731%" y="494.50"></text></g><g><title>getline (linecache.py:26) (1 samples, 0.23%)</title><rect x="57.8231%" y="500" width="0.2268%" height="15" fill="rgb(213,199,7)" fg:x="255" fg:w="1"/><text x="58.0731%" y="510.50"></text></g><g><title>getlines (linecache.py:36) (1 samples, 0.23%)</title><rect x="57.8231%" y="516" width="0.2268%" height="15" fill="rgb(250,110,10)" fg:x="255" fg:w="1"/><text x="58.0731%" y="526.50"></text></g><g><title>_handle_game_action (blackjack_cog.py:805) (2 samples, 0.45%)</title><rect x="57.8231%" y="228" width="0.4535%" height="15" fill="rgb(248,123,6)" fg:x="255" fg:w="2"/><text x="58.0731%" y="238.50"></text></g><g><title>create_game_embed (blackjack_cog.py:635) (2 samples, 0.45%)</title><rect x="57.8231%" y="244" width="0.4535%" height="15" fill="rgb(206,91,31)" fg:x="255" fg:w="2"/><text x="58.0731%" y="254.50"></text></g><g><title>_get_user_stats (blackjack_cog.py:647) (2 samples, 0.45%)</title><rect x="57.8231%" y="260" width="0.4535%" height="15" fill="rgb(211,154,13)" fg:x="255" fg:w="2"/><text x="58.0731%" y="270.50"></text></g><g><title>get_user_game_stats (gacha\services\game_stats_service.py:58) (2 samples, 0.45%)</title><rect x="57.8231%" y="276" width="0.4535%" height="15" fill="rgb(225,148,7)" fg:x="255" fg:w="2"/><text x="58.0731%" y="286.50"></text></g><g><title>_get_single_game_stats (gacha\services\game_stats_service.py:94) (2 samples, 0.45%)</title><rect x="57.8231%" y="292" width="0.4535%" height="15" fill="rgb(220,160,43)" fg:x="255" fg:w="2"/><text x="58.0731%" y="302.50"></text></g><g><title>get_user_stats_for_single_game (game_stats_repository.py:209) (2 samples, 0.45%)</title><rect x="57.8231%" y="308" width="0.4535%" height="15" fill="rgb(213,52,39)" fg:x="255" fg:w="2"/><text x="58.0731%" y="318.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:157) (1 samples, 0.23%)</title><rect x="58.0499%" y="324" width="0.2268%" height="15" fill="rgb(243,137,7)" fg:x="256" fg:w="1"/><text x="58.2999%" y="334.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (1 samples, 0.23%)</title><rect x="58.0499%" y="340" width="0.2268%" height="15" fill="rgb(230,79,13)" fg:x="256" fg:w="1"/><text x="58.2999%" y="350.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="58.0499%" y="356" width="0.2268%" height="15" fill="rgb(247,105,23)" fg:x="256" fg:w="1"/><text x="58.2999%" y="366.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="58.0499%" y="372" width="0.2268%" height="15" fill="rgb(223,179,41)" fg:x="256" fg:w="1"/><text x="58.2999%" y="382.50"></text></g><g><title>_handle_game_action (blackjack_cog.py:810) (1 samples, 0.23%)</title><rect x="58.2766%" y="228" width="0.2268%" height="15" fill="rgb(218,9,34)" fg:x="257" fg:w="1"/><text x="58.5266%" y="238.50"></text></g><g><title>callback (blackjack_cog.py:367) (6 samples, 1.36%)</title><rect x="57.3696%" y="212" width="1.3605%" height="15" fill="rgb(222,106,8)" fg:x="253" fg:w="6"/><text x="57.6196%" y="222.50"></text></g><g><title>_handle_game_action (blackjack_cog.py:817) (1 samples, 0.23%)</title><rect x="58.5034%" y="228" width="0.2268%" height="15" fill="rgb(211,220,0)" fg:x="258" fg:w="1"/><text x="58.7534%" y="238.50"></text></g><g><title>info (logging\__init__.py:1522) (1 samples, 0.23%)</title><rect x="58.5034%" y="244" width="0.2268%" height="15" fill="rgb(229,52,16)" fg:x="258" fg:w="1"/><text x="58.7534%" y="254.50"></text></g><g><title>_log (logging\__init__.py:1667) (1 samples, 0.23%)</title><rect x="58.5034%" y="260" width="0.2268%" height="15" fill="rgb(212,155,18)" fg:x="258" fg:w="1"/><text x="58.7534%" y="270.50"></text></g><g><title>handle (logging\__init__.py:1686) (1 samples, 0.23%)</title><rect x="58.5034%" y="276" width="0.2268%" height="15" fill="rgb(242,21,14)" fg:x="258" fg:w="1"/><text x="58.7534%" y="286.50"></text></g><g><title>callHandlers (logging\__init__.py:1744) (1 samples, 0.23%)</title><rect x="58.5034%" y="292" width="0.2268%" height="15" fill="rgb(222,19,48)" fg:x="258" fg:w="1"/><text x="58.7534%" y="302.50"></text></g><g><title>handle (logging\__init__.py:1026) (1 samples, 0.23%)</title><rect x="58.5034%" y="308" width="0.2268%" height="15" fill="rgb(232,45,27)" fg:x="258" fg:w="1"/><text x="58.7534%" y="318.50"></text></g><g><title>callback (blackjack_cog.py:391) (1 samples, 0.23%)</title><rect x="58.7302%" y="212" width="0.2268%" height="15" fill="rgb(249,103,42)" fg:x="259" fg:w="1"/><text x="58.9802%" y="222.50"></text></g><g><title>defer (discord\interactions.py:844) (1 samples, 0.23%)</title><rect x="58.7302%" y="228" width="0.2268%" height="15" fill="rgb(246,81,33)" fg:x="259" fg:w="1"/><text x="58.9802%" y="238.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="58.7302%" y="244" width="0.2268%" height="15" fill="rgb(252,33,42)" fg:x="259" fg:w="1"/><text x="58.9802%" y="254.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="58.7302%" y="260" width="0.2268%" height="15" fill="rgb(209,212,41)" fg:x="259" fg:w="1"/><text x="58.9802%" y="270.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="58.7302%" y="276" width="0.2268%" height="15" fill="rgb(207,154,6)" fg:x="259" fg:w="1"/><text x="58.9802%" y="286.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="58.7302%" y="292" width="0.2268%" height="15" fill="rgb(223,64,47)" fg:x="259" fg:w="1"/><text x="58.9802%" y="302.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1476) (1 samples, 0.23%)</title><rect x="58.7302%" y="308" width="0.2268%" height="15" fill="rgb(211,161,38)" fg:x="259" fg:w="1"/><text x="58.9802%" y="318.50"></text></g><g><title>write_bytes (aiohttp\client_reqrep.py:1374) (1 samples, 0.23%)</title><rect x="58.7302%" y="324" width="0.2268%" height="15" fill="rgb(219,138,40)" fg:x="259" fg:w="1"/><text x="58.9802%" y="334.50"></text></g><g><title>write_with_length (aiohttp\payload.py:425) (1 samples, 0.23%)</title><rect x="58.7302%" y="340" width="0.2268%" height="15" fill="rgb(241,228,46)" fg:x="259" fg:w="1"/><text x="58.9802%" y="350.50"></text></g><g><title>write (aiohttp\http_writer.py:194) (1 samples, 0.23%)</title><rect x="58.7302%" y="356" width="0.2268%" height="15" fill="rgb(223,209,38)" fg:x="259" fg:w="1"/><text x="58.9802%" y="366.50"></text></g><g><title>_send_headers_with_payload (aiohttp\http_writer.py:138) (1 samples, 0.23%)</title><rect x="58.7302%" y="372" width="0.2268%" height="15" fill="rgb(236,164,45)" fg:x="259" fg:w="1"/><text x="58.9802%" y="382.50"></text></g><g><title>_writelines (aiohttp\http_writer.py:108) (1 samples, 0.23%)</title><rect x="58.7302%" y="388" width="0.2268%" height="15" fill="rgb(231,15,5)" fg:x="259" fg:w="1"/><text x="58.9802%" y="398.50"></text></g><g><title>write (asyncio\sslproto.py:222) (1 samples, 0.23%)</title><rect x="58.7302%" y="404" width="0.2268%" height="15" fill="rgb(252,35,15)" fg:x="259" fg:w="1"/><text x="58.9802%" y="414.50"></text></g><g><title>_write_appdata (asyncio\sslproto.py:697) (1 samples, 0.23%)</title><rect x="58.7302%" y="420" width="0.2268%" height="15" fill="rgb(248,181,18)" fg:x="259" fg:w="1"/><text x="58.9802%" y="430.50"></text></g><g><title>_do_write (asyncio\sslproto.py:716) (1 samples, 0.23%)</title><rect x="58.7302%" y="436" width="0.2268%" height="15" fill="rgb(233,39,42)" fg:x="259" fg:w="1"/><text x="58.9802%" y="446.50"></text></g><g><title>_process_outgoing (asyncio\sslproto.py:722) (1 samples, 0.23%)</title><rect x="58.7302%" y="452" width="0.2268%" height="15" fill="rgb(238,110,33)" fg:x="259" fg:w="1"/><text x="58.9802%" y="462.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="58.7302%" y="468" width="0.2268%" height="15" fill="rgb(233,195,10)" fg:x="259" fg:w="1"/><text x="58.9802%" y="478.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (1 samples, 0.23%)</title><rect x="58.7302%" y="484" width="0.2268%" height="15" fill="rgb(254,105,3)" fg:x="259" fg:w="1"/><text x="58.9802%" y="494.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="58.7302%" y="500" width="0.2268%" height="15" fill="rgb(221,225,9)" fg:x="259" fg:w="1"/><text x="58.9802%" y="510.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="58.7302%" y="516" width="0.2268%" height="15" fill="rgb(224,227,45)" fg:x="259" fg:w="1"/><text x="58.9802%" y="526.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="58.7302%" y="532" width="0.2268%" height="15" fill="rgb(229,198,43)" fg:x="259" fg:w="1"/><text x="58.9802%" y="542.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="58.7302%" y="548" width="0.2268%" height="15" fill="rgb(206,209,35)" fg:x="259" fg:w="1"/><text x="58.9802%" y="558.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="58.7302%" y="564" width="0.2268%" height="15" fill="rgb(245,195,53)" fg:x="259" fg:w="1"/><text x="58.9802%" y="574.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="58.7302%" y="580" width="0.2268%" height="15" fill="rgb(240,92,26)" fg:x="259" fg:w="1"/><text x="58.9802%" y="590.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="58.7302%" y="596" width="0.2268%" height="15" fill="rgb(207,40,23)" fg:x="259" fg:w="1"/><text x="58.9802%" y="606.50"></text></g><g><title>_start_new_game (blackjack_cog.py:760) (2 samples, 0.45%)</title><rect x="58.9569%" y="228" width="0.4535%" height="15" fill="rgb(223,111,35)" fg:x="260" fg:w="2"/><text x="59.2069%" y="238.50"></text></g><g><title>__init__ (__init__) (2 samples, 0.45%)</title><rect x="58.9569%" y="244" width="0.4535%" height="15" fill="rgb(229,147,28)" fg:x="260" fg:w="2"/><text x="59.2069%" y="254.50"></text></g><g><title>__init__ (blackjack_cog.py:405) (2 samples, 0.45%)</title><rect x="58.9569%" y="260" width="0.4535%" height="15" fill="rgb(211,29,28)" fg:x="260" fg:w="2"/><text x="59.2069%" y="270.50"></text></g><g><title>__init__ (base_view.py:14) (2 samples, 0.45%)</title><rect x="58.9569%" y="276" width="0.4535%" height="15" fill="rgb(228,72,33)" fg:x="260" fg:w="2"/><text x="59.2069%" y="286.50"></text></g><g><title>__init__ (discord\ui\view.py:195) (2 samples, 0.45%)</title><rect x="58.9569%" y="292" width="0.4535%" height="15" fill="rgb(205,214,31)" fg:x="260" fg:w="2"/><text x="59.2069%" y="302.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (2 samples, 0.45%)</title><rect x="58.9569%" y="308" width="0.4535%" height="15" fill="rgb(224,111,15)" fg:x="260" fg:w="2"/><text x="59.2069%" y="318.50"></text></g><g><title>extract_stack (traceback.py:260) (2 samples, 0.45%)</title><rect x="58.9569%" y="324" width="0.4535%" height="15" fill="rgb(253,21,26)" fg:x="260" fg:w="2"/><text x="59.2069%" y="334.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="58.9569%" y="340" width="0.4535%" height="15" fill="rgb(245,139,43)" fg:x="260" fg:w="2"/><text x="59.2069%" y="350.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="58.9569%" y="356" width="0.4535%" height="15" fill="rgb(252,170,7)" fg:x="260" fg:w="2"/><text x="59.2069%" y="366.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="58.9569%" y="372" width="0.4535%" height="15" fill="rgb(231,118,14)" fg:x="260" fg:w="2"/><text x="59.2069%" y="382.50"></text></g><g><title>_start_new_game (blackjack_cog.py:761) (1 samples, 0.23%)</title><rect x="59.4104%" y="228" width="0.2268%" height="15" fill="rgb(238,83,0)" fg:x="262" fg:w="1"/><text x="59.6604%" y="238.50"></text></g><g><title>create_game_embed (blackjack_cog.py:635) (1 samples, 0.23%)</title><rect x="59.4104%" y="244" width="0.2268%" height="15" fill="rgb(221,39,39)" fg:x="262" fg:w="1"/><text x="59.6604%" y="254.50"></text></g><g><title>_get_user_stats (blackjack_cog.py:647) (1 samples, 0.23%)</title><rect x="59.4104%" y="260" width="0.2268%" height="15" fill="rgb(222,119,46)" fg:x="262" fg:w="1"/><text x="59.6604%" y="270.50"></text></g><g><title>get_user_game_stats (gacha\services\game_stats_service.py:58) (1 samples, 0.23%)</title><rect x="59.4104%" y="276" width="0.2268%" height="15" fill="rgb(222,165,49)" fg:x="262" fg:w="1"/><text x="59.6604%" y="286.50"></text></g><g><title>_get_single_game_stats (gacha\services\game_stats_service.py:94) (1 samples, 0.23%)</title><rect x="59.4104%" y="292" width="0.2268%" height="15" fill="rgb(219,113,52)" fg:x="262" fg:w="1"/><text x="59.6604%" y="302.50"></text></g><g><title>get_user_stats_for_single_game (game_stats_repository.py:209) (1 samples, 0.23%)</title><rect x="59.4104%" y="308" width="0.2268%" height="15" fill="rgb(214,7,15)" fg:x="262" fg:w="1"/><text x="59.6604%" y="318.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:156) (1 samples, 0.23%)</title><rect x="59.4104%" y="324" width="0.2268%" height="15" fill="rgb(235,32,4)" fg:x="262" fg:w="1"/><text x="59.6604%" y="334.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="59.4104%" y="340" width="0.2268%" height="15" fill="rgb(238,90,54)" fg:x="262" fg:w="1"/><text x="59.6604%" y="350.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="59.4104%" y="356" width="0.2268%" height="15" fill="rgb(213,208,19)" fg:x="262" fg:w="1"/><text x="59.6604%" y="366.50"></text></g><g><title>shield (asyncio\tasks.py:951) (1 samples, 0.23%)</title><rect x="59.4104%" y="372" width="0.2268%" height="15" fill="rgb(233,156,4)" fg:x="262" fg:w="1"/><text x="59.6604%" y="382.50"></text></g><g><title>ensure_future (asyncio\tasks.py:748) (1 samples, 0.23%)</title><rect x="59.4104%" y="388" width="0.2268%" height="15" fill="rgb(207,194,5)" fg:x="262" fg:w="1"/><text x="59.6604%" y="398.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="59.4104%" y="404" width="0.2268%" height="15" fill="rgb(206,111,30)" fg:x="262" fg:w="1"/><text x="59.6604%" y="414.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="59.4104%" y="420" width="0.2268%" height="15" fill="rgb(243,70,54)" fg:x="262" fg:w="1"/><text x="59.6604%" y="430.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="59.4104%" y="436" width="0.2268%" height="15" fill="rgb(242,28,8)" fg:x="262" fg:w="1"/><text x="59.6604%" y="446.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="59.4104%" y="452" width="0.2268%" height="15" fill="rgb(219,106,18)" fg:x="262" fg:w="1"/><text x="59.6604%" y="462.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="59.4104%" y="468" width="0.2268%" height="15" fill="rgb(244,222,10)" fg:x="262" fg:w="1"/><text x="59.6604%" y="478.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="59.4104%" y="484" width="0.2268%" height="15" fill="rgb(236,179,52)" fg:x="262" fg:w="1"/><text x="59.6604%" y="494.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="59.4104%" y="500" width="0.2268%" height="15" fill="rgb(213,23,39)" fg:x="262" fg:w="1"/><text x="59.6604%" y="510.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="59.4104%" y="516" width="0.2268%" height="15" fill="rgb(238,48,10)" fg:x="262" fg:w="1"/><text x="59.6604%" y="526.50"></text></g><g><title>request (discord\webhook\async_.py:170) (1 samples, 0.23%)</title><rect x="59.6372%" y="260" width="0.2268%" height="15" fill="rgb(251,196,23)" fg:x="263" fg:w="1"/><text x="59.8872%" y="270.50"></text></g><g><title>__aenter__ (discord\webhook\async_.py:109) (1 samples, 0.23%)</title><rect x="59.6372%" y="276" width="0.2268%" height="15" fill="rgb(250,152,24)" fg:x="263" fg:w="1"/><text x="59.8872%" y="286.50"></text></g><g><title>acquire (asyncio\locks.py:99) (1 samples, 0.23%)</title><rect x="59.6372%" y="292" width="0.2268%" height="15" fill="rgb(209,150,17)" fg:x="263" fg:w="1"/><text x="59.8872%" y="302.50"></text></g><g><title>callback (blackjack_cog.py:392) (5 samples, 1.13%)</title><rect x="58.9569%" y="212" width="1.1338%" height="15" fill="rgb(234,202,34)" fg:x="260" fg:w="5"/><text x="59.2069%" y="222.50"></text></g><g><title>_start_new_game (blackjack_cog.py:766) (2 samples, 0.45%)</title><rect x="59.6372%" y="228" width="0.4535%" height="15" fill="rgb(253,148,53)" fg:x="263" fg:w="2"/><text x="59.8872%" y="238.50"></text></g><g><title>send (discord\webhook\async_.py:1857) (2 samples, 0.45%)</title><rect x="59.6372%" y="244" width="0.4535%" height="15" fill="rgb(218,129,16)" fg:x="263" fg:w="2"/><text x="59.8872%" y="254.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="59.8639%" y="260" width="0.2268%" height="15" fill="rgb(216,85,19)" fg:x="264" fg:w="1"/><text x="60.1139%" y="270.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="59.8639%" y="276" width="0.2268%" height="15" fill="rgb(235,228,7)" fg:x="264" fg:w="1"/><text x="60.1139%" y="286.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="59.8639%" y="292" width="0.2268%" height="15" fill="rgb(245,175,0)" fg:x="264" fg:w="1"/><text x="60.1139%" y="302.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="59.8639%" y="308" width="0.2268%" height="15" fill="rgb(208,168,36)" fg:x="264" fg:w="1"/><text x="60.1139%" y="318.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1476) (1 samples, 0.23%)</title><rect x="59.8639%" y="324" width="0.2268%" height="15" fill="rgb(246,171,24)" fg:x="264" fg:w="1"/><text x="60.1139%" y="334.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="59.8639%" y="340" width="0.2268%" height="15" fill="rgb(215,142,24)" fg:x="264" fg:w="1"/><text x="60.1139%" y="350.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="59.8639%" y="356" width="0.2268%" height="15" fill="rgb(250,187,7)" fg:x="264" fg:w="1"/><text x="60.1139%" y="366.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="59.8639%" y="372" width="0.2268%" height="15" fill="rgb(228,66,33)" fg:x="264" fg:w="1"/><text x="60.1139%" y="382.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="59.8639%" y="388" width="0.2268%" height="15" fill="rgb(234,215,21)" fg:x="264" fg:w="1"/><text x="60.1139%" y="398.50"></text></g><g><title>_confirm_batch_favorite_action (gacha\views\collection\collection_view\card_view.py:500) (1 samples, 0.23%)</title><rect x="60.0907%" y="244" width="0.2268%" height="15" fill="rgb(222,191,20)" fg:x="265" fg:w="1"/><text x="60.3407%" y="254.50"></text></g><g><title>defer (discord\interactions.py:844) (1 samples, 0.23%)</title><rect x="60.0907%" y="260" width="0.2268%" height="15" fill="rgb(245,79,54)" fg:x="265" fg:w="1"/><text x="60.3407%" y="270.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="60.0907%" y="276" width="0.2268%" height="15" fill="rgb(240,10,37)" fg:x="265" fg:w="1"/><text x="60.3407%" y="286.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="60.0907%" y="292" width="0.2268%" height="15" fill="rgb(214,192,32)" fg:x="265" fg:w="1"/><text x="60.3407%" y="302.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="60.0907%" y="308" width="0.2268%" height="15" fill="rgb(209,36,54)" fg:x="265" fg:w="1"/><text x="60.3407%" y="318.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="60.0907%" y="324" width="0.2268%" height="15" fill="rgb(220,10,11)" fg:x="265" fg:w="1"/><text x="60.3407%" y="334.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1493) (1 samples, 0.23%)</title><rect x="60.0907%" y="340" width="0.2268%" height="15" fill="rgb(221,106,17)" fg:x="265" fg:w="1"/><text x="60.3407%" y="350.50"></text></g><g><title>__init__ (aiohttp\client_reqrep.py:349) (1 samples, 0.23%)</title><rect x="60.0907%" y="356" width="0.2268%" height="15" fill="rgb(251,142,44)" fg:x="265" fg:w="1"/><text x="60.3407%" y="366.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="60.0907%" y="372" width="0.2268%" height="15" fill="rgb(238,13,15)" fg:x="265" fg:w="1"/><text x="60.3407%" y="382.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="60.0907%" y="388" width="0.2268%" height="15" fill="rgb(208,107,27)" fg:x="265" fg:w="1"/><text x="60.3407%" y="398.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="60.0907%" y="404" width="0.2268%" height="15" fill="rgb(205,136,37)" fg:x="265" fg:w="1"/><text x="60.3407%" y="414.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="60.0907%" y="420" width="0.2268%" height="15" fill="rgb(250,205,27)" fg:x="265" fg:w="1"/><text x="60.3407%" y="430.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (2 samples, 0.45%)</title><rect x="60.3175%" y="372" width="0.4535%" height="15" fill="rgb(210,80,43)" fg:x="266" fg:w="2"/><text x="60.5675%" y="382.50"></text></g><g><title>send (asyncio\windows_events.py:547) (2 samples, 0.45%)</title><rect x="60.3175%" y="388" width="0.4535%" height="15" fill="rgb(247,160,36)" fg:x="266" fg:w="2"/><text x="60.5675%" y="398.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (2 samples, 0.45%)</title><rect x="60.3175%" y="404" width="0.4535%" height="15" fill="rgb(234,13,49)" fg:x="266" fg:w="2"/><text x="60.5675%" y="414.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="60.5442%" y="420" width="0.2268%" height="15" fill="rgb(234,122,0)" fg:x="267" fg:w="1"/><text x="60.7942%" y="430.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="60.5442%" y="436" width="0.2268%" height="15" fill="rgb(207,146,38)" fg:x="267" fg:w="1"/><text x="60.7942%" y="446.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="60.5442%" y="452" width="0.2268%" height="15" fill="rgb(207,177,25)" fg:x="267" fg:w="1"/><text x="60.7942%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="60.5442%" y="468" width="0.2268%" height="15" fill="rgb(211,178,42)" fg:x="267" fg:w="1"/><text x="60.7942%" y="478.50"></text></g><g><title>line (traceback.py:373) (1 samples, 0.23%)</title><rect x="60.5442%" y="484" width="0.2268%" height="15" fill="rgb(230,69,54)" fg:x="267" fg:w="1"/><text x="60.7942%" y="494.50"></text></g><g><title>_set_lines (traceback.py:354) (1 samples, 0.23%)</title><rect x="60.5442%" y="500" width="0.2268%" height="15" fill="rgb(214,135,41)" fg:x="267" fg:w="1"/><text x="60.7942%" y="510.50"></text></g><g><title>call_soon (asyncio\base_events.py:836) (1 samples, 0.23%)</title><rect x="60.7710%" y="388" width="0.2268%" height="15" fill="rgb(237,67,25)" fg:x="268" fg:w="1"/><text x="61.0210%" y="398.50"></text></g><g><title>_check_callback (asyncio\base_events.py:844) (1 samples, 0.23%)</title><rect x="60.7710%" y="404" width="0.2268%" height="15" fill="rgb(222,189,50)" fg:x="268" fg:w="1"/><text x="61.0210%" y="414.50"></text></g><g><title>iscoroutinefunction (asyncio\coroutines.py:22) (1 samples, 0.23%)</title><rect x="60.7710%" y="420" width="0.2268%" height="15" fill="rgb(245,148,34)" fg:x="268" fg:w="1"/><text x="61.0210%" y="430.50"></text></g><g><title>iscoroutinefunction (inspect.py:443) (1 samples, 0.23%)</title><rect x="60.7710%" y="436" width="0.2268%" height="15" fill="rgb(222,29,6)" fg:x="268" fg:w="1"/><text x="61.0210%" y="446.50"></text></g><g><title>_has_code_flag (inspect.py:404) (1 samples, 0.23%)</title><rect x="60.7710%" y="452" width="0.2268%" height="15" fill="rgb(221,189,43)" fg:x="268" fg:w="1"/><text x="61.0210%" y="462.50"></text></g><g><title>_unwrap_partialmethod (functools.py:440) (1 samples, 0.23%)</title><rect x="60.7710%" y="468" width="0.2268%" height="15" fill="rgb(207,36,27)" fg:x="268" fg:w="1"/><text x="61.0210%" y="478.50"></text></g><g><title>checkcache (linecache.py:90) (1 samples, 0.23%)</title><rect x="60.9977%" y="484" width="0.2268%" height="15" fill="rgb(217,90,24)" fg:x="269" fg:w="1"/><text x="61.2477%" y="494.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:176) (5 samples, 1.13%)</title><rect x="60.3175%" y="292" width="1.1338%" height="15" fill="rgb(224,66,35)" fg:x="266" fg:w="5"/><text x="60.5675%" y="302.50"></text></g><g><title>__aexit__ (asyncpg\transaction.py:91) (5 samples, 1.13%)</title><rect x="60.3175%" y="308" width="1.1338%" height="15" fill="rgb(221,13,50)" fg:x="266" fg:w="5"/><text x="60.5675%" y="318.50"></text></g><g><title>__commit (asyncpg\transaction.py:187) (5 samples, 1.13%)</title><rect x="60.3175%" y="324" width="1.1338%" height="15" fill="rgb(236,68,49)" fg:x="266" fg:w="5"/><text x="60.5675%" y="334.50"></text></g><g><title>execute (asyncpg\connection.py:349) (5 samples, 1.13%)</title><rect x="60.3175%" y="340" width="1.1338%" height="15" fill="rgb(229,146,28)" fg:x="266" fg:w="5"/><text x="60.5675%" y="350.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (5 samples, 1.13%)</title><rect x="60.3175%" y="356" width="1.1338%" height="15" fill="rgb(225,31,38)" fg:x="266" fg:w="5"/><text x="60.5675%" y="366.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (3 samples, 0.68%)</title><rect x="60.7710%" y="372" width="0.6803%" height="15" fill="rgb(250,208,3)" fg:x="268" fg:w="3"/><text x="61.0210%" y="382.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (2 samples, 0.45%)</title><rect x="60.9977%" y="388" width="0.4535%" height="15" fill="rgb(246,54,23)" fg:x="269" fg:w="2"/><text x="61.2477%" y="398.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (2 samples, 0.45%)</title><rect x="60.9977%" y="404" width="0.4535%" height="15" fill="rgb(243,76,11)" fg:x="269" fg:w="2"/><text x="61.2477%" y="414.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="60.9977%" y="420" width="0.4535%" height="15" fill="rgb(245,21,50)" fg:x="269" fg:w="2"/><text x="61.2477%" y="430.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="60.9977%" y="436" width="0.4535%" height="15" fill="rgb(228,9,43)" fg:x="269" fg:w="2"/><text x="61.2477%" y="446.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="60.9977%" y="452" width="0.4535%" height="15" fill="rgb(208,100,47)" fg:x="269" fg:w="2"/><text x="61.2477%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="60.9977%" y="468" width="0.4535%" height="15" fill="rgb(232,26,8)" fg:x="269" fg:w="2"/><text x="61.2477%" y="478.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="61.2245%" y="484" width="0.2268%" height="15" fill="rgb(216,166,38)" fg:x="270" fg:w="1"/><text x="61.4745%" y="494.50"></text></g><g><title>__instancecheck__ (&lt;frozen abc&gt;:117) (1 samples, 0.23%)</title><rect x="61.4512%" y="404" width="0.2268%" height="15" fill="rgb(251,202,51)" fg:x="271" fg:w="1"/><text x="61.7012%" y="414.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:178) (2 samples, 0.45%)</title><rect x="61.4512%" y="292" width="0.4535%" height="15" fill="rgb(254,216,34)" fg:x="271" fg:w="2"/><text x="61.7012%" y="302.50"></text></g><g><title>get_owned_card_ids_in_batch (gacha\repositories\collection\user_collection_repository.py:2079) (2 samples, 0.45%)</title><rect x="61.4512%" y="308" width="0.4535%" height="15" fill="rgb(251,32,27)" fg:x="271" fg:w="2"/><text x="61.7012%" y="318.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:107) (2 samples, 0.45%)</title><rect x="61.4512%" y="324" width="0.4535%" height="15" fill="rgb(208,127,28)" fg:x="271" fg:w="2"/><text x="61.7012%" y="334.50"></text></g><g><title>fetch (asyncpg\connection.py:690) (2 samples, 0.45%)</title><rect x="61.4512%" y="340" width="0.4535%" height="15" fill="rgb(224,137,22)" fg:x="271" fg:w="2"/><text x="61.7012%" y="350.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (2 samples, 0.45%)</title><rect x="61.4512%" y="356" width="0.4535%" height="15" fill="rgb(254,70,32)" fg:x="271" fg:w="2"/><text x="61.7012%" y="366.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (2 samples, 0.45%)</title><rect x="61.4512%" y="372" width="0.4535%" height="15" fill="rgb(229,75,37)" fg:x="271" fg:w="2"/><text x="61.7012%" y="382.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (2 samples, 0.45%)</title><rect x="61.4512%" y="388" width="0.4535%" height="15" fill="rgb(252,64,23)" fg:x="271" fg:w="2"/><text x="61.7012%" y="398.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="61.6780%" y="404" width="0.2268%" height="15" fill="rgb(232,162,48)" fg:x="272" fg:w="1"/><text x="61.9280%" y="414.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="61.6780%" y="420" width="0.2268%" height="15" fill="rgb(246,160,12)" fg:x="272" fg:w="1"/><text x="61.9280%" y="430.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="61.6780%" y="436" width="0.2268%" height="15" fill="rgb(247,166,0)" fg:x="272" fg:w="1"/><text x="61.9280%" y="446.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="61.6780%" y="452" width="0.2268%" height="15" fill="rgb(249,219,21)" fg:x="272" fg:w="1"/><text x="61.9280%" y="462.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="61.6780%" y="468" width="0.2268%" height="15" fill="rgb(205,209,3)" fg:x="272" fg:w="1"/><text x="61.9280%" y="478.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="61.6780%" y="484" width="0.2268%" height="15" fill="rgb(243,44,1)" fg:x="272" fg:w="1"/><text x="61.9280%" y="494.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="61.6780%" y="500" width="0.2268%" height="15" fill="rgb(206,159,16)" fg:x="272" fg:w="1"/><text x="61.9280%" y="510.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="61.6780%" y="516" width="0.2268%" height="15" fill="rgb(244,77,30)" fg:x="272" fg:w="1"/><text x="61.9280%" y="526.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="61.6780%" y="532" width="0.2268%" height="15" fill="rgb(218,69,12)" fg:x="272" fg:w="1"/><text x="61.9280%" y="542.50"></text></g><g><title>__instancecheck__ (&lt;frozen abc&gt;:119) (1 samples, 0.23%)</title><rect x="62.1315%" y="404" width="0.2268%" height="15" fill="rgb(212,87,7)" fg:x="274" fg:w="1"/><text x="62.3815%" y="414.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:187) (3 samples, 0.68%)</title><rect x="61.9048%" y="292" width="0.6803%" height="15" fill="rgb(245,114,25)" fg:x="273" fg:w="3"/><text x="62.1548%" y="302.50"></text></g><g><title>get_favorite_card_ids_in_batch (gacha\repositories\collection\user_collection_repository.py:2090) (3 samples, 0.68%)</title><rect x="61.9048%" y="308" width="0.6803%" height="15" fill="rgb(210,61,42)" fg:x="273" fg:w="3"/><text x="62.1548%" y="318.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:107) (3 samples, 0.68%)</title><rect x="61.9048%" y="324" width="0.6803%" height="15" fill="rgb(211,52,33)" fg:x="273" fg:w="3"/><text x="62.1548%" y="334.50"></text></g><g><title>fetch (asyncpg\connection.py:690) (3 samples, 0.68%)</title><rect x="61.9048%" y="340" width="0.6803%" height="15" fill="rgb(234,58,33)" fg:x="273" fg:w="3"/><text x="62.1548%" y="350.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (3 samples, 0.68%)</title><rect x="61.9048%" y="356" width="0.6803%" height="15" fill="rgb(220,115,36)" fg:x="273" fg:w="3"/><text x="62.1548%" y="366.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (3 samples, 0.68%)</title><rect x="61.9048%" y="372" width="0.6803%" height="15" fill="rgb(243,153,54)" fg:x="273" fg:w="3"/><text x="62.1548%" y="382.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (3 samples, 0.68%)</title><rect x="61.9048%" y="388" width="0.6803%" height="15" fill="rgb(251,47,18)" fg:x="273" fg:w="3"/><text x="62.1548%" y="398.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="62.3583%" y="404" width="0.2268%" height="15" fill="rgb(242,102,42)" fg:x="275" fg:w="1"/><text x="62.6083%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="62.3583%" y="420" width="0.2268%" height="15" fill="rgb(234,31,38)" fg:x="275" fg:w="1"/><text x="62.6083%" y="430.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="62.3583%" y="436" width="0.2268%" height="15" fill="rgb(221,117,51)" fg:x="275" fg:w="1"/><text x="62.6083%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="62.3583%" y="452" width="0.2268%" height="15" fill="rgb(212,20,18)" fg:x="275" fg:w="1"/><text x="62.6083%" y="462.50"></text></g><g><title>line (traceback.py:373) (1 samples, 0.23%)</title><rect x="62.3583%" y="468" width="0.2268%" height="15" fill="rgb(245,133,36)" fg:x="275" fg:w="1"/><text x="62.6083%" y="478.50"></text></g><g><title>_set_lines (traceback.py:351) (1 samples, 0.23%)</title><rect x="62.3583%" y="484" width="0.2268%" height="15" fill="rgb(212,6,19)" fg:x="275" fg:w="1"/><text x="62.6083%" y="494.50"></text></g><g><title>getline (linecache.py:26) (1 samples, 0.23%)</title><rect x="62.3583%" y="500" width="0.2268%" height="15" fill="rgb(218,1,36)" fg:x="275" fg:w="1"/><text x="62.6083%" y="510.50"></text></g><g><title>getlines (linecache.py:37) (1 samples, 0.23%)</title><rect x="62.3583%" y="516" width="0.2268%" height="15" fill="rgb(246,84,54)" fg:x="275" fg:w="1"/><text x="62.6083%" y="526.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:193) (1 samples, 0.23%)</title><rect x="62.5850%" y="292" width="0.2268%" height="15" fill="rgb(242,110,6)" fg:x="276" fg:w="1"/><text x="62.8350%" y="302.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:206) (4 samples, 0.91%)</title><rect x="62.8118%" y="292" width="0.9070%" height="15" fill="rgb(214,47,5)" fg:x="277" fg:w="4"/><text x="63.0618%" y="302.50"></text></g><g><title>get_cards_for_batch_favorite_sorted (gacha\repositories\collection\user_collection_repository.py:2113) (4 samples, 0.91%)</title><rect x="62.8118%" y="308" width="0.9070%" height="15" fill="rgb(218,159,25)" fg:x="277" fg:w="4"/><text x="63.0618%" y="318.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:217) (1 samples, 0.23%)</title><rect x="63.7188%" y="292" width="0.2268%" height="15" fill="rgb(215,211,28)" fg:x="281" fg:w="1"/><text x="63.9688%" y="302.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:218) (1 samples, 0.23%)</title><rect x="63.9456%" y="292" width="0.2268%" height="15" fill="rgb(238,59,32)" fg:x="282" fg:w="1"/><text x="64.1956%" y="302.50"></text></g><g><title>batch_set_favorite_status_raw (gacha\repositories\collection\user_collection_repository.py:2514) (1 samples, 0.23%)</title><rect x="64.1723%" y="308" width="0.2268%" height="15" fill="rgb(226,82,3)" fg:x="283" fg:w="1"/><text x="64.4223%" y="318.50"></text></g><g><title>call_con_method (asyncpg\pool.py:57) (1 samples, 0.23%)</title><rect x="64.3991%" y="340" width="0.2268%" height="15" fill="rgb(240,164,32)" fg:x="284" fg:w="1"/><text x="64.6491%" y="350.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="64.8526%" y="420" width="0.2268%" height="15" fill="rgb(232,46,7)" fg:x="286" fg:w="1"/><text x="65.1026%" y="430.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="64.8526%" y="436" width="0.2268%" height="15" fill="rgb(229,129,53)" fg:x="286" fg:w="1"/><text x="65.1026%" y="446.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="64.8526%" y="452" width="0.2268%" height="15" fill="rgb(234,188,29)" fg:x="286" fg:w="1"/><text x="65.1026%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="64.8526%" y="468" width="0.2268%" height="15" fill="rgb(246,141,4)" fg:x="286" fg:w="1"/><text x="65.1026%" y="478.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="64.8526%" y="484" width="0.2268%" height="15" fill="rgb(229,23,39)" fg:x="286" fg:w="1"/><text x="65.1026%" y="494.50"></text></g><g><title>send (asyncio\windows_events.py:543) (1 samples, 0.23%)</title><rect x="65.0794%" y="452" width="0.2268%" height="15" fill="rgb(206,12,3)" fg:x="287" fg:w="1"/><text x="65.3294%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="65.3061%" y="532" width="0.4535%" height="15" fill="rgb(252,226,20)" fg:x="288" fg:w="2"/><text x="65.5561%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="65.3061%" y="548" width="0.4535%" height="15" fill="rgb(216,123,35)" fg:x="288" fg:w="2"/><text x="65.5561%" y="558.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (4 samples, 0.91%)</title><rect x="65.0794%" y="436" width="0.9070%" height="15" fill="rgb(212,68,40)" fg:x="287" fg:w="4"/><text x="65.3294%" y="446.50"></text></g><g><title>send (asyncio\windows_events.py:547) (3 samples, 0.68%)</title><rect x="65.3061%" y="452" width="0.6803%" height="15" fill="rgb(254,125,32)" fg:x="288" fg:w="3"/><text x="65.5561%" y="462.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (3 samples, 0.68%)</title><rect x="65.3061%" y="468" width="0.6803%" height="15" fill="rgb(253,97,22)" fg:x="288" fg:w="3"/><text x="65.5561%" y="478.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (3 samples, 0.68%)</title><rect x="65.3061%" y="484" width="0.6803%" height="15" fill="rgb(241,101,14)" fg:x="288" fg:w="3"/><text x="65.5561%" y="494.50"></text></g><g><title>extract_stack (traceback.py:260) (3 samples, 0.68%)</title><rect x="65.3061%" y="500" width="0.6803%" height="15" fill="rgb(238,103,29)" fg:x="288" fg:w="3"/><text x="65.5561%" y="510.50"></text></g><g><title>extract (traceback.py:449) (3 samples, 0.68%)</title><rect x="65.3061%" y="516" width="0.6803%" height="15" fill="rgb(233,195,47)" fg:x="288" fg:w="3"/><text x="65.5561%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="65.7596%" y="532" width="0.2268%" height="15" fill="rgb(246,218,30)" fg:x="290" fg:w="1"/><text x="66.0096%" y="542.50"></text></g><g><title>line (traceback.py:373) (1 samples, 0.23%)</title><rect x="65.7596%" y="548" width="0.2268%" height="15" fill="rgb(219,145,47)" fg:x="290" fg:w="1"/><text x="66.0096%" y="558.50"></text></g><g><title>_set_lines (traceback.py:355) (1 samples, 0.23%)</title><rect x="65.7596%" y="564" width="0.2268%" height="15" fill="rgb(243,12,26)" fg:x="290" fg:w="1"/><text x="66.0096%" y="574.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:491) (1 samples, 0.23%)</title><rect x="65.9864%" y="532" width="0.2268%" height="15" fill="rgb(214,87,16)" fg:x="291" fg:w="1"/><text x="66.2364%" y="542.50"></text></g><g><title>_get_statement (asyncpg\connection.py:432) (8 samples, 1.81%)</title><rect x="64.8526%" y="404" width="1.8141%" height="15" fill="rgb(208,99,42)" fg:x="286" fg:w="8"/><text x="65.1026%" y="414.50">_..</text></g><g><title>write (asyncio\proactor_events.py:366) (7 samples, 1.59%)</title><rect x="65.0794%" y="420" width="1.5873%" height="15" fill="rgb(253,99,2)" fg:x="287" fg:w="7"/><text x="65.3294%" y="430.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (3 samples, 0.68%)</title><rect x="65.9864%" y="436" width="0.6803%" height="15" fill="rgb(220,168,23)" fg:x="291" fg:w="3"/><text x="66.2364%" y="446.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (3 samples, 0.68%)</title><rect x="65.9864%" y="452" width="0.6803%" height="15" fill="rgb(242,38,24)" fg:x="291" fg:w="3"/><text x="66.2364%" y="462.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (3 samples, 0.68%)</title><rect x="65.9864%" y="468" width="0.6803%" height="15" fill="rgb(225,182,9)" fg:x="291" fg:w="3"/><text x="66.2364%" y="478.50"></text></g><g><title>__init__ (asyncio\events.py:47) (3 samples, 0.68%)</title><rect x="65.9864%" y="484" width="0.6803%" height="15" fill="rgb(243,178,37)" fg:x="291" fg:w="3"/><text x="66.2364%" y="494.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (3 samples, 0.68%)</title><rect x="65.9864%" y="500" width="0.6803%" height="15" fill="rgb(232,139,19)" fg:x="291" fg:w="3"/><text x="66.2364%" y="510.50"></text></g><g><title>extract (traceback.py:449) (3 samples, 0.68%)</title><rect x="65.9864%" y="516" width="0.6803%" height="15" fill="rgb(225,201,24)" fg:x="291" fg:w="3"/><text x="66.2364%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="66.2132%" y="532" width="0.4535%" height="15" fill="rgb(221,47,46)" fg:x="292" fg:w="2"/><text x="66.4632%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="66.2132%" y="548" width="0.4535%" height="15" fill="rgb(249,23,13)" fg:x="292" fg:w="2"/><text x="66.4632%" y="558.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2004) (9 samples, 2.04%)</title><rect x="64.8526%" y="388" width="2.0408%" height="15" fill="rgb(219,9,5)" fg:x="286" fg:w="9"/><text x="65.1026%" y="398.50">_..</text></g><g><title>_get_statement (asyncpg\connection.py:470) (1 samples, 0.23%)</title><rect x="66.6667%" y="404" width="0.2268%" height="15" fill="rgb(254,171,16)" fg:x="294" fg:w="1"/><text x="66.9167%" y="414.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (2 samples, 0.45%)</title><rect x="67.1202%" y="404" width="0.4535%" height="15" fill="rgb(230,171,20)" fg:x="296" fg:w="2"/><text x="67.3702%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (2 samples, 0.45%)</title><rect x="67.1202%" y="420" width="0.4535%" height="15" fill="rgb(210,71,41)" fg:x="296" fg:w="2"/><text x="67.3702%" y="430.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="67.1202%" y="436" width="0.4535%" height="15" fill="rgb(206,173,20)" fg:x="296" fg:w="2"/><text x="67.3702%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="67.1202%" y="452" width="0.4535%" height="15" fill="rgb(233,88,34)" fg:x="296" fg:w="2"/><text x="67.3702%" y="462.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="67.3469%" y="468" width="0.2268%" height="15" fill="rgb(223,209,46)" fg:x="297" fg:w="1"/><text x="67.5969%" y="478.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (6 samples, 1.36%)</title><rect x="67.5737%" y="420" width="1.3605%" height="15" fill="rgb(250,43,18)" fg:x="298" fg:w="6"/><text x="67.8237%" y="430.50"></text></g><g><title>send (asyncio\windows_events.py:547) (6 samples, 1.36%)</title><rect x="67.5737%" y="436" width="1.3605%" height="15" fill="rgb(208,13,10)" fg:x="298" fg:w="6"/><text x="67.8237%" y="446.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (6 samples, 1.36%)</title><rect x="67.5737%" y="452" width="1.3605%" height="15" fill="rgb(212,200,36)" fg:x="298" fg:w="6"/><text x="67.8237%" y="462.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (6 samples, 1.36%)</title><rect x="67.5737%" y="468" width="1.3605%" height="15" fill="rgb(225,90,30)" fg:x="298" fg:w="6"/><text x="67.8237%" y="478.50"></text></g><g><title>extract_stack (traceback.py:260) (6 samples, 1.36%)</title><rect x="67.5737%" y="484" width="1.3605%" height="15" fill="rgb(236,182,39)" fg:x="298" fg:w="6"/><text x="67.8237%" y="494.50"></text></g><g><title>extract (traceback.py:449) (6 samples, 1.36%)</title><rect x="67.5737%" y="500" width="1.3605%" height="15" fill="rgb(212,144,35)" fg:x="298" fg:w="6"/><text x="67.8237%" y="510.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (6 samples, 1.36%)</title><rect x="67.5737%" y="516" width="1.3605%" height="15" fill="rgb(228,63,44)" fg:x="298" fg:w="6"/><text x="67.8237%" y="526.50"></text></g><g><title>checkcache (linecache.py:94) (6 samples, 1.36%)</title><rect x="67.5737%" y="532" width="1.3605%" height="15" fill="rgb(228,109,6)" fg:x="298" fg:w="6"/><text x="67.8237%" y="542.50"></text></g><g><title>batch_set_favorite_status_raw (gacha\repositories\collection\user_collection_repository.py:2537) (21 samples, 4.76%)</title><rect x="64.3991%" y="308" width="4.7619%" height="15" fill="rgb(238,117,24)" fg:x="284" fg:w="21"/><text x="64.6491%" y="318.50">batch_..</text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:107) (21 samples, 4.76%)</title><rect x="64.3991%" y="324" width="4.7619%" height="15" fill="rgb(242,26,26)" fg:x="284" fg:w="21"/><text x="64.6491%" y="334.50">fetch_..</text></g><g><title>fetch (asyncpg\connection.py:690) (20 samples, 4.54%)</title><rect x="64.6259%" y="340" width="4.5351%" height="15" fill="rgb(221,92,48)" fg:x="285" fg:w="20"/><text x="64.8759%" y="350.50">fetch..</text></g><g><title>_execute (asyncpg\connection.py:1864) (20 samples, 4.54%)</title><rect x="64.6259%" y="356" width="4.5351%" height="15" fill="rgb(209,209,32)" fg:x="285" fg:w="20"/><text x="64.8759%" y="366.50">_exec..</text></g><g><title>__execute (asyncpg\connection.py:1961) (20 samples, 4.54%)</title><rect x="64.6259%" y="372" width="4.5351%" height="15" fill="rgb(221,70,22)" fg:x="285" fg:w="20"/><text x="64.8759%" y="382.50">__exe..</text></g><g><title>_do_execute (asyncpg\connection.py:2024) (10 samples, 2.27%)</title><rect x="66.8934%" y="388" width="2.2676%" height="15" fill="rgb(248,145,5)" fg:x="295" fg:w="10"/><text x="67.1434%" y="398.50">_..</text></g><g><title>write (asyncio\proactor_events.py:366) (7 samples, 1.59%)</title><rect x="67.5737%" y="404" width="1.5873%" height="15" fill="rgb(226,116,26)" fg:x="298" fg:w="7"/><text x="67.8237%" y="414.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (1 samples, 0.23%)</title><rect x="68.9342%" y="420" width="0.2268%" height="15" fill="rgb(244,5,17)" fg:x="304" fg:w="1"/><text x="69.1842%" y="430.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="68.9342%" y="436" width="0.2268%" height="15" fill="rgb(252,159,33)" fg:x="304" fg:w="1"/><text x="69.1842%" y="446.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="68.9342%" y="452" width="0.2268%" height="15" fill="rgb(206,71,0)" fg:x="304" fg:w="1"/><text x="69.1842%" y="462.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="68.9342%" y="468" width="0.2268%" height="15" fill="rgb(233,118,54)" fg:x="304" fg:w="1"/><text x="69.1842%" y="478.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="68.9342%" y="484" width="0.2268%" height="15" fill="rgb(234,83,48)" fg:x="304" fg:w="1"/><text x="69.1842%" y="494.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="68.9342%" y="500" width="0.2268%" height="15" fill="rgb(228,3,54)" fg:x="304" fg:w="1"/><text x="69.1842%" y="510.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="68.9342%" y="516" width="0.2268%" height="15" fill="rgb(226,155,13)" fg:x="304" fg:w="1"/><text x="69.1842%" y="526.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="68.9342%" y="532" width="0.2268%" height="15" fill="rgb(241,28,37)" fg:x="304" fg:w="1"/><text x="69.1842%" y="542.50"></text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:223) (23 samples, 5.22%)</title><rect x="64.1723%" y="292" width="5.2154%" height="15" fill="rgb(233,93,10)" fg:x="283" fg:w="23"/><text x="64.4223%" y="302.50">batch_..</text></g><g><title>batch_set_favorite_status_raw (gacha\repositories\collection\user_collection_repository.py:2543) (1 samples, 0.23%)</title><rect x="69.1610%" y="308" width="0.2268%" height="15" fill="rgb(225,113,19)" fg:x="305" fg:w="1"/><text x="69.4110%" y="318.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2004) (2 samples, 0.45%)</title><rect x="69.6145%" y="404" width="0.4535%" height="15" fill="rgb(241,2,18)" fg:x="307" fg:w="2"/><text x="69.8645%" y="414.50"></text></g><g><title>_get_statement (asyncpg\connection.py:392) (2 samples, 0.45%)</title><rect x="69.6145%" y="420" width="0.4535%" height="15" fill="rgb(228,207,21)" fg:x="307" fg:w="2"/><text x="69.8645%" y="430.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="70.2948%" y="420" width="0.2268%" height="15" fill="rgb(213,211,35)" fg:x="310" fg:w="1"/><text x="70.5448%" y="430.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="70.2948%" y="436" width="0.2268%" height="15" fill="rgb(209,83,10)" fg:x="310" fg:w="1"/><text x="70.5448%" y="446.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="70.2948%" y="452" width="0.2268%" height="15" fill="rgb(209,164,1)" fg:x="310" fg:w="1"/><text x="70.5448%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="70.2948%" y="468" width="0.2268%" height="15" fill="rgb(213,184,43)" fg:x="310" fg:w="1"/><text x="70.5448%" y="478.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="70.2948%" y="484" width="0.2268%" height="15" fill="rgb(231,61,34)" fg:x="310" fg:w="1"/><text x="70.5448%" y="494.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (3 samples, 0.68%)</title><rect x="70.5215%" y="436" width="0.6803%" height="15" fill="rgb(235,75,3)" fg:x="311" fg:w="3"/><text x="70.7715%" y="446.50"></text></g><g><title>send (asyncio\windows_events.py:547) (3 samples, 0.68%)</title><rect x="70.5215%" y="452" width="0.6803%" height="15" fill="rgb(220,106,47)" fg:x="311" fg:w="3"/><text x="70.7715%" y="462.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (3 samples, 0.68%)</title><rect x="70.5215%" y="468" width="0.6803%" height="15" fill="rgb(210,196,33)" fg:x="311" fg:w="3"/><text x="70.7715%" y="478.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (3 samples, 0.68%)</title><rect x="70.5215%" y="484" width="0.6803%" height="15" fill="rgb(229,154,42)" fg:x="311" fg:w="3"/><text x="70.7715%" y="494.50"></text></g><g><title>extract_stack (traceback.py:260) (3 samples, 0.68%)</title><rect x="70.5215%" y="500" width="0.6803%" height="15" fill="rgb(228,114,26)" fg:x="311" fg:w="3"/><text x="70.7715%" y="510.50"></text></g><g><title>extract (traceback.py:449) (3 samples, 0.68%)</title><rect x="70.5215%" y="516" width="0.6803%" height="15" fill="rgb(208,144,1)" fg:x="311" fg:w="3"/><text x="70.7715%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (3 samples, 0.68%)</title><rect x="70.5215%" y="532" width="0.6803%" height="15" fill="rgb(239,112,37)" fg:x="311" fg:w="3"/><text x="70.7715%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (3 samples, 0.68%)</title><rect x="70.5215%" y="548" width="0.6803%" height="15" fill="rgb(210,96,50)" fg:x="311" fg:w="3"/><text x="70.7715%" y="558.50"></text></g><g><title>checkcache (linecache.py:77) (1 samples, 0.23%)</title><rect x="71.2018%" y="548" width="0.2268%" height="15" fill="rgb(222,178,2)" fg:x="314" fg:w="1"/><text x="71.4518%" y="558.50"></text></g><g><title>_bulk_update_stats_column (gacha\services\direct_market_stats_updater.py:111) (9 samples, 2.04%)</title><rect x="69.6145%" y="340" width="2.0408%" height="15" fill="rgb(226,74,18)" fg:x="307" fg:w="9"/><text x="69.8645%" y="350.50">_..</text></g><g><title>execute (asyncpg\connection.py:352) (9 samples, 2.04%)</title><rect x="69.6145%" y="356" width="2.0408%" height="15" fill="rgb(225,67,54)" fg:x="307" fg:w="9"/><text x="69.8645%" y="366.50">e..</text></g><g><title>_execute (asyncpg\connection.py:1864) (9 samples, 2.04%)</title><rect x="69.6145%" y="372" width="2.0408%" height="15" fill="rgb(251,92,32)" fg:x="307" fg:w="9"/><text x="69.8645%" y="382.50">_..</text></g><g><title>__execute (asyncpg\connection.py:1961) (9 samples, 2.04%)</title><rect x="69.6145%" y="388" width="2.0408%" height="15" fill="rgb(228,149,22)" fg:x="307" fg:w="9"/><text x="69.8645%" y="398.50">_..</text></g><g><title>_do_execute (asyncpg\connection.py:2024) (7 samples, 1.59%)</title><rect x="70.0680%" y="404" width="1.5873%" height="15" fill="rgb(243,54,13)" fg:x="309" fg:w="7"/><text x="70.3180%" y="414.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (5 samples, 1.13%)</title><rect x="70.5215%" y="420" width="1.1338%" height="15" fill="rgb(243,180,28)" fg:x="311" fg:w="5"/><text x="70.7715%" y="430.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (2 samples, 0.45%)</title><rect x="71.2018%" y="436" width="0.4535%" height="15" fill="rgb(208,167,24)" fg:x="314" fg:w="2"/><text x="71.4518%" y="446.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (2 samples, 0.45%)</title><rect x="71.2018%" y="452" width="0.4535%" height="15" fill="rgb(245,73,45)" fg:x="314" fg:w="2"/><text x="71.4518%" y="462.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (2 samples, 0.45%)</title><rect x="71.2018%" y="468" width="0.4535%" height="15" fill="rgb(237,203,48)" fg:x="314" fg:w="2"/><text x="71.4518%" y="478.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="71.2018%" y="484" width="0.4535%" height="15" fill="rgb(211,197,16)" fg:x="314" fg:w="2"/><text x="71.4518%" y="494.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="71.2018%" y="500" width="0.4535%" height="15" fill="rgb(243,99,51)" fg:x="314" fg:w="2"/><text x="71.4518%" y="510.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="71.2018%" y="516" width="0.4535%" height="15" fill="rgb(215,123,29)" fg:x="314" fg:w="2"/><text x="71.4518%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="71.2018%" y="532" width="0.4535%" height="15" fill="rgb(239,186,37)" fg:x="314" fg:w="2"/><text x="71.4518%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="71.4286%" y="548" width="0.2268%" height="15" fill="rgb(252,136,39)" fg:x="315" fg:w="1"/><text x="71.6786%" y="558.50"></text></g><g><title>_execute_batch_db_operation (gacha\views\collection\collection_view\card_view.py:558) (54 samples, 12.24%)</title><rect x="60.3175%" y="276" width="12.2449%" height="15" fill="rgb(223,213,32)" fg:x="266" fg:w="54"/><text x="60.5675%" y="286.50">_execute_batch_db_..</text></g><g><title>batch_favorite_cards (gacha\services\favorite_service.py:244) (14 samples, 3.17%)</title><rect x="69.3878%" y="292" width="3.1746%" height="15" fill="rgb(233,115,5)" fg:x="306" fg:w="14"/><text x="69.6378%" y="302.50">bat..</text></g><g><title>update_market_stats_in_transaction (gacha\services\direct_market_stats_updater.py:404) (14 samples, 3.17%)</title><rect x="69.3878%" y="308" width="3.1746%" height="15" fill="rgb(207,226,44)" fg:x="306" fg:w="14"/><text x="69.6378%" y="318.50">upd..</text></g><g><title>bulk_update_favorite_counts_direct (gacha\services\direct_market_stats_updater.py:155) (14 samples, 3.17%)</title><rect x="69.3878%" y="324" width="3.1746%" height="15" fill="rgb(208,126,0)" fg:x="306" fg:w="14"/><text x="69.6378%" y="334.50">bul..</text></g><g><title>_bulk_update_stats_column (gacha\services\direct_market_stats_updater.py:86) (4 samples, 0.91%)</title><rect x="71.6553%" y="340" width="0.9070%" height="15" fill="rgb(244,66,21)" fg:x="316" fg:w="4"/><text x="71.9053%" y="350.50"></text></g><g><title>_request (aiohttp\client.py:594) (1 samples, 0.23%)</title><rect x="72.5624%" y="356" width="0.2268%" height="15" fill="rgb(222,97,12)" fg:x="320" fg:w="1"/><text x="72.8124%" y="366.50"></text></g><g><title>start (aiohttp\helpers.py:587) (1 samples, 0.23%)</title><rect x="72.5624%" y="372" width="0.2268%" height="15" fill="rgb(219,213,19)" fg:x="320" fg:w="1"/><text x="72.8124%" y="382.50"></text></g><g><title>call_at (asyncio\base_events.py:816) (1 samples, 0.23%)</title><rect x="72.5624%" y="388" width="0.2268%" height="15" fill="rgb(252,169,30)" fg:x="320" fg:w="1"/><text x="72.8124%" y="398.50"></text></g><g><title>__init__ (asyncio\events.py:114) (1 samples, 0.23%)</title><rect x="72.5624%" y="404" width="0.2268%" height="15" fill="rgb(206,32,51)" fg:x="320" fg:w="1"/><text x="72.8124%" y="414.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="72.5624%" y="420" width="0.2268%" height="15" fill="rgb(250,172,42)" fg:x="320" fg:w="1"/><text x="72.8124%" y="430.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="72.5624%" y="436" width="0.2268%" height="15" fill="rgb(209,34,43)" fg:x="320" fg:w="1"/><text x="72.8124%" y="446.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="72.5624%" y="452" width="0.2268%" height="15" fill="rgb(223,11,35)" fg:x="320" fg:w="1"/><text x="72.8124%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="72.5624%" y="468" width="0.2268%" height="15" fill="rgb(251,219,26)" fg:x="320" fg:w="1"/><text x="72.8124%" y="478.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="72.5624%" y="484" width="0.2268%" height="15" fill="rgb(231,119,3)" fg:x="320" fg:w="1"/><text x="72.8124%" y="494.50"></text></g><g><title>_confirm_batch_favorite_action (gacha\views\collection\collection_view\card_view.py:501) (56 samples, 12.70%)</title><rect x="60.3175%" y="244" width="12.6984%" height="15" fill="rgb(216,97,11)" fg:x="266" fg:w="56"/><text x="60.5675%" y="254.50">_confirm_batch_favo..</text></g><g><title>_execute_batch_favorite (gacha\views\collection\collection_view\card_view.py:507) (56 samples, 12.70%)</title><rect x="60.3175%" y="260" width="12.6984%" height="15" fill="rgb(223,59,9)" fg:x="266" fg:w="56"/><text x="60.5675%" y="270.50">_execute_batch_favo..</text></g><g><title>_execute_batch_db_operation (gacha\views\collection\collection_view\card_view.py:559) (2 samples, 0.45%)</title><rect x="72.5624%" y="276" width="0.4535%" height="15" fill="rgb(233,93,31)" fg:x="320" fg:w="2"/><text x="72.8124%" y="286.50"></text></g><g><title>_show_batch_operation_success (gacha\views\collection\collection_view\card_view.py:571) (2 samples, 0.45%)</title><rect x="72.5624%" y="292" width="0.4535%" height="15" fill="rgb(239,81,33)" fg:x="320" fg:w="2"/><text x="72.8124%" y="302.50"></text></g><g><title>edit_original_response (discord\interactions.py:557) (2 samples, 0.45%)</title><rect x="72.5624%" y="308" width="0.4535%" height="15" fill="rgb(213,120,34)" fg:x="320" fg:w="2"/><text x="72.8124%" y="318.50"></text></g><g><title>request (discord\webhook\async_.py:182) (2 samples, 0.45%)</title><rect x="72.5624%" y="324" width="0.4535%" height="15" fill="rgb(243,49,53)" fg:x="320" fg:w="2"/><text x="72.8124%" y="334.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (2 samples, 0.45%)</title><rect x="72.5624%" y="340" width="0.4535%" height="15" fill="rgb(247,216,33)" fg:x="320" fg:w="2"/><text x="72.8124%" y="350.50"></text></g><g><title>_request (aiohttp\client.py:794) (1 samples, 0.23%)</title><rect x="72.7891%" y="356" width="0.2268%" height="15" fill="rgb(226,26,14)" fg:x="321" fg:w="1"/><text x="73.0391%" y="366.50"></text></g><g><title>update_cookies_from_headers (aiohttp\abc.py:201) (1 samples, 0.23%)</title><rect x="72.7891%" y="372" width="0.2268%" height="15" fill="rgb(215,49,53)" fg:x="321" fg:w="1"/><text x="73.0391%" y="382.50"></text></g><g><title>batch_unfavorite_cards (gacha\services\favorite_service.py:286) (1 samples, 0.23%)</title><rect x="73.0159%" y="292" width="0.2268%" height="15" fill="rgb(245,162,40)" fg:x="322" fg:w="1"/><text x="73.2659%" y="302.50"></text></g><g><title>__aexit__ (asyncpg\transaction.py:91) (1 samples, 0.23%)</title><rect x="73.0159%" y="308" width="0.2268%" height="15" fill="rgb(229,68,17)" fg:x="322" fg:w="1"/><text x="73.2659%" y="318.50"></text></g><g><title>__commit (asyncpg\transaction.py:187) (1 samples, 0.23%)</title><rect x="73.0159%" y="324" width="0.2268%" height="15" fill="rgb(213,182,10)" fg:x="322" fg:w="1"/><text x="73.2659%" y="334.50"></text></g><g><title>execute (asyncpg\connection.py:349) (1 samples, 0.23%)</title><rect x="73.0159%" y="340" width="0.2268%" height="15" fill="rgb(245,125,30)" fg:x="322" fg:w="1"/><text x="73.2659%" y="350.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="73.0159%" y="356" width="0.2268%" height="15" fill="rgb(232,202,2)" fg:x="322" fg:w="1"/><text x="73.2659%" y="366.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="73.0159%" y="372" width="0.2268%" height="15" fill="rgb(237,140,51)" fg:x="322" fg:w="1"/><text x="73.2659%" y="382.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="73.0159%" y="388" width="0.2268%" height="15" fill="rgb(236,157,25)" fg:x="322" fg:w="1"/><text x="73.2659%" y="398.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:488) (1 samples, 0.23%)</title><rect x="73.0159%" y="404" width="0.2268%" height="15" fill="rgb(219,209,0)" fg:x="322" fg:w="1"/><text x="73.2659%" y="414.50"></text></g><g><title>_execute_batch_db_operation (gacha\views\collection\collection_view\card_view.py:558) (2 samples, 0.45%)</title><rect x="73.0159%" y="276" width="0.4535%" height="15" fill="rgb(240,116,54)" fg:x="322" fg:w="2"/><text x="73.2659%" y="286.50"></text></g><g><title>batch_unfavorite_cards (gacha\services\favorite_service.py:305) (1 samples, 0.23%)</title><rect x="73.2426%" y="292" width="0.2268%" height="15" fill="rgb(216,10,36)" fg:x="323" fg:w="1"/><text x="73.4926%" y="302.50"></text></g><g><title>batch_set_favorite_status_raw (gacha\repositories\collection\user_collection_repository.py:2537) (1 samples, 0.23%)</title><rect x="73.2426%" y="308" width="0.2268%" height="15" fill="rgb(222,72,44)" fg:x="323" fg:w="1"/><text x="73.4926%" y="318.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:107) (1 samples, 0.23%)</title><rect x="73.2426%" y="324" width="0.2268%" height="15" fill="rgb(232,159,9)" fg:x="323" fg:w="1"/><text x="73.4926%" y="334.50"></text></g><g><title>fetch (asyncpg\connection.py:690) (1 samples, 0.23%)</title><rect x="73.2426%" y="340" width="0.2268%" height="15" fill="rgb(210,39,32)" fg:x="323" fg:w="1"/><text x="73.4926%" y="350.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="73.2426%" y="356" width="0.2268%" height="15" fill="rgb(216,194,45)" fg:x="323" fg:w="1"/><text x="73.4926%" y="366.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="73.2426%" y="372" width="0.2268%" height="15" fill="rgb(218,18,35)" fg:x="323" fg:w="1"/><text x="73.4926%" y="382.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="73.2426%" y="388" width="0.2268%" height="15" fill="rgb(207,83,51)" fg:x="323" fg:w="1"/><text x="73.4926%" y="398.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="73.2426%" y="404" width="0.2268%" height="15" fill="rgb(225,63,43)" fg:x="323" fg:w="1"/><text x="73.4926%" y="414.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (1 samples, 0.23%)</title><rect x="73.2426%" y="420" width="0.2268%" height="15" fill="rgb(207,57,36)" fg:x="323" fg:w="1"/><text x="73.4926%" y="430.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="73.2426%" y="436" width="0.2268%" height="15" fill="rgb(216,99,33)" fg:x="323" fg:w="1"/><text x="73.4926%" y="446.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="73.2426%" y="452" width="0.2268%" height="15" fill="rgb(225,42,16)" fg:x="323" fg:w="1"/><text x="73.4926%" y="462.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="73.2426%" y="468" width="0.2268%" height="15" fill="rgb(220,201,45)" fg:x="323" fg:w="1"/><text x="73.4926%" y="478.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="73.2426%" y="484" width="0.2268%" height="15" fill="rgb(225,33,4)" fg:x="323" fg:w="1"/><text x="73.4926%" y="494.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="73.2426%" y="500" width="0.2268%" height="15" fill="rgb(224,33,50)" fg:x="323" fg:w="1"/><text x="73.4926%" y="510.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="73.2426%" y="516" width="0.2268%" height="15" fill="rgb(246,198,51)" fg:x="323" fg:w="1"/><text x="73.4926%" y="526.50"></text></g><g><title>checkcache (linecache.py:76) (1 samples, 0.23%)</title><rect x="73.2426%" y="532" width="0.2268%" height="15" fill="rgb(205,22,4)" fg:x="323" fg:w="1"/><text x="73.4926%" y="542.50"></text></g><g><title>_refresh_view_after_batch_operation (gacha\views\collection\collection_view\card_view.py:578) (1 samples, 0.23%)</title><rect x="73.4694%" y="308" width="0.2268%" height="15" fill="rgb(206,3,8)" fg:x="324" fg:w="1"/><text x="73.7194%" y="318.50"></text></g><g><title>_fetch_and_apply_page_data (gacha\views\collection\collection_view\card_view.py:203) (1 samples, 0.23%)</title><rect x="73.4694%" y="324" width="0.2268%" height="15" fill="rgb(251,23,15)" fg:x="324" fg:w="1"/><text x="73.7194%" y="334.50"></text></g><g><title>get_user_cards_paginated (gacha\services\collection_service.py:70) (1 samples, 0.23%)</title><rect x="73.4694%" y="340" width="0.2268%" height="15" fill="rgb(252,88,28)" fg:x="324" fg:w="1"/><text x="73.7194%" y="350.50"></text></g><g><title>get_user_cards_paginated (gacha\repositories\collection\user_collection_repository.py:748) (1 samples, 0.23%)</title><rect x="73.4694%" y="356" width="0.2268%" height="15" fill="rgb(212,127,14)" fg:x="324" fg:w="1"/><text x="73.7194%" y="366.50"></text></g><g><title>_fetch_user_cards_for_page (gacha\repositories\collection\user_collection_repository.py:845) (1 samples, 0.23%)</title><rect x="73.4694%" y="372" width="0.2268%" height="15" fill="rgb(247,145,37)" fg:x="324" fg:w="1"/><text x="73.7194%" y="382.50"></text></g><g><title>_attach_owner_counts_to_cards (gacha\repositories\collection\user_collection_repository.py:1006) (1 samples, 0.23%)</title><rect x="73.4694%" y="388" width="0.2268%" height="15" fill="rgb(209,117,53)" fg:x="324" fg:w="1"/><text x="73.7194%" y="398.50"></text></g><g><title>get_card_owner_counts_batch (gacha\repositories\collection\user_collection_repository.py:1162) (1 samples, 0.23%)</title><rect x="73.4694%" y="404" width="0.2268%" height="15" fill="rgb(212,90,42)" fg:x="324" fg:w="1"/><text x="73.7194%" y="414.50"></text></g><g><title>_process_cached_owner_counts (gacha\repositories\collection\user_collection_repository.py:1077) (1 samples, 0.23%)</title><rect x="73.4694%" y="420" width="0.2268%" height="15" fill="rgb(218,164,37)" fg:x="324" fg:w="1"/><text x="73.7194%" y="430.50"></text></g><g><title>execute_command (redis\asyncio\client.py:672) (1 samples, 0.23%)</title><rect x="73.4694%" y="436" width="0.2268%" height="15" fill="rgb(246,65,34)" fg:x="324" fg:w="1"/><text x="73.7194%" y="446.50"></text></g><g><title>get_connection (redis\asyncio\connection.py:1141) (1 samples, 0.23%)</title><rect x="73.4694%" y="452" width="0.2268%" height="15" fill="rgb(231,100,33)" fg:x="324" fg:w="1"/><text x="73.7194%" y="462.50"></text></g><g><title>ensure_connection (redis\asyncio\connection.py:1180) (1 samples, 0.23%)</title><rect x="73.4694%" y="468" width="0.2268%" height="15" fill="rgb(228,126,14)" fg:x="324" fg:w="1"/><text x="73.7194%" y="478.50"></text></g><g><title>can_read_destructive (redis\asyncio\connection.py:561) (1 samples, 0.23%)</title><rect x="73.4694%" y="484" width="0.2268%" height="15" fill="rgb(215,173,21)" fg:x="324" fg:w="1"/><text x="73.7194%" y="494.50"></text></g><g><title>can_read_destructive (redis\_parsers\hiredis.py:239) (1 samples, 0.23%)</title><rect x="73.4694%" y="500" width="0.2268%" height="15" fill="rgb(210,6,40)" fg:x="324" fg:w="1"/><text x="73.7194%" y="510.50"></text></g><g><title>__aenter__ (asyncio\timeouts.py:94) (1 samples, 0.23%)</title><rect x="73.4694%" y="516" width="0.2268%" height="15" fill="rgb(212,48,18)" fg:x="324" fg:w="1"/><text x="73.7194%" y="526.50"></text></g><g><title>reschedule (asyncio\timeouts.py:69) (1 samples, 0.23%)</title><rect x="73.4694%" y="532" width="0.2268%" height="15" fill="rgb(230,214,11)" fg:x="324" fg:w="1"/><text x="73.7194%" y="542.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="73.4694%" y="548" width="0.2268%" height="15" fill="rgb(254,105,39)" fg:x="324" fg:w="1"/><text x="73.7194%" y="558.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="73.4694%" y="564" width="0.2268%" height="15" fill="rgb(245,158,5)" fg:x="324" fg:w="1"/><text x="73.7194%" y="574.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="73.4694%" y="580" width="0.2268%" height="15" fill="rgb(249,208,11)" fg:x="324" fg:w="1"/><text x="73.7194%" y="590.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="73.4694%" y="596" width="0.2268%" height="15" fill="rgb(210,39,28)" fg:x="324" fg:w="1"/><text x="73.7194%" y="606.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="73.4694%" y="612" width="0.2268%" height="15" fill="rgb(211,56,53)" fg:x="324" fg:w="1"/><text x="73.7194%" y="622.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="73.4694%" y="628" width="0.2268%" height="15" fill="rgb(226,201,30)" fg:x="324" fg:w="1"/><text x="73.7194%" y="638.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="73.4694%" y="644" width="0.2268%" height="15" fill="rgb(239,101,34)" fg:x="324" fg:w="1"/><text x="73.7194%" y="654.50"></text></g><g><title>confirm_button (confirmation.py:83) (61 samples, 13.83%)</title><rect x="60.0907%" y="212" width="13.8322%" height="15" fill="rgb(226,209,5)" fg:x="265" fg:w="61"/><text x="60.3407%" y="222.50">confirm_button (confi..</text></g><g><title>on_confirm (gacha\views\collection\collection_view\card_view.py:468) (61 samples, 13.83%)</title><rect x="60.0907%" y="228" width="13.8322%" height="15" fill="rgb(250,105,47)" fg:x="265" fg:w="61"/><text x="60.3407%" y="238.50">on_confirm (gacha\vie..</text></g><g><title>_confirm_batch_unfavorite_action (gacha\views\collection\collection_view\card_view.py:523) (4 samples, 0.91%)</title><rect x="73.0159%" y="244" width="0.9070%" height="15" fill="rgb(230,72,3)" fg:x="322" fg:w="4"/><text x="73.2659%" y="254.50"></text></g><g><title>_execute_batch_unfavorite (gacha\views\collection\collection_view\card_view.py:529) (4 samples, 0.91%)</title><rect x="73.0159%" y="260" width="0.9070%" height="15" fill="rgb(232,218,39)" fg:x="322" fg:w="4"/><text x="73.2659%" y="270.50"></text></g><g><title>_execute_batch_db_operation (gacha\views\collection\collection_view\card_view.py:559) (2 samples, 0.45%)</title><rect x="73.4694%" y="276" width="0.4535%" height="15" fill="rgb(248,166,6)" fg:x="324" fg:w="2"/><text x="73.7194%" y="286.50"></text></g><g><title>_show_batch_operation_success (gacha\views\collection\collection_view\card_view.py:574) (2 samples, 0.45%)</title><rect x="73.4694%" y="292" width="0.4535%" height="15" fill="rgb(247,89,20)" fg:x="324" fg:w="2"/><text x="73.7194%" y="302.50"></text></g><g><title>_refresh_view_after_batch_operation (gacha\views\collection\collection_view\card_view.py:582) (1 samples, 0.23%)</title><rect x="73.6961%" y="308" width="0.2268%" height="15" fill="rgb(248,130,54)" fg:x="325" fg:w="1"/><text x="73.9461%" y="318.50"></text></g><g><title>edit (discord\webhook\async_.py:865) (1 samples, 0.23%)</title><rect x="73.6961%" y="324" width="0.2268%" height="15" fill="rgb(234,196,4)" fg:x="325" fg:w="1"/><text x="73.9461%" y="334.50"></text></g><g><title>edit_message (discord\webhook\async_.py:2035) (1 samples, 0.23%)</title><rect x="73.6961%" y="340" width="0.2268%" height="15" fill="rgb(250,143,31)" fg:x="325" fg:w="1"/><text x="73.9461%" y="350.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="73.6961%" y="356" width="0.2268%" height="15" fill="rgb(211,110,34)" fg:x="325" fg:w="1"/><text x="73.9461%" y="366.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="73.6961%" y="372" width="0.2268%" height="15" fill="rgb(215,124,48)" fg:x="325" fg:w="1"/><text x="73.9461%" y="382.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="73.6961%" y="388" width="0.2268%" height="15" fill="rgb(216,46,13)" fg:x="325" fg:w="1"/><text x="73.9461%" y="398.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="73.6961%" y="404" width="0.2268%" height="15" fill="rgb(205,184,25)" fg:x="325" fg:w="1"/><text x="73.9461%" y="414.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1476) (1 samples, 0.23%)</title><rect x="73.6961%" y="420" width="0.2268%" height="15" fill="rgb(228,1,10)" fg:x="325" fg:w="1"/><text x="73.9461%" y="430.50"></text></g><g><title>write_bytes (aiohttp\client_reqrep.py:1374) (1 samples, 0.23%)</title><rect x="73.6961%" y="436" width="0.2268%" height="15" fill="rgb(213,116,27)" fg:x="325" fg:w="1"/><text x="73.9461%" y="446.50"></text></g><g><title>write_with_length (aiohttp\payload.py:425) (1 samples, 0.23%)</title><rect x="73.6961%" y="452" width="0.2268%" height="15" fill="rgb(241,95,50)" fg:x="325" fg:w="1"/><text x="73.9461%" y="462.50"></text></g><g><title>write (aiohttp\http_writer.py:194) (1 samples, 0.23%)</title><rect x="73.6961%" y="468" width="0.2268%" height="15" fill="rgb(238,48,32)" fg:x="325" fg:w="1"/><text x="73.9461%" y="478.50"></text></g><g><title>_send_headers_with_payload (aiohttp\http_writer.py:138) (1 samples, 0.23%)</title><rect x="73.6961%" y="484" width="0.2268%" height="15" fill="rgb(235,113,49)" fg:x="325" fg:w="1"/><text x="73.9461%" y="494.50"></text></g><g><title>_writelines (aiohttp\http_writer.py:110) (1 samples, 0.23%)</title><rect x="73.6961%" y="500" width="0.2268%" height="15" fill="rgb(205,127,43)" fg:x="325" fg:w="1"/><text x="73.9461%" y="510.50"></text></g><g><title>writelines (asyncio\sslproto.py:230) (1 samples, 0.23%)</title><rect x="73.6961%" y="516" width="0.2268%" height="15" fill="rgb(250,162,2)" fg:x="325" fg:w="1"/><text x="73.9461%" y="526.50"></text></g><g><title>_write_appdata (asyncio\sslproto.py:697) (1 samples, 0.23%)</title><rect x="73.6961%" y="532" width="0.2268%" height="15" fill="rgb(220,13,41)" fg:x="325" fg:w="1"/><text x="73.9461%" y="542.50"></text></g><g><title>_do_write (asyncio\sslproto.py:716) (1 samples, 0.23%)</title><rect x="73.6961%" y="548" width="0.2268%" height="15" fill="rgb(249,221,25)" fg:x="325" fg:w="1"/><text x="73.9461%" y="558.50"></text></g><g><title>_process_outgoing (asyncio\sslproto.py:722) (1 samples, 0.23%)</title><rect x="73.6961%" y="564" width="0.2268%" height="15" fill="rgb(215,208,19)" fg:x="325" fg:w="1"/><text x="73.9461%" y="574.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="73.6961%" y="580" width="0.2268%" height="15" fill="rgb(236,175,2)" fg:x="325" fg:w="1"/><text x="73.9461%" y="590.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="73.6961%" y="596" width="0.2268%" height="15" fill="rgb(241,52,2)" fg:x="325" fg:w="1"/><text x="73.9461%" y="606.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="73.6961%" y="612" width="0.2268%" height="15" fill="rgb(248,140,14)" fg:x="325" fg:w="1"/><text x="73.9461%" y="622.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="73.6961%" y="628" width="0.2268%" height="15" fill="rgb(253,22,42)" fg:x="325" fg:w="1"/><text x="73.9461%" y="638.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="73.6961%" y="644" width="0.2268%" height="15" fill="rgb(234,61,47)" fg:x="325" fg:w="1"/><text x="73.9461%" y="654.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="73.6961%" y="660" width="0.2268%" height="15" fill="rgb(208,226,15)" fg:x="325" fg:w="1"/><text x="73.9461%" y="670.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="73.6961%" y="676" width="0.2268%" height="15" fill="rgb(217,221,4)" fg:x="325" fg:w="1"/><text x="73.9461%" y="686.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="73.6961%" y="692" width="0.2268%" height="15" fill="rgb(212,174,34)" fg:x="325" fg:w="1"/><text x="73.9461%" y="702.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="73.6961%" y="708" width="0.2268%" height="15" fill="rgb(253,83,4)" fg:x="325" fg:w="1"/><text x="73.9461%" y="718.50"></text></g><g><title>_create_direct_connection (aiohttp\connector.py:1512) (1 samples, 0.23%)</title><rect x="73.9229%" y="356" width="0.2268%" height="15" fill="rgb(250,195,49)" fg:x="326" fg:w="1"/><text x="74.1729%" y="366.50"></text></g><g><title>_resolve_host (aiohttp\connector.py:1119) (1 samples, 0.23%)</title><rect x="73.9229%" y="372" width="0.2268%" height="15" fill="rgb(241,192,25)" fg:x="326" fg:w="1"/><text x="74.1729%" y="382.50"></text></g><g><title>_resolve_host_with_throttle (aiohttp\connector.py:1159) (1 samples, 0.23%)</title><rect x="73.9229%" y="388" width="0.2268%" height="15" fill="rgb(208,124,10)" fg:x="326" fg:w="1"/><text x="74.1729%" y="398.50"></text></g><g><title>resolve (aiohttp\resolver.py:40) (1 samples, 0.23%)</title><rect x="73.9229%" y="404" width="0.2268%" height="15" fill="rgb(222,33,0)" fg:x="326" fg:w="1"/><text x="74.1729%" y="414.50"></text></g><g><title>getaddrinfo (asyncio\base_events.py:940) (1 samples, 0.23%)</title><rect x="73.9229%" y="420" width="0.2268%" height="15" fill="rgb(234,209,28)" fg:x="326" fg:w="1"/><text x="74.1729%" y="430.50"></text></g><g><title>run_in_executor (asyncio\base_events.py:901) (1 samples, 0.23%)</title><rect x="73.9229%" y="436" width="0.2268%" height="15" fill="rgb(224,11,23)" fg:x="326" fg:w="1"/><text x="74.1729%" y="446.50"></text></g><g><title>submit (concurrent\futures\thread.py:179) (1 samples, 0.23%)</title><rect x="73.9229%" y="452" width="0.2268%" height="15" fill="rgb(232,99,1)" fg:x="326" fg:w="1"/><text x="74.1729%" y="462.50"></text></g><g><title>connect (aiohttp\connector.py:622) (2 samples, 0.45%)</title><rect x="73.9229%" y="324" width="0.4535%" height="15" fill="rgb(237,95,45)" fg:x="326" fg:w="2"/><text x="74.1729%" y="334.50"></text></g><g><title>_create_connection (aiohttp\connector.py:1189) (2 samples, 0.45%)</title><rect x="73.9229%" y="340" width="0.4535%" height="15" fill="rgb(208,109,11)" fg:x="326" fg:w="2"/><text x="74.1729%" y="350.50"></text></g><g><title>_create_direct_connection (aiohttp\connector.py:1530) (1 samples, 0.23%)</title><rect x="74.1497%" y="356" width="0.2268%" height="15" fill="rgb(216,190,48)" fg:x="327" fg:w="1"/><text x="74.3997%" y="366.50"></text></g><g><title>_wrap_create_connection (aiohttp\connector.py:1248) (1 samples, 0.23%)</title><rect x="74.1497%" y="372" width="0.2268%" height="15" fill="rgb(251,171,36)" fg:x="327" fg:w="1"/><text x="74.3997%" y="382.50"></text></g><g><title>start_connection (aiohappyeyeballs\impl.py:87) (1 samples, 0.23%)</title><rect x="74.1497%" y="388" width="0.2268%" height="15" fill="rgb(230,62,22)" fg:x="327" fg:w="1"/><text x="74.3997%" y="398.50"></text></g><g><title>staggered_race (aiohappyeyeballs\_staggered.py:157) (1 samples, 0.23%)</title><rect x="74.1497%" y="404" width="0.2268%" height="15" fill="rgb(225,114,35)" fg:x="327" fg:w="1"/><text x="74.3997%" y="414.50"></text></g><g><title>call_later (asyncio\base_events.py:799) (1 samples, 0.23%)</title><rect x="74.1497%" y="420" width="0.2268%" height="15" fill="rgb(215,118,42)" fg:x="327" fg:w="1"/><text x="74.3997%" y="430.50"></text></g><g><title>call_at (asyncio\base_events.py:816) (1 samples, 0.23%)</title><rect x="74.1497%" y="436" width="0.2268%" height="15" fill="rgb(243,119,21)" fg:x="327" fg:w="1"/><text x="74.3997%" y="446.50"></text></g><g><title>__init__ (asyncio\events.py:114) (1 samples, 0.23%)</title><rect x="74.1497%" y="452" width="0.2268%" height="15" fill="rgb(252,177,53)" fg:x="327" fg:w="1"/><text x="74.3997%" y="462.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="74.1497%" y="468" width="0.2268%" height="15" fill="rgb(237,209,29)" fg:x="327" fg:w="1"/><text x="74.3997%" y="478.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="74.1497%" y="484" width="0.2268%" height="15" fill="rgb(212,65,23)" fg:x="327" fg:w="1"/><text x="74.3997%" y="494.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="74.1497%" y="500" width="0.2268%" height="15" fill="rgb(230,222,46)" fg:x="327" fg:w="1"/><text x="74.3997%" y="510.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="74.1497%" y="516" width="0.2268%" height="15" fill="rgb(215,135,32)" fg:x="327" fg:w="1"/><text x="74.3997%" y="526.50"></text></g><g><title>checkcache (linecache.py:90) (1 samples, 0.23%)</title><rect x="74.1497%" y="532" width="0.2268%" height="15" fill="rgb(246,101,22)" fg:x="327" fg:w="1"/><text x="74.3997%" y="542.50"></text></g><g><title>_process_draw_callback (gacha\views\gacha\unified_draw_view.py:60) (3 samples, 0.68%)</title><rect x="73.9229%" y="228" width="0.6803%" height="15" fill="rgb(206,107,13)" fg:x="326" fg:w="3"/><text x="74.1729%" y="238.50"></text></g><g><title>defer (discord\interactions.py:844) (3 samples, 0.68%)</title><rect x="73.9229%" y="244" width="0.6803%" height="15" fill="rgb(250,100,44)" fg:x="326" fg:w="3"/><text x="74.1729%" y="254.50"></text></g><g><title>request (discord\webhook\async_.py:182) (3 samples, 0.68%)</title><rect x="73.9229%" y="260" width="0.6803%" height="15" fill="rgb(231,147,38)" fg:x="326" fg:w="3"/><text x="74.1729%" y="270.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (3 samples, 0.68%)</title><rect x="73.9229%" y="276" width="0.6803%" height="15" fill="rgb(229,8,40)" fg:x="326" fg:w="3"/><text x="74.1729%" y="286.50"></text></g><g><title>_request (aiohttp\client.py:770) (3 samples, 0.68%)</title><rect x="73.9229%" y="292" width="0.6803%" height="15" fill="rgb(221,135,30)" fg:x="326" fg:w="3"/><text x="74.1729%" y="302.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:725) (3 samples, 0.68%)</title><rect x="73.9229%" y="308" width="0.6803%" height="15" fill="rgb(249,193,18)" fg:x="326" fg:w="3"/><text x="74.1729%" y="318.50"></text></g><g><title>connect (aiohttp\connector.py:645) (1 samples, 0.23%)</title><rect x="74.3764%" y="324" width="0.2268%" height="15" fill="rgb(209,133,39)" fg:x="328" fg:w="1"/><text x="74.6264%" y="334.50"></text></g><g><title>__init__ (__init__) (1 samples, 0.23%)</title><rect x="74.3764%" y="340" width="0.2268%" height="15" fill="rgb(232,100,14)" fg:x="328" fg:w="1"/><text x="74.6264%" y="350.50"></text></g><g><title>__init__ (aiohttp\connector.py:162) (1 samples, 0.23%)</title><rect x="74.3764%" y="356" width="0.2268%" height="15" fill="rgb(224,185,1)" fg:x="328" fg:w="1"/><text x="74.6264%" y="366.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="74.3764%" y="372" width="0.2268%" height="15" fill="rgb(223,139,8)" fg:x="328" fg:w="1"/><text x="74.6264%" y="382.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="74.3764%" y="388" width="0.2268%" height="15" fill="rgb(232,213,38)" fg:x="328" fg:w="1"/><text x="74.6264%" y="398.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:476) (1 samples, 0.23%)</title><rect x="74.3764%" y="404" width="0.2268%" height="15" fill="rgb(207,94,22)" fg:x="328" fg:w="1"/><text x="74.6264%" y="414.50"></text></g><g><title>extended_frame_gen (traceback.py:446) (1 samples, 0.23%)</title><rect x="74.3764%" y="420" width="0.2268%" height="15" fill="rgb(219,183,54)" fg:x="328" fg:w="1"/><text x="74.6264%" y="430.50"></text></g><g><title>walk_stack (traceback.py:389) (1 samples, 0.23%)</title><rect x="74.3764%" y="436" width="0.2268%" height="15" fill="rgb(216,185,54)" fg:x="328" fg:w="1"/><text x="74.6264%" y="446.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:101) (1 samples, 0.23%)</title><rect x="74.6032%" y="292" width="0.2268%" height="15" fill="rgb(254,217,39)" fg:x="329" fg:w="1"/><text x="74.8532%" y="302.50"></text></g><g><title>determine_cards_to_draw (gacha\services\draw_engine_service.py:165) (1 samples, 0.23%)</title><rect x="74.6032%" y="308" width="0.2268%" height="15" fill="rgb(240,178,23)" fg:x="329" fg:w="1"/><text x="74.8532%" y="318.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:58) (1 samples, 0.23%)</title><rect x="74.6032%" y="324" width="0.2268%" height="15" fill="rgb(218,11,47)" fg:x="329" fg:w="1"/><text x="74.8532%" y="334.50"></text></g><g><title>__call__ (enum.py:726) (1 samples, 0.23%)</title><rect x="74.6032%" y="340" width="0.2268%" height="15" fill="rgb(218,51,51)" fg:x="329" fg:w="1"/><text x="74.8532%" y="350.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:119) (1 samples, 0.23%)</title><rect x="74.8299%" y="292" width="0.2268%" height="15" fill="rgb(238,126,27)" fg:x="330" fg:w="1"/><text x="75.0799%" y="302.50"></text></g><g><title>_execute_draw_transaction (gacha\services\gacha_service.py:167) (1 samples, 0.23%)</title><rect x="74.8299%" y="308" width="0.2268%" height="15" fill="rgb(249,202,22)" fg:x="330" fg:w="1"/><text x="75.0799%" y="318.50"></text></g><g><title>award_oil (gacha\services\economy_service.py:578) (1 samples, 0.23%)</title><rect x="74.8299%" y="324" width="0.2268%" height="15" fill="rgb(254,195,49)" fg:x="330" fg:w="1"/><text x="75.0799%" y="334.50"></text></g><g><title>award_balance (gacha\services\user_service.py:158) (1 samples, 0.23%)</title><rect x="74.8299%" y="340" width="0.2268%" height="15" fill="rgb(208,123,14)" fg:x="330" fg:w="1"/><text x="75.0799%" y="350.50"></text></g><g><title>_logic_with_transaction (gacha\services\user_service.py:147) (1 samples, 0.23%)</title><rect x="74.8299%" y="356" width="0.2268%" height="15" fill="rgb(224,200,8)" fg:x="330" fg:w="1"/><text x="75.0799%" y="366.50"></text></g><g><title>_award_balance_logic (gacha\services\user_service.py:176) (1 samples, 0.23%)</title><rect x="74.8299%" y="372" width="0.2268%" height="15" fill="rgb(217,61,36)" fg:x="330" fg:w="1"/><text x="75.0799%" y="382.50"></text></g><g><title>update_balance (gacha\repositories\user\user_repository.py:161) (1 samples, 0.23%)</title><rect x="74.8299%" y="388" width="0.2268%" height="15" fill="rgb(206,35,45)" fg:x="330" fg:w="1"/><text x="75.0799%" y="398.50"></text></g><g><title>execute_query (gacha\repositories\_base_repo.py:38) (1 samples, 0.23%)</title><rect x="74.8299%" y="404" width="0.2268%" height="15" fill="rgb(217,65,33)" fg:x="330" fg:w="1"/><text x="75.0799%" y="414.50"></text></g><g><title>execute (asyncpg\connection.py:352) (1 samples, 0.23%)</title><rect x="74.8299%" y="420" width="0.2268%" height="15" fill="rgb(222,158,48)" fg:x="330" fg:w="1"/><text x="75.0799%" y="430.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="74.8299%" y="436" width="0.2268%" height="15" fill="rgb(254,2,54)" fg:x="330" fg:w="1"/><text x="75.0799%" y="446.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="74.8299%" y="452" width="0.2268%" height="15" fill="rgb(250,143,38)" fg:x="330" fg:w="1"/><text x="75.0799%" y="462.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="74.8299%" y="468" width="0.2268%" height="15" fill="rgb(248,25,0)" fg:x="330" fg:w="1"/><text x="75.0799%" y="478.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="74.8299%" y="484" width="0.2268%" height="15" fill="rgb(206,152,27)" fg:x="330" fg:w="1"/><text x="75.0799%" y="494.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (1 samples, 0.23%)</title><rect x="74.8299%" y="500" width="0.2268%" height="15" fill="rgb(240,77,30)" fg:x="330" fg:w="1"/><text x="75.0799%" y="510.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="74.8299%" y="516" width="0.2268%" height="15" fill="rgb(231,5,3)" fg:x="330" fg:w="1"/><text x="75.0799%" y="526.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="74.8299%" y="532" width="0.2268%" height="15" fill="rgb(207,226,32)" fg:x="330" fg:w="1"/><text x="75.0799%" y="542.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="74.8299%" y="548" width="0.2268%" height="15" fill="rgb(222,207,47)" fg:x="330" fg:w="1"/><text x="75.0799%" y="558.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="74.8299%" y="564" width="0.2268%" height="15" fill="rgb(229,115,45)" fg:x="330" fg:w="1"/><text x="75.0799%" y="574.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="74.8299%" y="580" width="0.2268%" height="15" fill="rgb(224,191,6)" fg:x="330" fg:w="1"/><text x="75.0799%" y="590.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="74.8299%" y="596" width="0.2268%" height="15" fill="rgb(230,227,24)" fg:x="330" fg:w="1"/><text x="75.0799%" y="606.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="74.8299%" y="612" width="0.2268%" height="15" fill="rgb(228,80,19)" fg:x="330" fg:w="1"/><text x="75.0799%" y="622.50"></text></g><g><title>continue_multi_draw_button (gacha\views\gacha\unified_draw_view.py:368) (6 samples, 1.36%)</title><rect x="73.9229%" y="212" width="1.3605%" height="15" fill="rgb(247,229,0)" fg:x="326" fg:w="6"/><text x="74.1729%" y="222.50"></text></g><g><title>_process_draw_callback (gacha\views\gacha\unified_draw_view.py:62) (3 samples, 0.68%)</title><rect x="74.6032%" y="228" width="0.6803%" height="15" fill="rgb(237,194,15)" fg:x="329" fg:w="3"/><text x="74.8532%" y="238.50"></text></g><g><title>_handle_draw_callback (draw_cog.py:193) (3 samples, 0.68%)</title><rect x="74.6032%" y="244" width="0.6803%" height="15" fill="rgb(219,203,20)" fg:x="329" fg:w="3"/><text x="74.8532%" y="254.50"></text></g><g><title>_execute_draw_operation (draw_cog.py:132) (3 samples, 0.68%)</title><rect x="74.6032%" y="260" width="0.6803%" height="15" fill="rgb(234,128,8)" fg:x="329" fg:w="3"/><text x="74.8532%" y="270.50"></text></g><g><title>draw_multiple (gacha\services\gacha_service.py:44) (3 samples, 0.68%)</title><rect x="74.6032%" y="276" width="0.6803%" height="15" fill="rgb(248,202,8)" fg:x="329" fg:w="3"/><text x="74.8532%" y="286.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:137) (1 samples, 0.23%)</title><rect x="75.0567%" y="292" width="0.2268%" height="15" fill="rgb(206,104,37)" fg:x="331" fg:w="1"/><text x="75.3067%" y="302.50"></text></g><g><title>_enrich_drawn_cards_info (gacha\services\gacha_service.py:337) (1 samples, 0.23%)</title><rect x="75.0567%" y="308" width="0.2268%" height="15" fill="rgb(223,8,27)" fg:x="331" fg:w="1"/><text x="75.3067%" y="318.50"></text></g><g><title>get_card_owner_counts_batch (gacha\repositories\collection\user_collection_repository.py:1168) (1 samples, 0.23%)</title><rect x="75.0567%" y="324" width="0.2268%" height="15" fill="rgb(216,217,28)" fg:x="331" fg:w="1"/><text x="75.3067%" y="334.50"></text></g><g><title>_fetch_owner_counts_from_db (gacha\repositories\collection\user_collection_repository.py:1111) (1 samples, 0.23%)</title><rect x="75.0567%" y="340" width="0.2268%" height="15" fill="rgb(249,199,1)" fg:x="331" fg:w="1"/><text x="75.3067%" y="350.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:112) (1 samples, 0.23%)</title><rect x="75.0567%" y="356" width="0.2268%" height="15" fill="rgb(240,85,17)" fg:x="331" fg:w="1"/><text x="75.3067%" y="366.50"></text></g><g><title>fetch (asyncpg\connection.py:690) (1 samples, 0.23%)</title><rect x="75.0567%" y="372" width="0.2268%" height="15" fill="rgb(206,108,45)" fg:x="331" fg:w="1"/><text x="75.3067%" y="382.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="75.0567%" y="388" width="0.2268%" height="15" fill="rgb(245,210,41)" fg:x="331" fg:w="1"/><text x="75.3067%" y="398.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="75.0567%" y="404" width="0.2268%" height="15" fill="rgb(206,13,37)" fg:x="331" fg:w="1"/><text x="75.3067%" y="414.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="75.0567%" y="420" width="0.2268%" height="15" fill="rgb(250,61,18)" fg:x="331" fg:w="1"/><text x="75.3067%" y="430.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="75.0567%" y="436" width="0.2268%" height="15" fill="rgb(235,172,48)" fg:x="331" fg:w="1"/><text x="75.3067%" y="446.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (1 samples, 0.23%)</title><rect x="75.0567%" y="452" width="0.2268%" height="15" fill="rgb(249,201,17)" fg:x="331" fg:w="1"/><text x="75.3067%" y="462.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="75.0567%" y="468" width="0.2268%" height="15" fill="rgb(219,208,6)" fg:x="331" fg:w="1"/><text x="75.3067%" y="478.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="75.0567%" y="484" width="0.2268%" height="15" fill="rgb(248,31,23)" fg:x="331" fg:w="1"/><text x="75.3067%" y="494.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="75.0567%" y="500" width="0.2268%" height="15" fill="rgb(245,15,42)" fg:x="331" fg:w="1"/><text x="75.3067%" y="510.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="75.0567%" y="516" width="0.2268%" height="15" fill="rgb(222,217,39)" fg:x="331" fg:w="1"/><text x="75.3067%" y="526.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="75.0567%" y="532" width="0.2268%" height="15" fill="rgb(210,219,27)" fg:x="331" fg:w="1"/><text x="75.3067%" y="542.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="75.0567%" y="548" width="0.2268%" height="15" fill="rgb(252,166,36)" fg:x="331" fg:w="1"/><text x="75.3067%" y="558.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="75.0567%" y="564" width="0.2268%" height="15" fill="rgb(245,132,34)" fg:x="331" fg:w="1"/><text x="75.3067%" y="574.50"></text></g><g><title>determine_cards_to_draw (gacha\services\draw_engine_service.py:158) (1 samples, 0.23%)</title><rect x="75.2834%" y="308" width="0.2268%" height="15" fill="rgb(236,54,3)" fg:x="332" fg:w="1"/><text x="75.5334%" y="318.50"></text></g><g><title>_get_user_wish_info (gacha\services\draw_engine_service.py:226) (1 samples, 0.23%)</title><rect x="75.2834%" y="324" width="0.2268%" height="15" fill="rgb(241,173,43)" fg:x="332" fg:w="1"/><text x="75.5334%" y="334.50"></text></g><g><title>get_user (gacha\services\user_service.py:46) (1 samples, 0.23%)</title><rect x="75.2834%" y="340" width="0.2268%" height="15" fill="rgb(215,190,9)" fg:x="332" fg:w="1"/><text x="75.5334%" y="350.50"></text></g><g><title>get_user_optional (gacha\repositories\user\user_repository.py:55) (1 samples, 0.23%)</title><rect x="75.2834%" y="356" width="0.2268%" height="15" fill="rgb(242,101,16)" fg:x="332" fg:w="1"/><text x="75.5334%" y="366.50"></text></g><g><title>get_user (gacha\repositories\user\user_repository.py:33) (1 samples, 0.23%)</title><rect x="75.2834%" y="372" width="0.2268%" height="15" fill="rgb(223,190,21)" fg:x="332" fg:w="1"/><text x="75.5334%" y="382.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:157) (1 samples, 0.23%)</title><rect x="75.2834%" y="388" width="0.2268%" height="15" fill="rgb(215,228,25)" fg:x="332" fg:w="1"/><text x="75.5334%" y="398.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (1 samples, 0.23%)</title><rect x="75.2834%" y="404" width="0.2268%" height="15" fill="rgb(225,36,22)" fg:x="332" fg:w="1"/><text x="75.5334%" y="414.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="75.2834%" y="420" width="0.2268%" height="15" fill="rgb(251,106,46)" fg:x="332" fg:w="1"/><text x="75.5334%" y="430.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="75.2834%" y="436" width="0.2268%" height="15" fill="rgb(208,90,1)" fg:x="332" fg:w="1"/><text x="75.5334%" y="446.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="75.2834%" y="452" width="0.2268%" height="15" fill="rgb(243,10,4)" fg:x="332" fg:w="1"/><text x="75.5334%" y="462.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="75.2834%" y="468" width="0.2268%" height="15" fill="rgb(212,137,27)" fg:x="332" fg:w="1"/><text x="75.5334%" y="478.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="75.2834%" y="484" width="0.2268%" height="15" fill="rgb(231,220,49)" fg:x="332" fg:w="1"/><text x="75.5334%" y="494.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="75.2834%" y="500" width="0.2268%" height="15" fill="rgb(237,96,20)" fg:x="332" fg:w="1"/><text x="75.5334%" y="510.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="75.2834%" y="516" width="0.2268%" height="15" fill="rgb(239,229,30)" fg:x="332" fg:w="1"/><text x="75.5334%" y="526.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="75.2834%" y="532" width="0.2268%" height="15" fill="rgb(219,65,33)" fg:x="332" fg:w="1"/><text x="75.5334%" y="542.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="75.2834%" y="548" width="0.2268%" height="15" fill="rgb(243,134,7)" fg:x="332" fg:w="1"/><text x="75.5334%" y="558.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="75.2834%" y="564" width="0.2268%" height="15" fill="rgb(216,177,54)" fg:x="332" fg:w="1"/><text x="75.5334%" y="574.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="75.2834%" y="580" width="0.2268%" height="15" fill="rgb(211,160,20)" fg:x="332" fg:w="1"/><text x="75.5334%" y="590.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="75.2834%" y="596" width="0.2268%" height="15" fill="rgb(239,85,39)" fg:x="332" fg:w="1"/><text x="75.5334%" y="606.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:55) (2 samples, 0.45%)</title><rect x="75.5102%" y="324" width="0.4535%" height="15" fill="rgb(232,125,22)" fg:x="333" fg:w="2"/><text x="75.7602%" y="334.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:58) (1 samples, 0.23%)</title><rect x="75.9637%" y="324" width="0.2268%" height="15" fill="rgb(244,57,34)" fg:x="335" fg:w="1"/><text x="76.2137%" y="334.50"></text></g><g><title>__call__ (enum.py:726) (1 samples, 0.23%)</title><rect x="75.9637%" y="340" width="0.2268%" height="15" fill="rgb(214,203,32)" fg:x="335" fg:w="1"/><text x="76.2137%" y="350.50"></text></g><g><title>determine_cards_to_draw (gacha\services\draw_engine_service.py:165) (5 samples, 1.13%)</title><rect x="75.5102%" y="308" width="1.1338%" height="15" fill="rgb(207,58,43)" fg:x="333" fg:w="5"/><text x="75.7602%" y="318.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:64) (2 samples, 0.45%)</title><rect x="76.1905%" y="324" width="0.4535%" height="15" fill="rgb(215,193,15)" fg:x="336" fg:w="2"/><text x="76.4405%" y="334.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:101) (7 samples, 1.59%)</title><rect x="75.2834%" y="292" width="1.5873%" height="15" fill="rgb(232,15,44)" fg:x="332" fg:w="7"/><text x="75.5334%" y="302.50"></text></g><g><title>determine_cards_to_draw (gacha\services\draw_engine_service.py:183) (1 samples, 0.23%)</title><rect x="76.6440%" y="308" width="0.2268%" height="15" fill="rgb(212,3,48)" fg:x="338" fg:w="1"/><text x="76.8940%" y="318.50"></text></g><g><title>_select_card_from_prefetched_pool (gacha\services\draw_engine_service.py:114) (1 samples, 0.23%)</title><rect x="76.6440%" y="324" width="0.2268%" height="15" fill="rgb(218,128,7)" fg:x="338" fg:w="1"/><text x="76.8940%" y="334.50"></text></g><g><title>to_thread (asyncio\threads.py:25) (1 samples, 0.23%)</title><rect x="76.6440%" y="340" width="0.2268%" height="15" fill="rgb(226,216,39)" fg:x="338" fg:w="1"/><text x="76.8940%" y="350.50"></text></g><g><title>run_in_executor (asyncio\base_events.py:900) (1 samples, 0.23%)</title><rect x="76.6440%" y="356" width="0.2268%" height="15" fill="rgb(243,47,51)" fg:x="338" fg:w="1"/><text x="76.8940%" y="366.50"></text></g><g><title>wrap_future (asyncio\futures.py:414) (1 samples, 0.23%)</title><rect x="76.6440%" y="372" width="0.2268%" height="15" fill="rgb(241,183,40)" fg:x="338" fg:w="1"/><text x="76.8940%" y="382.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="76.6440%" y="388" width="0.2268%" height="15" fill="rgb(231,217,32)" fg:x="338" fg:w="1"/><text x="76.8940%" y="398.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="76.6440%" y="404" width="0.2268%" height="15" fill="rgb(229,61,38)" fg:x="338" fg:w="1"/><text x="76.8940%" y="414.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="76.6440%" y="420" width="0.2268%" height="15" fill="rgb(225,210,5)" fg:x="338" fg:w="1"/><text x="76.8940%" y="430.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="76.6440%" y="436" width="0.2268%" height="15" fill="rgb(231,79,45)" fg:x="338" fg:w="1"/><text x="76.8940%" y="446.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="76.6440%" y="452" width="0.2268%" height="15" fill="rgb(224,100,7)" fg:x="338" fg:w="1"/><text x="76.8940%" y="462.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:119) (1 samples, 0.23%)</title><rect x="76.8707%" y="292" width="0.2268%" height="15" fill="rgb(241,198,18)" fg:x="339" fg:w="1"/><text x="77.1207%" y="302.50"></text></g><g><title>_execute_draw_transaction (gacha\services\gacha_service.py:167) (1 samples, 0.23%)</title><rect x="76.8707%" y="308" width="0.2268%" height="15" fill="rgb(252,97,53)" fg:x="339" fg:w="1"/><text x="77.1207%" y="318.50"></text></g><g><title>award_oil (gacha\services\economy_service.py:578) (1 samples, 0.23%)</title><rect x="76.8707%" y="324" width="0.2268%" height="15" fill="rgb(220,88,7)" fg:x="339" fg:w="1"/><text x="77.1207%" y="334.50"></text></g><g><title>award_balance (gacha\services\user_service.py:158) (1 samples, 0.23%)</title><rect x="76.8707%" y="340" width="0.2268%" height="15" fill="rgb(213,176,14)" fg:x="339" fg:w="1"/><text x="77.1207%" y="350.50"></text></g><g><title>_logic_with_transaction (gacha\services\user_service.py:147) (1 samples, 0.23%)</title><rect x="76.8707%" y="356" width="0.2268%" height="15" fill="rgb(246,73,7)" fg:x="339" fg:w="1"/><text x="77.1207%" y="366.50"></text></g><g><title>_award_balance_logic (gacha\services\user_service.py:169) (1 samples, 0.23%)</title><rect x="76.8707%" y="372" width="0.2268%" height="15" fill="rgb(245,64,36)" fg:x="339" fg:w="1"/><text x="77.1207%" y="382.50"></text></g><g><title>get_user_for_update (gacha\repositories\user\user_repository.py:67) (1 samples, 0.23%)</title><rect x="76.8707%" y="388" width="0.2268%" height="15" fill="rgb(245,80,10)" fg:x="339" fg:w="1"/><text x="77.1207%" y="398.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (1 samples, 0.23%)</title><rect x="76.8707%" y="404" width="0.2268%" height="15" fill="rgb(232,107,50)" fg:x="339" fg:w="1"/><text x="77.1207%" y="414.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="76.8707%" y="420" width="0.2268%" height="15" fill="rgb(253,3,0)" fg:x="339" fg:w="1"/><text x="77.1207%" y="430.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="76.8707%" y="436" width="0.2268%" height="15" fill="rgb(212,99,53)" fg:x="339" fg:w="1"/><text x="77.1207%" y="446.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="76.8707%" y="452" width="0.2268%" height="15" fill="rgb(249,111,54)" fg:x="339" fg:w="1"/><text x="77.1207%" y="462.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="76.8707%" y="468" width="0.2268%" height="15" fill="rgb(249,55,30)" fg:x="339" fg:w="1"/><text x="77.1207%" y="478.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="76.8707%" y="484" width="0.2268%" height="15" fill="rgb(237,47,42)" fg:x="339" fg:w="1"/><text x="77.1207%" y="494.50"></text></g><g><title>send (asyncio\windows_events.py:540) (1 samples, 0.23%)</title><rect x="76.8707%" y="500" width="0.2268%" height="15" fill="rgb(211,20,18)" fg:x="339" fg:w="1"/><text x="77.1207%" y="510.50"></text></g><g><title>_register_with_iocp (asyncio\windows_events.py:708) (1 samples, 0.23%)</title><rect x="76.8707%" y="516" width="0.2268%" height="15" fill="rgb(231,203,46)" fg:x="339" fg:w="1"/><text x="77.1207%" y="526.50"></text></g><g><title>__contains__ (_weakrefset.py:80) (1 samples, 0.23%)</title><rect x="76.8707%" y="532" width="0.2268%" height="15" fill="rgb(237,142,3)" fg:x="339" fg:w="1"/><text x="77.1207%" y="542.50"></text></g><g><title>multi_draw_button (gacha\views\gacha\unified_draw_view.py:188) (9 samples, 2.04%)</title><rect x="75.2834%" y="212" width="2.0408%" height="15" fill="rgb(241,107,1)" fg:x="332" fg:w="9"/><text x="75.5334%" y="222.50">m..</text></g><g><title>_process_draw_callback (gacha\views\gacha\unified_draw_view.py:62) (9 samples, 2.04%)</title><rect x="75.2834%" y="228" width="2.0408%" height="15" fill="rgb(229,83,13)" fg:x="332" fg:w="9"/><text x="75.5334%" y="238.50">_..</text></g><g><title>_handle_draw_callback (draw_cog.py:193) (9 samples, 2.04%)</title><rect x="75.2834%" y="244" width="2.0408%" height="15" fill="rgb(241,91,40)" fg:x="332" fg:w="9"/><text x="75.5334%" y="254.50">_..</text></g><g><title>_execute_draw_operation (draw_cog.py:132) (9 samples, 2.04%)</title><rect x="75.2834%" y="260" width="2.0408%" height="15" fill="rgb(225,3,45)" fg:x="332" fg:w="9"/><text x="75.5334%" y="270.50">_..</text></g><g><title>draw_multiple (gacha\services\gacha_service.py:44) (9 samples, 2.04%)</title><rect x="75.2834%" y="276" width="2.0408%" height="15" fill="rgb(244,223,14)" fg:x="332" fg:w="9"/><text x="75.5334%" y="286.50">d..</text></g><g><title>draw_cards (gacha\services\gacha_service.py:137) (1 samples, 0.23%)</title><rect x="77.0975%" y="292" width="0.2268%" height="15" fill="rgb(224,124,37)" fg:x="340" fg:w="1"/><text x="77.3475%" y="302.50"></text></g><g><title>_enrich_drawn_cards_info (gacha\services\gacha_service.py:337) (1 samples, 0.23%)</title><rect x="77.0975%" y="308" width="0.2268%" height="15" fill="rgb(251,171,30)" fg:x="340" fg:w="1"/><text x="77.3475%" y="318.50"></text></g><g><title>get_card_owner_counts_batch (gacha\repositories\collection\user_collection_repository.py:1162) (1 samples, 0.23%)</title><rect x="77.0975%" y="324" width="0.2268%" height="15" fill="rgb(236,46,54)" fg:x="340" fg:w="1"/><text x="77.3475%" y="334.50"></text></g><g><title>_process_cached_owner_counts (gacha\repositories\collection\user_collection_repository.py:1077) (1 samples, 0.23%)</title><rect x="77.0975%" y="340" width="0.2268%" height="15" fill="rgb(245,213,5)" fg:x="340" fg:w="1"/><text x="77.3475%" y="350.50"></text></g><g><title>execute_command (redis\asyncio\client.py:672) (1 samples, 0.23%)</title><rect x="77.0975%" y="356" width="0.2268%" height="15" fill="rgb(230,144,27)" fg:x="340" fg:w="1"/><text x="77.3475%" y="366.50"></text></g><g><title>get_connection (redis\asyncio\connection.py:1141) (1 samples, 0.23%)</title><rect x="77.0975%" y="372" width="0.2268%" height="15" fill="rgb(220,86,6)" fg:x="340" fg:w="1"/><text x="77.3475%" y="382.50"></text></g><g><title>ensure_connection (redis\asyncio\connection.py:1180) (1 samples, 0.23%)</title><rect x="77.0975%" y="388" width="0.2268%" height="15" fill="rgb(240,20,13)" fg:x="340" fg:w="1"/><text x="77.3475%" y="398.50"></text></g><g><title>can_read_destructive (redis\asyncio\connection.py:561) (1 samples, 0.23%)</title><rect x="77.0975%" y="404" width="0.2268%" height="15" fill="rgb(217,89,34)" fg:x="340" fg:w="1"/><text x="77.3475%" y="414.50"></text></g><g><title>can_read_destructive (redis\_parsers\hiredis.py:240) (1 samples, 0.23%)</title><rect x="77.0975%" y="420" width="0.2268%" height="15" fill="rgb(229,13,5)" fg:x="340" fg:w="1"/><text x="77.3475%" y="430.50"></text></g><g><title>read_from_socket (redis\_parsers\hiredis.py:245) (1 samples, 0.23%)</title><rect x="77.0975%" y="436" width="0.2268%" height="15" fill="rgb(244,67,35)" fg:x="340" fg:w="1"/><text x="77.3475%" y="446.50"></text></g><g><title>read (asyncio\streams.py:730) (1 samples, 0.23%)</title><rect x="77.0975%" y="452" width="0.2268%" height="15" fill="rgb(221,40,2)" fg:x="340" fg:w="1"/><text x="77.3475%" y="462.50"></text></g><g><title>_wait_for_data (asyncio\streams.py:537) (1 samples, 0.23%)</title><rect x="77.0975%" y="468" width="0.2268%" height="15" fill="rgb(237,157,21)" fg:x="340" fg:w="1"/><text x="77.3475%" y="478.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="77.0975%" y="484" width="0.2268%" height="15" fill="rgb(222,94,11)" fg:x="340" fg:w="1"/><text x="77.3475%" y="494.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="77.0975%" y="500" width="0.2268%" height="15" fill="rgb(249,113,6)" fg:x="340" fg:w="1"/><text x="77.3475%" y="510.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="77.0975%" y="516" width="0.2268%" height="15" fill="rgb(238,137,36)" fg:x="340" fg:w="1"/><text x="77.3475%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="77.0975%" y="532" width="0.2268%" height="15" fill="rgb(210,102,26)" fg:x="340" fg:w="1"/><text x="77.3475%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="77.0975%" y="548" width="0.2268%" height="15" fill="rgb(218,30,30)" fg:x="340" fg:w="1"/><text x="77.3475%" y="558.50"></text></g><g><title>transfer_stats_page_button (auxiliary\cogs\health_cog.py:1672) (1 samples, 0.23%)</title><rect x="77.3243%" y="212" width="0.2268%" height="15" fill="rgb(214,67,26)" fg:x="341" fg:w="1"/><text x="77.5743%" y="222.50"></text></g><g><title>defer (discord\interactions.py:844) (1 samples, 0.23%)</title><rect x="77.3243%" y="228" width="0.2268%" height="15" fill="rgb(251,9,53)" fg:x="341" fg:w="1"/><text x="77.5743%" y="238.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="77.3243%" y="244" width="0.2268%" height="15" fill="rgb(228,204,25)" fg:x="341" fg:w="1"/><text x="77.5743%" y="254.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="77.3243%" y="260" width="0.2268%" height="15" fill="rgb(207,153,8)" fg:x="341" fg:w="1"/><text x="77.5743%" y="270.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="77.3243%" y="276" width="0.2268%" height="15" fill="rgb(242,9,16)" fg:x="341" fg:w="1"/><text x="77.5743%" y="286.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="77.3243%" y="292" width="0.2268%" height="15" fill="rgb(217,211,10)" fg:x="341" fg:w="1"/><text x="77.5743%" y="302.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1476) (1 samples, 0.23%)</title><rect x="77.3243%" y="308" width="0.2268%" height="15" fill="rgb(219,228,52)" fg:x="341" fg:w="1"/><text x="77.5743%" y="318.50"></text></g><g><title>write_bytes (aiohttp\client_reqrep.py:1374) (1 samples, 0.23%)</title><rect x="77.3243%" y="324" width="0.2268%" height="15" fill="rgb(231,92,29)" fg:x="341" fg:w="1"/><text x="77.5743%" y="334.50"></text></g><g><title>write_with_length (aiohttp\payload.py:425) (1 samples, 0.23%)</title><rect x="77.3243%" y="340" width="0.2268%" height="15" fill="rgb(232,8,23)" fg:x="341" fg:w="1"/><text x="77.5743%" y="350.50"></text></g><g><title>write (aiohttp\http_writer.py:194) (1 samples, 0.23%)</title><rect x="77.3243%" y="356" width="0.2268%" height="15" fill="rgb(216,211,34)" fg:x="341" fg:w="1"/><text x="77.5743%" y="366.50"></text></g><g><title>_send_headers_with_payload (aiohttp\http_writer.py:138) (1 samples, 0.23%)</title><rect x="77.3243%" y="372" width="0.2268%" height="15" fill="rgb(236,151,0)" fg:x="341" fg:w="1"/><text x="77.5743%" y="382.50"></text></g><g><title>_writelines (aiohttp\http_writer.py:108) (1 samples, 0.23%)</title><rect x="77.3243%" y="388" width="0.2268%" height="15" fill="rgb(209,168,3)" fg:x="341" fg:w="1"/><text x="77.5743%" y="398.50"></text></g><g><title>write (asyncio\sslproto.py:222) (1 samples, 0.23%)</title><rect x="77.3243%" y="404" width="0.2268%" height="15" fill="rgb(208,129,28)" fg:x="341" fg:w="1"/><text x="77.5743%" y="414.50"></text></g><g><title>_write_appdata (asyncio\sslproto.py:697) (1 samples, 0.23%)</title><rect x="77.3243%" y="420" width="0.2268%" height="15" fill="rgb(229,78,22)" fg:x="341" fg:w="1"/><text x="77.5743%" y="430.50"></text></g><g><title>_do_write (asyncio\sslproto.py:716) (1 samples, 0.23%)</title><rect x="77.3243%" y="436" width="0.2268%" height="15" fill="rgb(228,187,13)" fg:x="341" fg:w="1"/><text x="77.5743%" y="446.50"></text></g><g><title>_process_outgoing (asyncio\sslproto.py:722) (1 samples, 0.23%)</title><rect x="77.3243%" y="452" width="0.2268%" height="15" fill="rgb(240,119,24)" fg:x="341" fg:w="1"/><text x="77.5743%" y="462.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="77.3243%" y="468" width="0.2268%" height="15" fill="rgb(209,194,42)" fg:x="341" fg:w="1"/><text x="77.5743%" y="478.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="77.3243%" y="484" width="0.2268%" height="15" fill="rgb(247,200,46)" fg:x="341" fg:w="1"/><text x="77.5743%" y="494.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="77.3243%" y="500" width="0.2268%" height="15" fill="rgb(218,76,16)" fg:x="341" fg:w="1"/><text x="77.5743%" y="510.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="77.3243%" y="516" width="0.2268%" height="15" fill="rgb(225,21,48)" fg:x="341" fg:w="1"/><text x="77.5743%" y="526.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="77.3243%" y="532" width="0.2268%" height="15" fill="rgb(239,223,50)" fg:x="341" fg:w="1"/><text x="77.5743%" y="542.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="77.3243%" y="548" width="0.2268%" height="15" fill="rgb(244,45,21)" fg:x="341" fg:w="1"/><text x="77.5743%" y="558.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="77.3243%" y="564" width="0.2268%" height="15" fill="rgb(232,33,43)" fg:x="341" fg:w="1"/><text x="77.5743%" y="574.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="77.3243%" y="580" width="0.2268%" height="15" fill="rgb(209,8,3)" fg:x="341" fg:w="1"/><text x="77.5743%" y="590.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="77.3243%" y="596" width="0.2268%" height="15" fill="rgb(214,25,53)" fg:x="341" fg:w="1"/><text x="77.5743%" y="606.50"></text></g><g><title>_scheduled_task (discord\ui\view.py:435) (97 samples, 22.00%)</title><rect x="55.7823%" y="196" width="21.9955%" height="15" fill="rgb(254,186,54)" fg:x="246" fg:w="97"/><text x="56.0323%" y="206.50">_scheduled_task (discord\ui\view.py..</text></g><g><title>transfer_stats_page_button (auxiliary\cogs\health_cog.py:1679) (1 samples, 0.23%)</title><rect x="77.5510%" y="212" width="0.2268%" height="15" fill="rgb(208,174,49)" fg:x="342" fg:w="1"/><text x="77.8010%" y="222.50"></text></g><g><title>edit_original_response (discord\interactions.py:572) (1 samples, 0.23%)</title><rect x="77.5510%" y="228" width="0.2268%" height="15" fill="rgb(233,191,51)" fg:x="342" fg:w="1"/><text x="77.8010%" y="238.50"></text></g><g><title>store_view (discord\state.py:418) (1 samples, 0.23%)</title><rect x="77.5510%" y="244" width="0.2268%" height="15" fill="rgb(222,134,10)" fg:x="342" fg:w="1"/><text x="77.8010%" y="254.50"></text></g><g><title>add_view (discord\ui\view.py:572) (1 samples, 0.23%)</title><rect x="77.5510%" y="260" width="0.2268%" height="15" fill="rgb(230,226,20)" fg:x="342" fg:w="1"/><text x="77.8010%" y="270.50"></text></g><g><title>_start_listening_from_store (discord\ui\view.py:446) (1 samples, 0.23%)</title><rect x="77.5510%" y="276" width="0.2268%" height="15" fill="rgb(251,111,25)" fg:x="342" fg:w="1"/><text x="77.8010%" y="286.50"></text></g><g><title>create_task (asyncio\tasks.py:410) (1 samples, 0.23%)</title><rect x="77.5510%" y="292" width="0.2268%" height="15" fill="rgb(224,40,46)" fg:x="342" fg:w="1"/><text x="77.8010%" y="302.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="77.5510%" y="308" width="0.2268%" height="15" fill="rgb(236,108,47)" fg:x="342" fg:w="1"/><text x="77.8010%" y="318.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="77.5510%" y="324" width="0.2268%" height="15" fill="rgb(234,93,0)" fg:x="342" fg:w="1"/><text x="77.8010%" y="334.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="77.5510%" y="340" width="0.2268%" height="15" fill="rgb(224,213,32)" fg:x="342" fg:w="1"/><text x="77.8010%" y="350.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="77.5510%" y="356" width="0.2268%" height="15" fill="rgb(251,11,48)" fg:x="342" fg:w="1"/><text x="77.8010%" y="366.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="77.5510%" y="372" width="0.2268%" height="15" fill="rgb(236,173,5)" fg:x="342" fg:w="1"/><text x="77.8010%" y="382.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="77.5510%" y="388" width="0.2268%" height="15" fill="rgb(230,95,12)" fg:x="342" fg:w="1"/><text x="77.8010%" y="398.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="77.5510%" y="404" width="0.2268%" height="15" fill="rgb(232,209,1)" fg:x="342" fg:w="1"/><text x="77.8010%" y="414.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="77.5510%" y="420" width="0.2268%" height="15" fill="rgb(232,6,1)" fg:x="342" fg:w="1"/><text x="77.8010%" y="430.50"></text></g><g><title>_set_state (asyncio\futures.py:380) (1 samples, 0.23%)</title><rect x="77.7778%" y="196" width="0.2268%" height="15" fill="rgb(210,224,50)" fg:x="343" fg:w="1"/><text x="78.0278%" y="206.50"></text></g><g><title>_copy_future_state (asyncio\futures.py:359) (1 samples, 0.23%)</title><rect x="77.7778%" y="212" width="0.2268%" height="15" fill="rgb(228,127,35)" fg:x="343" fg:w="1"/><text x="78.0278%" y="222.50"></text></g><g><title>call_soon (asyncio\base_events.py:836) (1 samples, 0.23%)</title><rect x="77.7778%" y="228" width="0.2268%" height="15" fill="rgb(245,102,45)" fg:x="343" fg:w="1"/><text x="78.0278%" y="238.50"></text></g><g><title>_check_callback (asyncio\base_events.py:844) (1 samples, 0.23%)</title><rect x="77.7778%" y="244" width="0.2268%" height="15" fill="rgb(214,1,49)" fg:x="343" fg:w="1"/><text x="78.0278%" y="254.50"></text></g><g><title>iscoroutinefunction (asyncio\coroutines.py:22) (1 samples, 0.23%)</title><rect x="77.7778%" y="260" width="0.2268%" height="15" fill="rgb(226,163,40)" fg:x="343" fg:w="1"/><text x="78.0278%" y="270.50"></text></g><g><title>iscoroutinefunction (inspect.py:443) (1 samples, 0.23%)</title><rect x="77.7778%" y="276" width="0.2268%" height="15" fill="rgb(239,212,28)" fg:x="343" fg:w="1"/><text x="78.0278%" y="286.50"></text></g><g><title>_has_coroutine_mark (inspect.py:423) (1 samples, 0.23%)</title><rect x="77.7778%" y="292" width="0.2268%" height="15" fill="rgb(220,20,13)" fg:x="343" fg:w="1"/><text x="78.0278%" y="302.50"></text></g><g><title>_wrapped_set_result (tasks\__init__.py:119) (2 samples, 0.45%)</title><rect x="78.0045%" y="196" width="0.4535%" height="15" fill="rgb(210,164,35)" fg:x="344" fg:w="2"/><text x="78.2545%" y="206.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (2 samples, 0.45%)</title><rect x="78.0045%" y="212" width="0.4535%" height="15" fill="rgb(248,109,41)" fg:x="344" fg:w="2"/><text x="78.2545%" y="222.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (2 samples, 0.45%)</title><rect x="78.0045%" y="228" width="0.4535%" height="15" fill="rgb(238,23,50)" fg:x="344" fg:w="2"/><text x="78.2545%" y="238.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="78.0045%" y="244" width="0.4535%" height="15" fill="rgb(211,48,49)" fg:x="344" fg:w="2"/><text x="78.2545%" y="254.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="78.0045%" y="260" width="0.4535%" height="15" fill="rgb(223,36,21)" fg:x="344" fg:w="2"/><text x="78.2545%" y="270.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="78.2313%" y="276" width="0.2268%" height="15" fill="rgb(207,123,46)" fg:x="345" fg:w="1"/><text x="78.4813%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:481) (1 samples, 0.23%)</title><rect x="78.2313%" y="292" width="0.2268%" height="15" fill="rgb(240,218,32)" fg:x="345" fg:w="1"/><text x="78.4813%" y="302.50"></text></g><g><title>lazycache (linecache.py:198) (1 samples, 0.23%)</title><rect x="78.2313%" y="308" width="0.2268%" height="15" fill="rgb(252,5,43)" fg:x="345" fg:w="1"/><text x="78.4813%" y="318.50"></text></g><g><title>_write_draw_history (gacha\services\gacha_service.py:253) (1 samples, 0.23%)</title><rect x="78.4580%" y="196" width="0.2268%" height="15" fill="rgb(252,84,19)" fg:x="346" fg:w="1"/><text x="78.7080%" y="206.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="78.4580%" y="212" width="0.2268%" height="15" fill="rgb(243,152,39)" fg:x="346" fg:w="1"/><text x="78.7080%" y="222.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="78.4580%" y="228" width="0.2268%" height="15" fill="rgb(234,160,15)" fg:x="346" fg:w="1"/><text x="78.7080%" y="238.50"></text></g><g><title>shield (asyncio\tasks.py:951) (1 samples, 0.23%)</title><rect x="78.4580%" y="244" width="0.2268%" height="15" fill="rgb(237,34,20)" fg:x="346" fg:w="1"/><text x="78.7080%" y="254.50"></text></g><g><title>ensure_future (asyncio\tasks.py:748) (1 samples, 0.23%)</title><rect x="78.4580%" y="260" width="0.2268%" height="15" fill="rgb(229,97,13)" fg:x="346" fg:w="1"/><text x="78.7080%" y="270.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="78.4580%" y="276" width="0.2268%" height="15" fill="rgb(234,71,50)" fg:x="346" fg:w="1"/><text x="78.7080%" y="286.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="78.4580%" y="292" width="0.2268%" height="15" fill="rgb(253,155,4)" fg:x="346" fg:w="1"/><text x="78.7080%" y="302.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="78.4580%" y="308" width="0.2268%" height="15" fill="rgb(222,185,37)" fg:x="346" fg:w="1"/><text x="78.7080%" y="318.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="78.4580%" y="324" width="0.2268%" height="15" fill="rgb(251,177,13)" fg:x="346" fg:w="1"/><text x="78.7080%" y="334.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="78.4580%" y="340" width="0.2268%" height="15" fill="rgb(250,179,40)" fg:x="346" fg:w="1"/><text x="78.7080%" y="350.50"></text></g><g><title>_write_draw_history (gacha\services\gacha_service.py:254) (1 samples, 0.23%)</title><rect x="78.6848%" y="196" width="0.2268%" height="15" fill="rgb(242,44,2)" fg:x="347" fg:w="1"/><text x="78.9348%" y="206.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (1 samples, 0.23%)</title><rect x="78.6848%" y="212" width="0.2268%" height="15" fill="rgb(216,177,13)" fg:x="347" fg:w="1"/><text x="78.9348%" y="222.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="78.6848%" y="228" width="0.2268%" height="15" fill="rgb(216,106,43)" fg:x="347" fg:w="1"/><text x="78.9348%" y="238.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="78.6848%" y="244" width="0.2268%" height="15" fill="rgb(216,183,2)" fg:x="347" fg:w="1"/><text x="78.9348%" y="254.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="78.6848%" y="260" width="0.2268%" height="15" fill="rgb(249,75,3)" fg:x="347" fg:w="1"/><text x="78.9348%" y="270.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="78.6848%" y="276" width="0.2268%" height="15" fill="rgb(219,67,39)" fg:x="347" fg:w="1"/><text x="78.9348%" y="286.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="78.6848%" y="292" width="0.2268%" height="15" fill="rgb(253,228,2)" fg:x="347" fg:w="1"/><text x="78.9348%" y="302.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="78.6848%" y="308" width="0.2268%" height="15" fill="rgb(235,138,27)" fg:x="347" fg:w="1"/><text x="78.9348%" y="318.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="78.6848%" y="324" width="0.2268%" height="15" fill="rgb(236,97,51)" fg:x="347" fg:w="1"/><text x="78.9348%" y="334.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="78.6848%" y="340" width="0.2268%" height="15" fill="rgb(240,80,30)" fg:x="347" fg:w="1"/><text x="78.9348%" y="350.50"></text></g><g><title>_write_draw_history (gacha\services\gacha_service.py:284) (1 samples, 0.23%)</title><rect x="78.9116%" y="196" width="0.2268%" height="15" fill="rgb(230,178,19)" fg:x="348" fg:w="1"/><text x="79.1616%" y="206.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="78.9116%" y="212" width="0.2268%" height="15" fill="rgb(210,190,27)" fg:x="348" fg:w="1"/><text x="79.1616%" y="222.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="78.9116%" y="228" width="0.2268%" height="15" fill="rgb(222,107,31)" fg:x="348" fg:w="1"/><text x="79.1616%" y="238.50"></text></g><g><title>shield (asyncio\tasks.py:956) (1 samples, 0.23%)</title><rect x="78.9116%" y="244" width="0.2268%" height="15" fill="rgb(216,127,34)" fg:x="348" fg:w="1"/><text x="79.1616%" y="254.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="78.9116%" y="260" width="0.2268%" height="15" fill="rgb(234,116,52)" fg:x="348" fg:w="1"/><text x="79.1616%" y="270.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="78.9116%" y="276" width="0.2268%" height="15" fill="rgb(222,124,15)" fg:x="348" fg:w="1"/><text x="79.1616%" y="286.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="78.9116%" y="292" width="0.2268%" height="15" fill="rgb(231,179,28)" fg:x="348" fg:w="1"/><text x="79.1616%" y="302.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="78.9116%" y="308" width="0.2268%" height="15" fill="rgb(226,93,45)" fg:x="348" fg:w="1"/><text x="79.1616%" y="318.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="78.9116%" y="324" width="0.2268%" height="15" fill="rgb(215,8,51)" fg:x="348" fg:w="1"/><text x="79.1616%" y="334.50"></text></g><g><title>checkcache (linecache.py:74) (1 samples, 0.23%)</title><rect x="79.1383%" y="292" width="0.2268%" height="15" fill="rgb(223,106,5)" fg:x="349" fg:w="1"/><text x="79.3883%" y="302.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (4 samples, 0.91%)</title><rect x="79.1383%" y="196" width="0.9070%" height="15" fill="rgb(250,191,5)" fg:x="349" fg:w="4"/><text x="79.3883%" y="206.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (4 samples, 0.91%)</title><rect x="79.1383%" y="212" width="0.9070%" height="15" fill="rgb(242,132,44)" fg:x="349" fg:w="4"/><text x="79.3883%" y="222.50"></text></g><g><title>__init__ (asyncio\events.py:47) (4 samples, 0.91%)</title><rect x="79.1383%" y="228" width="0.9070%" height="15" fill="rgb(251,152,29)" fg:x="349" fg:w="4"/><text x="79.3883%" y="238.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (4 samples, 0.91%)</title><rect x="79.1383%" y="244" width="0.9070%" height="15" fill="rgb(218,179,5)" fg:x="349" fg:w="4"/><text x="79.3883%" y="254.50"></text></g><g><title>extract (traceback.py:449) (4 samples, 0.91%)</title><rect x="79.1383%" y="260" width="0.9070%" height="15" fill="rgb(227,67,19)" fg:x="349" fg:w="4"/><text x="79.3883%" y="270.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (4 samples, 0.91%)</title><rect x="79.1383%" y="276" width="0.9070%" height="15" fill="rgb(233,119,31)" fg:x="349" fg:w="4"/><text x="79.3883%" y="286.50"></text></g><g><title>checkcache (linecache.py:94) (3 samples, 0.68%)</title><rect x="79.3651%" y="292" width="0.6803%" height="15" fill="rgb(241,120,22)" fg:x="350" fg:w="3"/><text x="79.6151%" y="302.50"></text></g><g><title>reset (asyncpg\connection.py:1558) (1 samples, 0.23%)</title><rect x="80.0454%" y="212" width="0.2268%" height="15" fill="rgb(224,102,30)" fg:x="353" fg:w="1"/><text x="80.2954%" y="222.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (5 samples, 1.13%)</title><rect x="80.2721%" y="292" width="1.1338%" height="15" fill="rgb(210,164,37)" fg:x="354" fg:w="5"/><text x="80.5221%" y="302.50"></text></g><g><title>checkcache (linecache.py:94) (5 samples, 1.13%)</title><rect x="80.2721%" y="308" width="1.1338%" height="15" fill="rgb(226,191,16)" fg:x="354" fg:w="5"/><text x="80.5221%" y="318.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (6 samples, 1.36%)</title><rect x="80.2721%" y="244" width="1.3605%" height="15" fill="rgb(214,40,45)" fg:x="354" fg:w="6"/><text x="80.5221%" y="254.50"></text></g><g><title>extract_stack (traceback.py:260) (6 samples, 1.36%)</title><rect x="80.2721%" y="260" width="1.3605%" height="15" fill="rgb(244,29,26)" fg:x="354" fg:w="6"/><text x="80.5221%" y="270.50"></text></g><g><title>extract (traceback.py:449) (6 samples, 1.36%)</title><rect x="80.2721%" y="276" width="1.3605%" height="15" fill="rgb(216,16,5)" fg:x="354" fg:w="6"/><text x="80.5221%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="81.4059%" y="292" width="0.2268%" height="15" fill="rgb(249,76,35)" fg:x="359" fg:w="1"/><text x="81.6559%" y="302.50"></text></g><g><title>line (traceback.py:373) (1 samples, 0.23%)</title><rect x="81.4059%" y="308" width="0.2268%" height="15" fill="rgb(207,11,44)" fg:x="359" fg:w="1"/><text x="81.6559%" y="318.50"></text></g><g><title>_set_lines (traceback.py:349) (1 samples, 0.23%)</title><rect x="81.4059%" y="324" width="0.2268%" height="15" fill="rgb(228,190,49)" fg:x="359" fg:w="1"/><text x="81.6559%" y="334.50"></text></g><g><title>send (asyncio\windows_events.py:543) (1 samples, 0.23%)</title><rect x="81.6327%" y="276" width="0.2268%" height="15" fill="rgb(214,173,12)" fg:x="360" fg:w="1"/><text x="81.8827%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:481) (1 samples, 0.23%)</title><rect x="81.8594%" y="356" width="0.2268%" height="15" fill="rgb(218,26,35)" fg:x="361" fg:w="1"/><text x="82.1094%" y="366.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (5 samples, 1.13%)</title><rect x="81.6327%" y="260" width="1.1338%" height="15" fill="rgb(220,200,19)" fg:x="360" fg:w="5"/><text x="81.8827%" y="270.50"></text></g><g><title>send (asyncio\windows_events.py:547) (4 samples, 0.91%)</title><rect x="81.8594%" y="276" width="0.9070%" height="15" fill="rgb(239,95,49)" fg:x="361" fg:w="4"/><text x="82.1094%" y="286.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (4 samples, 0.91%)</title><rect x="81.8594%" y="292" width="0.9070%" height="15" fill="rgb(235,85,53)" fg:x="361" fg:w="4"/><text x="82.1094%" y="302.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (4 samples, 0.91%)</title><rect x="81.8594%" y="308" width="0.9070%" height="15" fill="rgb(233,133,31)" fg:x="361" fg:w="4"/><text x="82.1094%" y="318.50"></text></g><g><title>extract_stack (traceback.py:260) (4 samples, 0.91%)</title><rect x="81.8594%" y="324" width="0.9070%" height="15" fill="rgb(218,25,20)" fg:x="361" fg:w="4"/><text x="82.1094%" y="334.50"></text></g><g><title>extract (traceback.py:449) (4 samples, 0.91%)</title><rect x="81.8594%" y="340" width="0.9070%" height="15" fill="rgb(252,210,38)" fg:x="361" fg:w="4"/><text x="82.1094%" y="350.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (3 samples, 0.68%)</title><rect x="82.0862%" y="356" width="0.6803%" height="15" fill="rgb(242,134,21)" fg:x="362" fg:w="3"/><text x="82.3362%" y="366.50"></text></g><g><title>checkcache (linecache.py:94) (3 samples, 0.68%)</title><rect x="82.0862%" y="372" width="0.6803%" height="15" fill="rgb(213,28,48)" fg:x="362" fg:w="3"/><text x="82.3362%" y="382.50"></text></g><g><title>release (asyncpg\pool.py:218) (17 samples, 3.85%)</title><rect x="80.0454%" y="196" width="3.8549%" height="15" fill="rgb(250,196,2)" fg:x="353" fg:w="17"/><text x="80.2954%" y="206.50">rele..</text></g><g><title>reset (asyncpg\connection.py:1562) (16 samples, 3.63%)</title><rect x="80.2721%" y="212" width="3.6281%" height="15" fill="rgb(227,5,17)" fg:x="354" fg:w="16"/><text x="80.5221%" y="222.50">rese..</text></g><g><title>execute (asyncpg\connection.py:349) (16 samples, 3.63%)</title><rect x="80.2721%" y="228" width="3.6281%" height="15" fill="rgb(221,226,24)" fg:x="354" fg:w="16"/><text x="80.5221%" y="238.50">exec..</text></g><g><title>write (asyncio\proactor_events.py:366) (10 samples, 2.27%)</title><rect x="81.6327%" y="244" width="2.2676%" height="15" fill="rgb(211,5,48)" fg:x="360" fg:w="10"/><text x="81.8827%" y="254.50">w..</text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (5 samples, 1.13%)</title><rect x="82.7664%" y="260" width="1.1338%" height="15" fill="rgb(219,150,6)" fg:x="365" fg:w="5"/><text x="83.0164%" y="270.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (5 samples, 1.13%)</title><rect x="82.7664%" y="276" width="1.1338%" height="15" fill="rgb(251,46,16)" fg:x="365" fg:w="5"/><text x="83.0164%" y="286.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (5 samples, 1.13%)</title><rect x="82.7664%" y="292" width="1.1338%" height="15" fill="rgb(220,204,40)" fg:x="365" fg:w="5"/><text x="83.0164%" y="302.50"></text></g><g><title>__init__ (asyncio\events.py:47) (5 samples, 1.13%)</title><rect x="82.7664%" y="308" width="1.1338%" height="15" fill="rgb(211,85,2)" fg:x="365" fg:w="5"/><text x="83.0164%" y="318.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (5 samples, 1.13%)</title><rect x="82.7664%" y="324" width="1.1338%" height="15" fill="rgb(229,17,7)" fg:x="365" fg:w="5"/><text x="83.0164%" y="334.50"></text></g><g><title>extract (traceback.py:449) (5 samples, 1.13%)</title><rect x="82.7664%" y="340" width="1.1338%" height="15" fill="rgb(239,72,28)" fg:x="365" fg:w="5"/><text x="83.0164%" y="350.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (5 samples, 1.13%)</title><rect x="82.7664%" y="356" width="1.1338%" height="15" fill="rgb(230,47,54)" fg:x="365" fg:w="5"/><text x="83.0164%" y="366.50"></text></g><g><title>checkcache (linecache.py:94) (5 samples, 1.13%)</title><rect x="82.7664%" y="372" width="1.1338%" height="15" fill="rgb(214,50,8)" fg:x="365" fg:w="5"/><text x="83.0164%" y="382.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:477) (1 samples, 0.23%)</title><rect x="83.9002%" y="324" width="0.2268%" height="15" fill="rgb(216,198,43)" fg:x="370" fg:w="1"/><text x="84.1502%" y="334.50"></text></g><g><title>checkcache (linecache.py:90) (1 samples, 0.23%)</title><rect x="84.1270%" y="340" width="0.2268%" height="15" fill="rgb(234,20,35)" fg:x="371" fg:w="1"/><text x="84.3770%" y="350.50"></text></g><g><title>release (asyncpg\pool.py:235) (6 samples, 1.36%)</title><rect x="83.9002%" y="196" width="1.3605%" height="15" fill="rgb(254,45,19)" fg:x="370" fg:w="6"/><text x="84.1502%" y="206.50"></text></g><g><title>_setup_inactive_callback (asyncpg\pool.py:262) (6 samples, 1.36%)</title><rect x="83.9002%" y="212" width="1.3605%" height="15" fill="rgb(219,14,44)" fg:x="370" fg:w="6"/><text x="84.1502%" y="222.50"></text></g><g><title>call_later (asyncio\base_events.py:799) (6 samples, 1.36%)</title><rect x="83.9002%" y="228" width="1.3605%" height="15" fill="rgb(217,220,26)" fg:x="370" fg:w="6"/><text x="84.1502%" y="238.50"></text></g><g><title>call_at (asyncio\base_events.py:816) (6 samples, 1.36%)</title><rect x="83.9002%" y="244" width="1.3605%" height="15" fill="rgb(213,158,28)" fg:x="370" fg:w="6"/><text x="84.1502%" y="254.50"></text></g><g><title>__init__ (asyncio\events.py:114) (6 samples, 1.36%)</title><rect x="83.9002%" y="260" width="1.3605%" height="15" fill="rgb(252,51,52)" fg:x="370" fg:w="6"/><text x="84.1502%" y="270.50"></text></g><g><title>__init__ (asyncio\events.py:47) (6 samples, 1.36%)</title><rect x="83.9002%" y="276" width="1.3605%" height="15" fill="rgb(246,89,16)" fg:x="370" fg:w="6"/><text x="84.1502%" y="286.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (6 samples, 1.36%)</title><rect x="83.9002%" y="292" width="1.3605%" height="15" fill="rgb(216,158,49)" fg:x="370" fg:w="6"/><text x="84.1502%" y="302.50"></text></g><g><title>extract (traceback.py:449) (6 samples, 1.36%)</title><rect x="83.9002%" y="308" width="1.3605%" height="15" fill="rgb(236,107,19)" fg:x="370" fg:w="6"/><text x="84.1502%" y="318.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (5 samples, 1.13%)</title><rect x="84.1270%" y="324" width="1.1338%" height="15" fill="rgb(228,185,30)" fg:x="371" fg:w="5"/><text x="84.3770%" y="334.50"></text></g><g><title>checkcache (linecache.py:94) (4 samples, 0.91%)</title><rect x="84.3537%" y="340" width="0.9070%" height="15" fill="rgb(246,134,8)" fg:x="372" fg:w="4"/><text x="84.6037%" y="350.50"></text></g><g><title>send_heartbeat (discord\gateway.py:671) (1 samples, 0.23%)</title><rect x="85.2608%" y="196" width="0.2268%" height="15" fill="rgb(214,143,50)" fg:x="376" fg:w="1"/><text x="85.5108%" y="206.50"></text></g><g><title>send_str (aiohttp\client_ws.py:245) (1 samples, 0.23%)</title><rect x="85.2608%" y="212" width="0.2268%" height="15" fill="rgb(228,75,8)" fg:x="376" fg:w="1"/><text x="85.5108%" y="222.50"></text></g><g><title>send_frame (aiohttp\_websocket\writer.py:139) (1 samples, 0.23%)</title><rect x="85.2608%" y="228" width="0.2268%" height="15" fill="rgb(207,175,4)" fg:x="376" fg:w="1"/><text x="85.5108%" y="238.50"></text></g><g><title>write (asyncio\sslproto.py:222) (1 samples, 0.23%)</title><rect x="85.2608%" y="244" width="0.2268%" height="15" fill="rgb(205,108,24)" fg:x="376" fg:w="1"/><text x="85.5108%" y="254.50"></text></g><g><title>_write_appdata (asyncio\sslproto.py:697) (1 samples, 0.23%)</title><rect x="85.2608%" y="260" width="0.2268%" height="15" fill="rgb(244,120,49)" fg:x="376" fg:w="1"/><text x="85.5108%" y="270.50"></text></g><g><title>_do_write (asyncio\sslproto.py:716) (1 samples, 0.23%)</title><rect x="85.2608%" y="276" width="0.2268%" height="15" fill="rgb(223,47,38)" fg:x="376" fg:w="1"/><text x="85.5108%" y="286.50"></text></g><g><title>_process_outgoing (asyncio\sslproto.py:722) (1 samples, 0.23%)</title><rect x="85.2608%" y="292" width="0.2268%" height="15" fill="rgb(229,179,11)" fg:x="376" fg:w="1"/><text x="85.5108%" y="302.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="85.2608%" y="308" width="0.2268%" height="15" fill="rgb(231,122,1)" fg:x="376" fg:w="1"/><text x="85.5108%" y="318.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (1 samples, 0.23%)</title><rect x="85.2608%" y="324" width="0.2268%" height="15" fill="rgb(245,119,9)" fg:x="376" fg:w="1"/><text x="85.5108%" y="334.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="85.2608%" y="340" width="0.2268%" height="15" fill="rgb(241,163,25)" fg:x="376" fg:w="1"/><text x="85.5108%" y="350.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="85.2608%" y="356" width="0.2268%" height="15" fill="rgb(217,214,3)" fg:x="376" fg:w="1"/><text x="85.5108%" y="366.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="85.2608%" y="372" width="0.2268%" height="15" fill="rgb(240,86,28)" fg:x="376" fg:w="1"/><text x="85.5108%" y="382.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="85.2608%" y="388" width="0.2268%" height="15" fill="rgb(215,47,9)" fg:x="376" fg:w="1"/><text x="85.5108%" y="398.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="85.2608%" y="404" width="0.2268%" height="15" fill="rgb(252,25,45)" fg:x="376" fg:w="1"/><text x="85.5108%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="85.2608%" y="420" width="0.2268%" height="15" fill="rgb(251,164,9)" fg:x="376" fg:w="1"/><text x="85.5108%" y="430.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="85.2608%" y="436" width="0.2268%" height="15" fill="rgb(233,194,0)" fg:x="376" fg:w="1"/><text x="85.5108%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:476) (1 samples, 0.23%)</title><rect x="85.4875%" y="356" width="0.2268%" height="15" fill="rgb(249,111,24)" fg:x="377" fg:w="1"/><text x="85.7375%" y="366.50"></text></g><g><title>extended_frame_gen (traceback.py:446) (1 samples, 0.23%)</title><rect x="85.4875%" y="372" width="0.2268%" height="15" fill="rgb(250,223,3)" fg:x="377" fg:w="1"/><text x="85.7375%" y="382.50"></text></g><g><title>walk_stack (traceback.py:389) (1 samples, 0.23%)</title><rect x="85.4875%" y="388" width="0.2268%" height="15" fill="rgb(236,178,37)" fg:x="377" fg:w="1"/><text x="85.7375%" y="398.50"></text></g><g><title>receive (aiohttp\client_ws.py:333) (2 samples, 0.45%)</title><rect x="85.4875%" y="228" width="0.4535%" height="15" fill="rgb(241,158,50)" fg:x="377" fg:w="2"/><text x="85.7375%" y="238.50"></text></g><g><title>__aenter__ (asyncio\timeouts.py:94) (2 samples, 0.45%)</title><rect x="85.4875%" y="244" width="0.4535%" height="15" fill="rgb(213,121,41)" fg:x="377" fg:w="2"/><text x="85.7375%" y="254.50"></text></g><g><title>reschedule (asyncio\timeouts.py:71) (2 samples, 0.45%)</title><rect x="85.4875%" y="260" width="0.4535%" height="15" fill="rgb(240,92,3)" fg:x="377" fg:w="2"/><text x="85.7375%" y="270.50"></text></g><g><title>call_at (asyncio\base_events.py:816) (2 samples, 0.45%)</title><rect x="85.4875%" y="276" width="0.4535%" height="15" fill="rgb(205,123,3)" fg:x="377" fg:w="2"/><text x="85.7375%" y="286.50"></text></g><g><title>__init__ (asyncio\events.py:114) (2 samples, 0.45%)</title><rect x="85.4875%" y="292" width="0.4535%" height="15" fill="rgb(205,97,47)" fg:x="377" fg:w="2"/><text x="85.7375%" y="302.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="85.4875%" y="308" width="0.4535%" height="15" fill="rgb(247,152,14)" fg:x="377" fg:w="2"/><text x="85.7375%" y="318.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="85.4875%" y="324" width="0.4535%" height="15" fill="rgb(248,195,53)" fg:x="377" fg:w="2"/><text x="85.7375%" y="334.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="85.4875%" y="340" width="0.4535%" height="15" fill="rgb(226,201,16)" fg:x="377" fg:w="2"/><text x="85.7375%" y="350.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="85.7143%" y="356" width="0.2268%" height="15" fill="rgb(205,98,0)" fg:x="378" fg:w="1"/><text x="85.9643%" y="366.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="85.7143%" y="372" width="0.2268%" height="15" fill="rgb(214,191,48)" fg:x="378" fg:w="1"/><text x="85.9643%" y="382.50"></text></g><g><title>poll_event (discord\gateway.py:623) (5 samples, 1.13%)</title><rect x="85.4875%" y="212" width="1.1338%" height="15" fill="rgb(237,112,39)" fg:x="377" fg:w="5"/><text x="85.7375%" y="222.50"></text></g><g><title>receive (aiohttp\client_ws.py:334) (3 samples, 0.68%)</title><rect x="85.9410%" y="228" width="0.6803%" height="15" fill="rgb(247,203,27)" fg:x="379" fg:w="3"/><text x="86.1910%" y="238.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (3 samples, 0.68%)</title><rect x="85.9410%" y="244" width="0.6803%" height="15" fill="rgb(235,124,28)" fg:x="379" fg:w="3"/><text x="86.1910%" y="254.50"></text></g><g><title>extract_stack (traceback.py:260) (3 samples, 0.68%)</title><rect x="85.9410%" y="260" width="0.6803%" height="15" fill="rgb(208,207,46)" fg:x="379" fg:w="3"/><text x="86.1910%" y="270.50"></text></g><g><title>extract (traceback.py:449) (3 samples, 0.68%)</title><rect x="85.9410%" y="276" width="0.6803%" height="15" fill="rgb(234,176,4)" fg:x="379" fg:w="3"/><text x="86.1910%" y="286.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (3 samples, 0.68%)</title><rect x="85.9410%" y="292" width="0.6803%" height="15" fill="rgb(230,133,28)" fg:x="379" fg:w="3"/><text x="86.1910%" y="302.50"></text></g><g><title>checkcache (linecache.py:94) (3 samples, 0.68%)</title><rect x="85.9410%" y="308" width="0.6803%" height="15" fill="rgb(211,137,40)" fg:x="379" fg:w="3"/><text x="86.1910%" y="318.50"></text></g><g><title>received_message (discord\gateway.py:499) (1 samples, 0.23%)</title><rect x="86.6213%" y="228" width="0.2268%" height="15" fill="rgb(254,35,13)" fg:x="382" fg:w="1"/><text x="86.8713%" y="238.50"></text></g><g><title>loads (json\__init__.py:346) (1 samples, 0.23%)</title><rect x="86.6213%" y="244" width="0.2268%" height="15" fill="rgb(225,49,51)" fg:x="382" fg:w="1"/><text x="86.8713%" y="254.50"></text></g><g><title>decode (json\decoder.py:345) (1 samples, 0.23%)</title><rect x="86.6213%" y="260" width="0.2268%" height="15" fill="rgb(251,10,15)" fg:x="382" fg:w="1"/><text x="86.8713%" y="270.50"></text></g><g><title>raw_decode (json\decoder.py:361) (1 samples, 0.23%)</title><rect x="86.6213%" y="276" width="0.2268%" height="15" fill="rgb(228,207,15)" fg:x="382" fg:w="1"/><text x="86.8713%" y="286.50"></text></g><g><title>parse_interaction_create (discord\state.py:825) (1 samples, 0.23%)</title><rect x="86.8481%" y="244" width="0.2268%" height="15" fill="rgb(241,99,19)" fg:x="383" fg:w="1"/><text x="87.0981%" y="254.50"></text></g><g><title>dispatch_view (discord\ui\view.py:710) (1 samples, 0.23%)</title><rect x="86.8481%" y="260" width="0.2268%" height="15" fill="rgb(207,104,49)" fg:x="383" fg:w="1"/><text x="87.0981%" y="270.50"></text></g><g><title>_dispatch_item (discord\ui\view.py:463) (1 samples, 0.23%)</title><rect x="86.8481%" y="276" width="0.2268%" height="15" fill="rgb(234,99,18)" fg:x="383" fg:w="1"/><text x="87.0981%" y="286.50"></text></g><g><title>create_task (asyncio\tasks.py:410) (1 samples, 0.23%)</title><rect x="86.8481%" y="292" width="0.2268%" height="15" fill="rgb(213,191,49)" fg:x="383" fg:w="1"/><text x="87.0981%" y="302.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="86.8481%" y="308" width="0.2268%" height="15" fill="rgb(210,226,19)" fg:x="383" fg:w="1"/><text x="87.0981%" y="318.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="86.8481%" y="324" width="0.2268%" height="15" fill="rgb(229,97,18)" fg:x="383" fg:w="1"/><text x="87.0981%" y="334.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="86.8481%" y="340" width="0.2268%" height="15" fill="rgb(211,167,15)" fg:x="383" fg:w="1"/><text x="87.0981%" y="350.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="86.8481%" y="356" width="0.2268%" height="15" fill="rgb(210,169,34)" fg:x="383" fg:w="1"/><text x="87.0981%" y="366.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="86.8481%" y="372" width="0.2268%" height="15" fill="rgb(241,121,31)" fg:x="383" fg:w="1"/><text x="87.0981%" y="382.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="87.0748%" y="308" width="0.2268%" height="15" fill="rgb(232,40,11)" fg:x="384" fg:w="1"/><text x="87.3248%" y="318.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="87.0748%" y="324" width="0.2268%" height="15" fill="rgb(205,86,26)" fg:x="384" fg:w="1"/><text x="87.3248%" y="334.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="87.0748%" y="340" width="0.2268%" height="15" fill="rgb(231,126,28)" fg:x="384" fg:w="1"/><text x="87.3248%" y="350.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="87.0748%" y="356" width="0.2268%" height="15" fill="rgb(219,221,18)" fg:x="384" fg:w="1"/><text x="87.3248%" y="366.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="87.0748%" y="372" width="0.2268%" height="15" fill="rgb(211,40,0)" fg:x="384" fg:w="1"/><text x="87.3248%" y="382.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="87.0748%" y="388" width="0.2268%" height="15" fill="rgb(239,85,43)" fg:x="384" fg:w="1"/><text x="87.3248%" y="398.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="87.0748%" y="404" width="0.2268%" height="15" fill="rgb(231,55,21)" fg:x="384" fg:w="1"/><text x="87.3248%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="87.3016%" y="308" width="0.2268%" height="15" fill="rgb(225,184,43)" fg:x="385" fg:w="1"/><text x="87.5516%" y="318.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="87.3016%" y="324" width="0.2268%" height="15" fill="rgb(251,158,41)" fg:x="385" fg:w="1"/><text x="87.5516%" y="334.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="87.3016%" y="340" width="0.2268%" height="15" fill="rgb(234,159,37)" fg:x="385" fg:w="1"/><text x="87.5516%" y="350.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="87.3016%" y="356" width="0.2268%" height="15" fill="rgb(216,204,22)" fg:x="385" fg:w="1"/><text x="87.5516%" y="366.50"></text></g><g><title>parse_interaction_create (discord\state.py:832) (3 samples, 0.68%)</title><rect x="87.0748%" y="244" width="0.6803%" height="15" fill="rgb(214,17,3)" fg:x="384" fg:w="3"/><text x="87.3248%" y="254.50"></text></g><g><title>dispatch (commands\bot.py:231) (3 samples, 0.68%)</title><rect x="87.0748%" y="260" width="0.6803%" height="15" fill="rgb(212,111,17)" fg:x="384" fg:w="3"/><text x="87.3248%" y="270.50"></text></g><g><title>_schedule_event (discord\client.py:499) (3 samples, 0.68%)</title><rect x="87.0748%" y="276" width="0.6803%" height="15" fill="rgb(221,157,24)" fg:x="384" fg:w="3"/><text x="87.3248%" y="286.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (3 samples, 0.68%)</title><rect x="87.0748%" y="292" width="0.6803%" height="15" fill="rgb(252,16,13)" fg:x="384" fg:w="3"/><text x="87.3248%" y="302.50"></text></g><g><title>extract_stack (traceback.py:261) (1 samples, 0.23%)</title><rect x="87.5283%" y="308" width="0.2268%" height="15" fill="rgb(221,62,2)" fg:x="386" fg:w="1"/><text x="87.7783%" y="318.50"></text></g><g><title>parse_message_create (discord\state.py:688) (1 samples, 0.23%)</title><rect x="87.7551%" y="244" width="0.2268%" height="15" fill="rgb(247,87,22)" fg:x="387" fg:w="1"/><text x="88.0051%" y="254.50"></text></g><g><title>dispatch (commands\bot.py:228) (1 samples, 0.23%)</title><rect x="87.7551%" y="260" width="0.2268%" height="15" fill="rgb(215,73,9)" fg:x="387" fg:w="1"/><text x="88.0051%" y="270.50"></text></g><g><title>dispatch (discord\client.py:539) (1 samples, 0.23%)</title><rect x="87.7551%" y="276" width="0.2268%" height="15" fill="rgb(207,175,33)" fg:x="387" fg:w="1"/><text x="88.0051%" y="286.50"></text></g><g><title>_schedule_event (discord\client.py:499) (1 samples, 0.23%)</title><rect x="87.7551%" y="292" width="0.2268%" height="15" fill="rgb(243,129,54)" fg:x="387" fg:w="1"/><text x="88.0051%" y="302.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="87.7551%" y="308" width="0.2268%" height="15" fill="rgb(227,119,45)" fg:x="387" fg:w="1"/><text x="88.0051%" y="318.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="87.7551%" y="324" width="0.2268%" height="15" fill="rgb(205,109,36)" fg:x="387" fg:w="1"/><text x="88.0051%" y="334.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="87.7551%" y="340" width="0.2268%" height="15" fill="rgb(205,6,39)" fg:x="387" fg:w="1"/><text x="88.0051%" y="350.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="87.7551%" y="356" width="0.2268%" height="15" fill="rgb(221,32,16)" fg:x="387" fg:w="1"/><text x="88.0051%" y="366.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="87.7551%" y="372" width="0.2268%" height="15" fill="rgb(228,144,50)" fg:x="387" fg:w="1"/><text x="88.0051%" y="382.50"></text></g><g><title>parse_message_update (discord\state.py:719) (1 samples, 0.23%)</title><rect x="87.9819%" y="244" width="0.2268%" height="15" fill="rgb(229,201,53)" fg:x="388" fg:w="1"/><text x="88.2319%" y="254.50"></text></g><g><title>worker (discord\shard.py:167) (13 samples, 2.95%)</title><rect x="85.4875%" y="196" width="2.9478%" height="15" fill="rgb(249,153,27)" fg:x="377" fg:w="13"/><text x="85.7375%" y="206.50">wo..</text></g><g><title>poll_event (discord\gateway.py:627) (8 samples, 1.81%)</title><rect x="86.6213%" y="212" width="1.8141%" height="15" fill="rgb(227,106,25)" fg:x="382" fg:w="8"/><text x="86.8713%" y="222.50">p..</text></g><g><title>received_message (discord\gateway.py:574) (7 samples, 1.59%)</title><rect x="86.8481%" y="228" width="1.5873%" height="15" fill="rgb(230,65,29)" fg:x="383" fg:w="7"/><text x="87.0981%" y="238.50"></text></g><g><title>parse_message_update (discord\state.py:721) (1 samples, 0.23%)</title><rect x="88.2086%" y="244" width="0.2268%" height="15" fill="rgb(221,57,46)" fg:x="389" fg:w="1"/><text x="88.4586%" y="254.50"></text></g><g><title>__init__ (discord\message.py:2199) (1 samples, 0.23%)</title><rect x="88.2086%" y="260" width="0.2268%" height="15" fill="rgb(229,161,17)" fg:x="389" fg:w="1"/><text x="88.4586%" y="270.50"></text></g><g><title>from_dict (discord\embeds.py:256) (1 samples, 0.23%)</title><rect x="88.2086%" y="276" width="0.2268%" height="15" fill="rgb(222,213,11)" fg:x="389" fg:w="1"/><text x="88.4586%" y="286.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:734) (1 samples, 0.23%)</title><rect x="88.4354%" y="340" width="0.2268%" height="15" fill="rgb(235,35,13)" fg:x="390" fg:w="1"/><text x="88.6854%" y="350.50"></text></g><g><title>blackjack_command (blackjack_cog.py:828) (2 samples, 0.45%)</title><rect x="88.4354%" y="260" width="0.4535%" height="15" fill="rgb(233,158,34)" fg:x="390" fg:w="2"/><text x="88.6854%" y="270.50"></text></g><g><title>defer (discord\interactions.py:844) (2 samples, 0.45%)</title><rect x="88.4354%" y="276" width="0.4535%" height="15" fill="rgb(215,151,48)" fg:x="390" fg:w="2"/><text x="88.6854%" y="286.50"></text></g><g><title>request (discord\webhook\async_.py:182) (2 samples, 0.45%)</title><rect x="88.4354%" y="292" width="0.4535%" height="15" fill="rgb(229,84,14)" fg:x="390" fg:w="2"/><text x="88.6854%" y="302.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (2 samples, 0.45%)</title><rect x="88.4354%" y="308" width="0.4535%" height="15" fill="rgb(229,68,14)" fg:x="390" fg:w="2"/><text x="88.6854%" y="318.50"></text></g><g><title>_request (aiohttp\client.py:770) (2 samples, 0.45%)</title><rect x="88.4354%" y="324" width="0.4535%" height="15" fill="rgb(243,106,26)" fg:x="390" fg:w="2"/><text x="88.6854%" y="334.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="88.6621%" y="340" width="0.2268%" height="15" fill="rgb(206,45,38)" fg:x="391" fg:w="1"/><text x="88.9121%" y="350.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1467) (1 samples, 0.23%)</title><rect x="88.6621%" y="356" width="0.2268%" height="15" fill="rgb(226,6,15)" fg:x="391" fg:w="1"/><text x="88.9121%" y="366.50"></text></g><g><title>write_headers (aiohttp\http_writer.py:217) (1 samples, 0.23%)</title><rect x="88.6621%" y="372" width="0.2268%" height="15" fill="rgb(232,22,54)" fg:x="391" fg:w="1"/><text x="88.9121%" y="382.50"></text></g><g><title>_start_new_game (blackjack_cog.py:717) (1 samples, 0.23%)</title><rect x="88.8889%" y="276" width="0.2268%" height="15" fill="rgb(229,222,32)" fg:x="392" fg:w="1"/><text x="89.1389%" y="286.50"></text></g><g><title>award_oil (gacha\services\economy_service.py:578) (1 samples, 0.23%)</title><rect x="88.8889%" y="292" width="0.2268%" height="15" fill="rgb(228,62,29)" fg:x="392" fg:w="1"/><text x="89.1389%" y="302.50"></text></g><g><title>award_balance (gacha\services\user_service.py:153) (1 samples, 0.23%)</title><rect x="88.8889%" y="308" width="0.2268%" height="15" fill="rgb(251,103,34)" fg:x="392" fg:w="1"/><text x="89.1389%" y="318.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="88.8889%" y="324" width="0.2268%" height="15" fill="rgb(233,12,30)" fg:x="392" fg:w="1"/><text x="89.1389%" y="334.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="88.8889%" y="340" width="0.2268%" height="15" fill="rgb(238,52,0)" fg:x="392" fg:w="1"/><text x="89.1389%" y="350.50"></text></g><g><title>shield (asyncio\tasks.py:956) (1 samples, 0.23%)</title><rect x="88.8889%" y="356" width="0.2268%" height="15" fill="rgb(223,98,5)" fg:x="392" fg:w="1"/><text x="89.1389%" y="366.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="88.8889%" y="372" width="0.2268%" height="15" fill="rgb(228,75,37)" fg:x="392" fg:w="1"/><text x="89.1389%" y="382.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="88.8889%" y="388" width="0.2268%" height="15" fill="rgb(205,115,49)" fg:x="392" fg:w="1"/><text x="89.1389%" y="398.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="88.8889%" y="404" width="0.2268%" height="15" fill="rgb(250,154,43)" fg:x="392" fg:w="1"/><text x="89.1389%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="88.8889%" y="420" width="0.2268%" height="15" fill="rgb(226,43,29)" fg:x="392" fg:w="1"/><text x="89.1389%" y="430.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="88.8889%" y="436" width="0.2268%" height="15" fill="rgb(249,228,39)" fg:x="392" fg:w="1"/><text x="89.1389%" y="446.50"></text></g><g><title>_start_new_game (blackjack_cog.py:761) (2 samples, 0.45%)</title><rect x="89.1156%" y="276" width="0.4535%" height="15" fill="rgb(216,79,43)" fg:x="393" fg:w="2"/><text x="89.3656%" y="286.50"></text></g><g><title>create_game_embed (blackjack_cog.py:635) (2 samples, 0.45%)</title><rect x="89.1156%" y="292" width="0.4535%" height="15" fill="rgb(228,95,12)" fg:x="393" fg:w="2"/><text x="89.3656%" y="302.50"></text></g><g><title>_get_user_stats (blackjack_cog.py:647) (2 samples, 0.45%)</title><rect x="89.1156%" y="308" width="0.4535%" height="15" fill="rgb(249,221,15)" fg:x="393" fg:w="2"/><text x="89.3656%" y="318.50"></text></g><g><title>get_user_game_stats (gacha\services\game_stats_service.py:58) (2 samples, 0.45%)</title><rect x="89.1156%" y="324" width="0.4535%" height="15" fill="rgb(233,34,13)" fg:x="393" fg:w="2"/><text x="89.3656%" y="334.50"></text></g><g><title>_get_single_game_stats (gacha\services\game_stats_service.py:94) (2 samples, 0.45%)</title><rect x="89.1156%" y="340" width="0.4535%" height="15" fill="rgb(214,103,39)" fg:x="393" fg:w="2"/><text x="89.3656%" y="350.50"></text></g><g><title>get_user_stats_for_single_game (game_stats_repository.py:209) (2 samples, 0.45%)</title><rect x="89.1156%" y="356" width="0.4535%" height="15" fill="rgb(251,126,39)" fg:x="393" fg:w="2"/><text x="89.3656%" y="366.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:157) (2 samples, 0.45%)</title><rect x="89.1156%" y="372" width="0.4535%" height="15" fill="rgb(214,216,36)" fg:x="393" fg:w="2"/><text x="89.3656%" y="382.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (2 samples, 0.45%)</title><rect x="89.1156%" y="388" width="0.4535%" height="15" fill="rgb(220,221,8)" fg:x="393" fg:w="2"/><text x="89.3656%" y="398.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (2 samples, 0.45%)</title><rect x="89.1156%" y="404" width="0.4535%" height="15" fill="rgb(240,216,3)" fg:x="393" fg:w="2"/><text x="89.3656%" y="414.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (2 samples, 0.45%)</title><rect x="89.1156%" y="420" width="0.4535%" height="15" fill="rgb(232,218,17)" fg:x="393" fg:w="2"/><text x="89.3656%" y="430.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (2 samples, 0.45%)</title><rect x="89.1156%" y="436" width="0.4535%" height="15" fill="rgb(229,163,45)" fg:x="393" fg:w="2"/><text x="89.3656%" y="446.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (2 samples, 0.45%)</title><rect x="89.1156%" y="452" width="0.4535%" height="15" fill="rgb(231,110,42)" fg:x="393" fg:w="2"/><text x="89.3656%" y="462.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:409) (2 samples, 0.45%)</title><rect x="89.1156%" y="468" width="0.4535%" height="15" fill="rgb(208,170,48)" fg:x="393" fg:w="2"/><text x="89.3656%" y="478.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (2 samples, 0.45%)</title><rect x="89.1156%" y="484" width="0.4535%" height="15" fill="rgb(239,116,25)" fg:x="393" fg:w="2"/><text x="89.3656%" y="494.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (2 samples, 0.45%)</title><rect x="89.1156%" y="500" width="0.4535%" height="15" fill="rgb(219,200,50)" fg:x="393" fg:w="2"/><text x="89.3656%" y="510.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="89.1156%" y="516" width="0.4535%" height="15" fill="rgb(245,200,0)" fg:x="393" fg:w="2"/><text x="89.3656%" y="526.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="89.1156%" y="532" width="0.4535%" height="15" fill="rgb(245,119,33)" fg:x="393" fg:w="2"/><text x="89.3656%" y="542.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="89.1156%" y="548" width="0.4535%" height="15" fill="rgb(231,125,12)" fg:x="393" fg:w="2"/><text x="89.3656%" y="558.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="89.1156%" y="564" width="0.4535%" height="15" fill="rgb(216,96,41)" fg:x="393" fg:w="2"/><text x="89.3656%" y="574.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="89.1156%" y="580" width="0.4535%" height="15" fill="rgb(248,43,45)" fg:x="393" fg:w="2"/><text x="89.3656%" y="590.50"></text></g><g><title>blackjack_command (blackjack_cog.py:836) (4 samples, 0.91%)</title><rect x="88.8889%" y="260" width="0.9070%" height="15" fill="rgb(217,222,7)" fg:x="392" fg:w="4"/><text x="89.1389%" y="270.50"></text></g><g><title>_start_new_game (blackjack_cog.py:766) (1 samples, 0.23%)</title><rect x="89.5692%" y="276" width="0.2268%" height="15" fill="rgb(233,28,6)" fg:x="395" fg:w="1"/><text x="89.8192%" y="286.50"></text></g><g><title>send (discord\webhook\async_.py:1877) (1 samples, 0.23%)</title><rect x="89.5692%" y="292" width="0.2268%" height="15" fill="rgb(231,218,15)" fg:x="395" fg:w="1"/><text x="89.8192%" y="302.50"></text></g><g><title>store_view (discord\state.py:418) (1 samples, 0.23%)</title><rect x="89.5692%" y="308" width="0.2268%" height="15" fill="rgb(226,171,48)" fg:x="395" fg:w="1"/><text x="89.8192%" y="318.50"></text></g><g><title>add_view (discord\ui\view.py:572) (1 samples, 0.23%)</title><rect x="89.5692%" y="324" width="0.2268%" height="15" fill="rgb(235,201,9)" fg:x="395" fg:w="1"/><text x="89.8192%" y="334.50"></text></g><g><title>_start_listening_from_store (discord\ui\view.py:446) (1 samples, 0.23%)</title><rect x="89.5692%" y="340" width="0.2268%" height="15" fill="rgb(217,80,15)" fg:x="395" fg:w="1"/><text x="89.8192%" y="350.50"></text></g><g><title>create_task (asyncio\tasks.py:410) (1 samples, 0.23%)</title><rect x="89.5692%" y="356" width="0.2268%" height="15" fill="rgb(219,152,8)" fg:x="395" fg:w="1"/><text x="89.8192%" y="366.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="89.5692%" y="372" width="0.2268%" height="15" fill="rgb(243,107,38)" fg:x="395" fg:w="1"/><text x="89.8192%" y="382.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="89.5692%" y="388" width="0.2268%" height="15" fill="rgb(231,17,5)" fg:x="395" fg:w="1"/><text x="89.8192%" y="398.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="89.5692%" y="404" width="0.2268%" height="15" fill="rgb(209,25,54)" fg:x="395" fg:w="1"/><text x="89.8192%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="89.5692%" y="420" width="0.2268%" height="15" fill="rgb(219,0,2)" fg:x="395" fg:w="1"/><text x="89.8192%" y="430.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="89.5692%" y="436" width="0.2268%" height="15" fill="rgb(246,9,5)" fg:x="395" fg:w="1"/><text x="89.8192%" y="446.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:54) (2 samples, 0.45%)</title><rect x="89.7959%" y="324" width="0.4535%" height="15" fill="rgb(226,159,4)" fg:x="396" fg:w="2"/><text x="90.0459%" y="334.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:55) (1 samples, 0.23%)</title><rect x="90.2494%" y="324" width="0.2268%" height="15" fill="rgb(219,175,34)" fg:x="398" fg:w="1"/><text x="90.4994%" y="334.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:56) (1 samples, 0.23%)</title><rect x="90.4762%" y="324" width="0.2268%" height="15" fill="rgb(236,10,46)" fg:x="399" fg:w="1"/><text x="90.7262%" y="334.50"></text></g><g><title>determine_cards_to_draw (gacha\services\draw_engine_service.py:165) (5 samples, 1.13%)</title><rect x="89.7959%" y="308" width="1.1338%" height="15" fill="rgb(240,211,16)" fg:x="396" fg:w="5"/><text x="90.0459%" y="318.50"></text></g><g><title>_get_all_card_pools (gacha\services\draw_engine_service.py:58) (1 samples, 0.23%)</title><rect x="90.7029%" y="324" width="0.2268%" height="15" fill="rgb(205,3,43)" fg:x="400" fg:w="1"/><text x="90.9529%" y="334.50"></text></g><g><title>__call__ (enum.py:726) (1 samples, 0.23%)</title><rect x="90.7029%" y="340" width="0.2268%" height="15" fill="rgb(245,7,22)" fg:x="400" fg:w="1"/><text x="90.9529%" y="350.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:101) (6 samples, 1.36%)</title><rect x="89.7959%" y="292" width="1.3605%" height="15" fill="rgb(239,132,32)" fg:x="396" fg:w="6"/><text x="90.0459%" y="302.50"></text></g><g><title>determine_cards_to_draw (gacha\services\draw_engine_service.py:201) (1 samples, 0.23%)</title><rect x="90.9297%" y="308" width="0.2268%" height="15" fill="rgb(228,202,34)" fg:x="401" fg:w="1"/><text x="91.1797%" y="318.50"></text></g><g><title>_get_cards_details (gacha\services\draw_engine_service.py:262) (1 samples, 0.23%)</title><rect x="90.9297%" y="324" width="0.2268%" height="15" fill="rgb(254,200,22)" fg:x="401" fg:w="1"/><text x="91.1797%" y="334.50"></text></g><g><title>get_cards_details_by_ids (gacha\repositories\card\master_card_repository.py:213) (1 samples, 0.23%)</title><rect x="90.9297%" y="340" width="0.2268%" height="15" fill="rgb(219,10,39)" fg:x="401" fg:w="1"/><text x="91.1797%" y="350.50"></text></g><g><title>get_cards_by_criteria (gacha\repositories\card\master_card_repository.py:57) (1 samples, 0.23%)</title><rect x="90.9297%" y="356" width="0.2268%" height="15" fill="rgb(226,210,39)" fg:x="401" fg:w="1"/><text x="91.1797%" y="366.50"></text></g><g><title>_query_cards_from_db (gacha\repositories\card\master_card_repository.py:177) (1 samples, 0.23%)</title><rect x="90.9297%" y="372" width="0.2268%" height="15" fill="rgb(208,219,16)" fg:x="401" fg:w="1"/><text x="91.1797%" y="382.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:112) (1 samples, 0.23%)</title><rect x="90.9297%" y="388" width="0.2268%" height="15" fill="rgb(216,158,51)" fg:x="401" fg:w="1"/><text x="91.1797%" y="398.50"></text></g><g><title>fetch (asyncpg\connection.py:690) (1 samples, 0.23%)</title><rect x="90.9297%" y="404" width="0.2268%" height="15" fill="rgb(233,14,44)" fg:x="401" fg:w="1"/><text x="91.1797%" y="414.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="90.9297%" y="420" width="0.2268%" height="15" fill="rgb(237,97,39)" fg:x="401" fg:w="1"/><text x="91.1797%" y="430.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="90.9297%" y="436" width="0.2268%" height="15" fill="rgb(218,198,43)" fg:x="401" fg:w="1"/><text x="91.1797%" y="446.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="90.9297%" y="452" width="0.2268%" height="15" fill="rgb(231,104,20)" fg:x="401" fg:w="1"/><text x="91.1797%" y="462.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="90.9297%" y="468" width="0.2268%" height="15" fill="rgb(254,36,13)" fg:x="401" fg:w="1"/><text x="91.1797%" y="478.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="90.9297%" y="484" width="0.2268%" height="15" fill="rgb(248,14,50)" fg:x="401" fg:w="1"/><text x="91.1797%" y="494.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="90.9297%" y="500" width="0.2268%" height="15" fill="rgb(217,107,29)" fg:x="401" fg:w="1"/><text x="91.1797%" y="510.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="90.9297%" y="516" width="0.2268%" height="15" fill="rgb(251,169,33)" fg:x="401" fg:w="1"/><text x="91.1797%" y="526.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="90.9297%" y="532" width="0.2268%" height="15" fill="rgb(217,108,32)" fg:x="401" fg:w="1"/><text x="91.1797%" y="542.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="90.9297%" y="548" width="0.2268%" height="15" fill="rgb(219,66,42)" fg:x="401" fg:w="1"/><text x="91.1797%" y="558.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="90.9297%" y="564" width="0.2268%" height="15" fill="rgb(206,180,7)" fg:x="401" fg:w="1"/><text x="91.1797%" y="574.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="90.9297%" y="580" width="0.2268%" height="15" fill="rgb(208,226,31)" fg:x="401" fg:w="1"/><text x="91.1797%" y="590.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="90.9297%" y="596" width="0.2268%" height="15" fill="rgb(218,26,49)" fg:x="401" fg:w="1"/><text x="91.1797%" y="606.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:119) (1 samples, 0.23%)</title><rect x="91.1565%" y="292" width="0.2268%" height="15" fill="rgb(233,197,48)" fg:x="402" fg:w="1"/><text x="91.4065%" y="302.50"></text></g><g><title>_execute_draw_transaction (gacha\services\gacha_service.py:167) (1 samples, 0.23%)</title><rect x="91.1565%" y="308" width="0.2268%" height="15" fill="rgb(252,181,51)" fg:x="402" fg:w="1"/><text x="91.4065%" y="318.50"></text></g><g><title>award_oil (gacha\services\economy_service.py:578) (1 samples, 0.23%)</title><rect x="91.1565%" y="324" width="0.2268%" height="15" fill="rgb(253,90,19)" fg:x="402" fg:w="1"/><text x="91.4065%" y="334.50"></text></g><g><title>award_balance (gacha\services\user_service.py:158) (1 samples, 0.23%)</title><rect x="91.1565%" y="340" width="0.2268%" height="15" fill="rgb(215,171,30)" fg:x="402" fg:w="1"/><text x="91.4065%" y="350.50"></text></g><g><title>_logic_with_transaction (gacha\services\user_service.py:147) (1 samples, 0.23%)</title><rect x="91.1565%" y="356" width="0.2268%" height="15" fill="rgb(214,222,9)" fg:x="402" fg:w="1"/><text x="91.4065%" y="366.50"></text></g><g><title>_award_balance_logic (gacha\services\user_service.py:176) (1 samples, 0.23%)</title><rect x="91.1565%" y="372" width="0.2268%" height="15" fill="rgb(223,3,22)" fg:x="402" fg:w="1"/><text x="91.4065%" y="382.50"></text></g><g><title>update_balance (gacha\repositories\user\user_repository.py:161) (1 samples, 0.23%)</title><rect x="91.1565%" y="388" width="0.2268%" height="15" fill="rgb(225,196,46)" fg:x="402" fg:w="1"/><text x="91.4065%" y="398.50"></text></g><g><title>execute_query (gacha\repositories\_base_repo.py:38) (1 samples, 0.23%)</title><rect x="91.1565%" y="404" width="0.2268%" height="15" fill="rgb(209,110,37)" fg:x="402" fg:w="1"/><text x="91.4065%" y="414.50"></text></g><g><title>execute (asyncpg\connection.py:352) (1 samples, 0.23%)</title><rect x="91.1565%" y="420" width="0.2268%" height="15" fill="rgb(249,89,12)" fg:x="402" fg:w="1"/><text x="91.4065%" y="430.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="91.1565%" y="436" width="0.2268%" height="15" fill="rgb(226,27,33)" fg:x="402" fg:w="1"/><text x="91.4065%" y="446.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="91.1565%" y="452" width="0.2268%" height="15" fill="rgb(213,82,22)" fg:x="402" fg:w="1"/><text x="91.4065%" y="462.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="91.1565%" y="468" width="0.2268%" height="15" fill="rgb(248,140,0)" fg:x="402" fg:w="1"/><text x="91.4065%" y="478.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="91.1565%" y="484" width="0.2268%" height="15" fill="rgb(228,106,3)" fg:x="402" fg:w="1"/><text x="91.4065%" y="494.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="91.1565%" y="500" width="0.2268%" height="15" fill="rgb(209,23,37)" fg:x="402" fg:w="1"/><text x="91.4065%" y="510.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="91.1565%" y="516" width="0.2268%" height="15" fill="rgb(241,93,50)" fg:x="402" fg:w="1"/><text x="91.4065%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="91.1565%" y="532" width="0.2268%" height="15" fill="rgb(253,46,43)" fg:x="402" fg:w="1"/><text x="91.4065%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="91.1565%" y="548" width="0.2268%" height="15" fill="rgb(226,206,43)" fg:x="402" fg:w="1"/><text x="91.4065%" y="558.50"></text></g><g><title>draw_cards (gacha\services\gacha_service.py:137) (1 samples, 0.23%)</title><rect x="91.3832%" y="292" width="0.2268%" height="15" fill="rgb(217,54,7)" fg:x="403" fg:w="1"/><text x="91.6332%" y="302.50"></text></g><g><title>_enrich_drawn_cards_info (gacha\services\gacha_service.py:337) (1 samples, 0.23%)</title><rect x="91.3832%" y="308" width="0.2268%" height="15" fill="rgb(223,5,52)" fg:x="403" fg:w="1"/><text x="91.6332%" y="318.50"></text></g><g><title>get_card_owner_counts_batch (gacha\repositories\collection\user_collection_repository.py:1175) (1 samples, 0.23%)</title><rect x="91.3832%" y="324" width="0.2268%" height="15" fill="rgb(206,52,46)" fg:x="403" fg:w="1"/><text x="91.6332%" y="334.50"></text></g><g><title>_cache_owner_counts (gacha\repositories\collection\user_collection_repository.py:1137) (1 samples, 0.23%)</title><rect x="91.3832%" y="340" width="0.2268%" height="15" fill="rgb(253,136,11)" fg:x="403" fg:w="1"/><text x="91.6332%" y="350.50"></text></g><g><title>execute (redis\asyncio\client.py:1590) (1 samples, 0.23%)</title><rect x="91.3832%" y="356" width="0.2268%" height="15" fill="rgb(208,106,33)" fg:x="403" fg:w="1"/><text x="91.6332%" y="366.50"></text></g><g><title>get_connection (redis\asyncio\connection.py:1141) (1 samples, 0.23%)</title><rect x="91.3832%" y="372" width="0.2268%" height="15" fill="rgb(206,54,4)" fg:x="403" fg:w="1"/><text x="91.6332%" y="382.50"></text></g><g><title>ensure_connection (redis\asyncio\connection.py:1180) (1 samples, 0.23%)</title><rect x="91.3832%" y="388" width="0.2268%" height="15" fill="rgb(213,3,15)" fg:x="403" fg:w="1"/><text x="91.6332%" y="398.50"></text></g><g><title>can_read_destructive (redis\asyncio\connection.py:561) (1 samples, 0.23%)</title><rect x="91.3832%" y="404" width="0.2268%" height="15" fill="rgb(252,211,39)" fg:x="403" fg:w="1"/><text x="91.6332%" y="414.50"></text></g><g><title>can_read_destructive (redis\_parsers\hiredis.py:240) (1 samples, 0.23%)</title><rect x="91.3832%" y="420" width="0.2268%" height="15" fill="rgb(223,6,36)" fg:x="403" fg:w="1"/><text x="91.6332%" y="430.50"></text></g><g><title>read_from_socket (redis\_parsers\hiredis.py:245) (1 samples, 0.23%)</title><rect x="91.3832%" y="436" width="0.2268%" height="15" fill="rgb(252,169,45)" fg:x="403" fg:w="1"/><text x="91.6332%" y="446.50"></text></g><g><title>read (asyncio\streams.py:730) (1 samples, 0.23%)</title><rect x="91.3832%" y="452" width="0.2268%" height="15" fill="rgb(212,48,26)" fg:x="403" fg:w="1"/><text x="91.6332%" y="462.50"></text></g><g><title>_wait_for_data (asyncio\streams.py:537) (1 samples, 0.23%)</title><rect x="91.3832%" y="468" width="0.2268%" height="15" fill="rgb(251,102,48)" fg:x="403" fg:w="1"/><text x="91.6332%" y="478.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="91.3832%" y="484" width="0.2268%" height="15" fill="rgb(243,208,16)" fg:x="403" fg:w="1"/><text x="91.6332%" y="494.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="91.3832%" y="500" width="0.2268%" height="15" fill="rgb(219,96,24)" fg:x="403" fg:w="1"/><text x="91.6332%" y="510.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="91.3832%" y="516" width="0.2268%" height="15" fill="rgb(219,33,29)" fg:x="403" fg:w="1"/><text x="91.6332%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="91.3832%" y="532" width="0.2268%" height="15" fill="rgb(223,176,5)" fg:x="403" fg:w="1"/><text x="91.6332%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="91.3832%" y="548" width="0.2268%" height="15" fill="rgb(228,140,14)" fg:x="403" fg:w="1"/><text x="91.6332%" y="558.50"></text></g><g><title>_execute_draw_operation (draw_cog.py:136) (9 samples, 2.04%)</title><rect x="89.7959%" y="276" width="2.0408%" height="15" fill="rgb(217,179,31)" fg:x="396" fg:w="9"/><text x="90.0459%" y="286.50">_..</text></g><g><title>draw_cards (gacha\services\gacha_service.py:91) (1 samples, 0.23%)</title><rect x="91.6100%" y="292" width="0.2268%" height="15" fill="rgb(230,9,30)" fg:x="404" fg:w="1"/><text x="91.8600%" y="302.50"></text></g><g><title>get_user (gacha\services\user_service.py:46) (1 samples, 0.23%)</title><rect x="91.6100%" y="308" width="0.2268%" height="15" fill="rgb(230,136,20)" fg:x="404" fg:w="1"/><text x="91.8600%" y="318.50"></text></g><g><title>get_user_optional (gacha\repositories\user\user_repository.py:55) (1 samples, 0.23%)</title><rect x="91.6100%" y="324" width="0.2268%" height="15" fill="rgb(215,210,22)" fg:x="404" fg:w="1"/><text x="91.8600%" y="334.50"></text></g><g><title>get_user (gacha\repositories\user\user_repository.py:33) (1 samples, 0.23%)</title><rect x="91.6100%" y="340" width="0.2268%" height="15" fill="rgb(218,43,5)" fg:x="404" fg:w="1"/><text x="91.8600%" y="350.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:156) (1 samples, 0.23%)</title><rect x="91.6100%" y="356" width="0.2268%" height="15" fill="rgb(216,11,5)" fg:x="404" fg:w="1"/><text x="91.8600%" y="366.50"></text></g><g><title>__aenter__ (asyncpg\pool.py:1024) (1 samples, 0.23%)</title><rect x="91.6100%" y="372" width="0.2268%" height="15" fill="rgb(209,82,29)" fg:x="404" fg:w="1"/><text x="91.8600%" y="382.50"></text></g><g><title>_acquire (asyncpg\pool.py:864) (1 samples, 0.23%)</title><rect x="91.6100%" y="388" width="0.2268%" height="15" fill="rgb(244,115,12)" fg:x="404" fg:w="1"/><text x="91.8600%" y="398.50"></text></g><g><title>_acquire_impl (asyncpg\pool.py:849) (1 samples, 0.23%)</title><rect x="91.6100%" y="404" width="0.2268%" height="15" fill="rgb(222,82,18)" fg:x="404" fg:w="1"/><text x="91.8600%" y="414.50"></text></g><g><title>acquire (asyncpg\pool.py:170) (1 samples, 0.23%)</title><rect x="91.6100%" y="420" width="0.2268%" height="15" fill="rgb(249,227,8)" fg:x="404" fg:w="1"/><text x="91.8600%" y="430.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="91.6100%" y="436" width="0.2268%" height="15" fill="rgb(253,141,45)" fg:x="404" fg:w="1"/><text x="91.8600%" y="446.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="91.6100%" y="452" width="0.2268%" height="15" fill="rgb(234,184,4)" fg:x="404" fg:w="1"/><text x="91.8600%" y="462.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="91.6100%" y="468" width="0.2268%" height="15" fill="rgb(218,194,23)" fg:x="404" fg:w="1"/><text x="91.8600%" y="478.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:481) (1 samples, 0.23%)</title><rect x="91.6100%" y="484" width="0.2268%" height="15" fill="rgb(235,66,41)" fg:x="404" fg:w="1"/><text x="91.8600%" y="494.50"></text></g><g><title>draw_command (draw_cog.py:114) (10 samples, 2.27%)</title><rect x="89.7959%" y="260" width="2.2676%" height="15" fill="rgb(245,217,1)" fg:x="396" fg:w="10"/><text x="90.0459%" y="270.50">d..</text></g><g><title>_execute_draw_operation (draw_cog.py:140) (1 samples, 0.23%)</title><rect x="91.8367%" y="276" width="0.2268%" height="15" fill="rgb(229,91,1)" fg:x="405" fg:w="1"/><text x="92.0867%" y="286.50"></text></g><g><title>_process_draw_result (draw_cog.py:167) (1 samples, 0.23%)</title><rect x="91.8367%" y="292" width="0.2268%" height="15" fill="rgb(207,101,30)" fg:x="405" fg:w="1"/><text x="92.0867%" y="302.50"></text></g><g><title>_process_single_draw_result (draw_cog.py:228) (1 samples, 0.23%)</title><rect x="91.8367%" y="308" width="0.2268%" height="15" fill="rgb(223,82,49)" fg:x="405" fg:w="1"/><text x="92.0867%" y="318.50"></text></g><g><title>send (discord\webhook\async_.py:1857) (1 samples, 0.23%)</title><rect x="91.8367%" y="324" width="0.2268%" height="15" fill="rgb(218,167,17)" fg:x="405" fg:w="1"/><text x="92.0867%" y="334.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="91.8367%" y="340" width="0.2268%" height="15" fill="rgb(208,103,14)" fg:x="405" fg:w="1"/><text x="92.0867%" y="350.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="91.8367%" y="356" width="0.2268%" height="15" fill="rgb(238,20,8)" fg:x="405" fg:w="1"/><text x="92.0867%" y="366.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="91.8367%" y="372" width="0.2268%" height="15" fill="rgb(218,80,54)" fg:x="405" fg:w="1"/><text x="92.0867%" y="382.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="91.8367%" y="388" width="0.2268%" height="15" fill="rgb(240,144,17)" fg:x="405" fg:w="1"/><text x="92.0867%" y="398.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1476) (1 samples, 0.23%)</title><rect x="91.8367%" y="404" width="0.2268%" height="15" fill="rgb(245,27,50)" fg:x="405" fg:w="1"/><text x="92.0867%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="91.8367%" y="420" width="0.2268%" height="15" fill="rgb(251,51,7)" fg:x="405" fg:w="1"/><text x="92.0867%" y="430.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="91.8367%" y="436" width="0.2268%" height="15" fill="rgb(245,217,29)" fg:x="405" fg:w="1"/><text x="92.0867%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="91.8367%" y="452" width="0.2268%" height="15" fill="rgb(221,176,29)" fg:x="405" fg:w="1"/><text x="92.0867%" y="462.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="91.8367%" y="468" width="0.2268%" height="15" fill="rgb(212,180,24)" fg:x="405" fg:w="1"/><text x="92.0867%" y="478.50"></text></g><g><title>draw_command (draw_cog.py:88) (1 samples, 0.23%)</title><rect x="92.0635%" y="260" width="0.2268%" height="15" fill="rgb(254,24,2)" fg:x="406" fg:w="1"/><text x="92.3135%" y="270.50"></text></g><g><title>defer (discord\interactions.py:844) (1 samples, 0.23%)</title><rect x="92.0635%" y="276" width="0.2268%" height="15" fill="rgb(230,100,2)" fg:x="406" fg:w="1"/><text x="92.3135%" y="286.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="92.0635%" y="292" width="0.2268%" height="15" fill="rgb(219,142,25)" fg:x="406" fg:w="1"/><text x="92.3135%" y="302.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="92.0635%" y="308" width="0.2268%" height="15" fill="rgb(240,73,43)" fg:x="406" fg:w="1"/><text x="92.3135%" y="318.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="92.0635%" y="324" width="0.2268%" height="15" fill="rgb(214,114,15)" fg:x="406" fg:w="1"/><text x="92.3135%" y="334.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:748) (1 samples, 0.23%)</title><rect x="92.0635%" y="340" width="0.2268%" height="15" fill="rgb(207,130,4)" fg:x="406" fg:w="1"/><text x="92.3135%" y="350.50"></text></g><g><title>_execute_pagination_query (gacha\repositories\card\card_encyclopedia_repository.py:358) (1 samples, 0.23%)</title><rect x="92.2902%" y="340" width="0.2268%" height="15" fill="rgb(221,25,40)" fg:x="407" fg:w="1"/><text x="92.5402%" y="350.50"></text></g><g><title>fetch_value (gacha\repositories\_base_repo.py:201) (1 samples, 0.23%)</title><rect x="92.2902%" y="356" width="0.2268%" height="15" fill="rgb(241,184,7)" fg:x="407" fg:w="1"/><text x="92.5402%" y="366.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="92.2902%" y="372" width="0.2268%" height="15" fill="rgb(235,159,4)" fg:x="407" fg:w="1"/><text x="92.5402%" y="382.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="92.2902%" y="388" width="0.2268%" height="15" fill="rgb(214,87,48)" fg:x="407" fg:w="1"/><text x="92.5402%" y="398.50"></text></g><g><title>shield (asyncio\tasks.py:951) (1 samples, 0.23%)</title><rect x="92.2902%" y="404" width="0.2268%" height="15" fill="rgb(246,198,24)" fg:x="407" fg:w="1"/><text x="92.5402%" y="414.50"></text></g><g><title>ensure_future (asyncio\tasks.py:748) (1 samples, 0.23%)</title><rect x="92.2902%" y="420" width="0.2268%" height="15" fill="rgb(209,66,40)" fg:x="407" fg:w="1"/><text x="92.5402%" y="430.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="92.2902%" y="436" width="0.2268%" height="15" fill="rgb(233,147,39)" fg:x="407" fg:w="1"/><text x="92.5402%" y="446.50"></text></g><g><title>call_soon (asyncio\base_events.py:837) (1 samples, 0.23%)</title><rect x="92.2902%" y="452" width="0.2268%" height="15" fill="rgb(231,145,52)" fg:x="407" fg:w="1"/><text x="92.5402%" y="462.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (1 samples, 0.23%)</title><rect x="92.2902%" y="468" width="0.2268%" height="15" fill="rgb(206,20,26)" fg:x="407" fg:w="1"/><text x="92.5402%" y="478.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="92.2902%" y="484" width="0.2268%" height="15" fill="rgb(238,220,4)" fg:x="407" fg:w="1"/><text x="92.5402%" y="494.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="92.2902%" y="500" width="0.2268%" height="15" fill="rgb(252,195,42)" fg:x="407" fg:w="1"/><text x="92.5402%" y="510.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="92.2902%" y="516" width="0.2268%" height="15" fill="rgb(209,10,6)" fg:x="407" fg:w="1"/><text x="92.5402%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="92.2902%" y="532" width="0.2268%" height="15" fill="rgb(229,3,52)" fg:x="407" fg:w="1"/><text x="92.5402%" y="542.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="92.2902%" y="548" width="0.2268%" height="15" fill="rgb(253,49,37)" fg:x="407" fg:w="1"/><text x="92.5402%" y="558.50"></text></g><g><title>fetch_value (gacha\repositories\_base_repo.py:201) (1 samples, 0.23%)</title><rect x="92.5170%" y="372" width="0.2268%" height="15" fill="rgb(240,103,49)" fg:x="408" fg:w="1"/><text x="92.7670%" y="382.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (1 samples, 0.23%)</title><rect x="92.5170%" y="388" width="0.2268%" height="15" fill="rgb(250,182,30)" fg:x="408" fg:w="1"/><text x="92.7670%" y="398.50"></text></g><g><title>release (asyncpg\pool.py:905) (1 samples, 0.23%)</title><rect x="92.5170%" y="404" width="0.2268%" height="15" fill="rgb(248,8,30)" fg:x="408" fg:w="1"/><text x="92.7670%" y="414.50"></text></g><g><title>shield (asyncio\tasks.py:951) (1 samples, 0.23%)</title><rect x="92.5170%" y="420" width="0.2268%" height="15" fill="rgb(237,120,30)" fg:x="408" fg:w="1"/><text x="92.7670%" y="430.50"></text></g><g><title>ensure_future (asyncio\tasks.py:748) (1 samples, 0.23%)</title><rect x="92.5170%" y="436" width="0.2268%" height="15" fill="rgb(221,146,34)" fg:x="408" fg:w="1"/><text x="92.7670%" y="446.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="92.5170%" y="452" width="0.2268%" height="15" fill="rgb(242,55,13)" fg:x="408" fg:w="1"/><text x="92.7670%" y="462.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="92.5170%" y="468" width="0.2268%" height="15" fill="rgb(242,112,31)" fg:x="408" fg:w="1"/><text x="92.7670%" y="478.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="92.5170%" y="484" width="0.2268%" height="15" fill="rgb(249,192,27)" fg:x="408" fg:w="1"/><text x="92.7670%" y="494.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="92.5170%" y="500" width="0.2268%" height="15" fill="rgb(208,204,44)" fg:x="408" fg:w="1"/><text x="92.7670%" y="510.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="92.5170%" y="516" width="0.2268%" height="15" fill="rgb(208,93,54)" fg:x="408" fg:w="1"/><text x="92.7670%" y="526.50"></text></g><g><title>get_card_for_page (gacha\services\encyclopedia_service.py:55) (6 samples, 1.36%)</title><rect x="92.2902%" y="308" width="1.3605%" height="15" fill="rgb(242,1,31)" fg:x="407" fg:w="6"/><text x="92.5402%" y="318.50"></text></g><g><title>get_paginated_card_id (gacha\repositories\card\card_encyclopedia_repository.py:301) (6 samples, 1.36%)</title><rect x="92.2902%" y="324" width="1.3605%" height="15" fill="rgb(241,83,25)" fg:x="407" fg:w="6"/><text x="92.5402%" y="334.50"></text></g><g><title>_execute_pagination_query (gacha\repositories\card\card_encyclopedia_repository.py:376) (5 samples, 1.13%)</title><rect x="92.5170%" y="340" width="1.1338%" height="15" fill="rgb(205,169,50)" fg:x="408" fg:w="5"/><text x="92.7670%" y="350.50"></text></g><g><title>_handle_normal_pagination (gacha\repositories\card\card_encyclopedia_repository.py:443) (5 samples, 1.13%)</title><rect x="92.5170%" y="356" width="1.1338%" height="15" fill="rgb(239,186,37)" fg:x="408" fg:w="5"/><text x="92.7670%" y="366.50"></text></g><g><title>fetch_value (gacha\repositories\_base_repo.py:202) (4 samples, 0.91%)</title><rect x="92.7438%" y="372" width="0.9070%" height="15" fill="rgb(205,221,10)" fg:x="409" fg:w="4"/><text x="92.9938%" y="382.50"></text></g><g><title>fetchval (asyncpg\connection.py:714) (4 samples, 0.91%)</title><rect x="92.7438%" y="388" width="0.9070%" height="15" fill="rgb(218,196,15)" fg:x="409" fg:w="4"/><text x="92.9938%" y="398.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (4 samples, 0.91%)</title><rect x="92.7438%" y="404" width="0.9070%" height="15" fill="rgb(218,196,35)" fg:x="409" fg:w="4"/><text x="92.9938%" y="414.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (4 samples, 0.91%)</title><rect x="92.7438%" y="420" width="0.9070%" height="15" fill="rgb(233,63,24)" fg:x="409" fg:w="4"/><text x="92.9938%" y="430.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (4 samples, 0.91%)</title><rect x="92.7438%" y="436" width="0.9070%" height="15" fill="rgb(225,8,4)" fg:x="409" fg:w="4"/><text x="92.9938%" y="446.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (4 samples, 0.91%)</title><rect x="92.7438%" y="452" width="0.9070%" height="15" fill="rgb(234,105,35)" fg:x="409" fg:w="4"/><text x="92.9938%" y="462.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (4 samples, 0.91%)</title><rect x="92.7438%" y="468" width="0.9070%" height="15" fill="rgb(236,21,32)" fg:x="409" fg:w="4"/><text x="92.9938%" y="478.50"></text></g><g><title>send (asyncio\windows_events.py:547) (4 samples, 0.91%)</title><rect x="92.7438%" y="484" width="0.9070%" height="15" fill="rgb(228,109,6)" fg:x="409" fg:w="4"/><text x="92.9938%" y="494.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (4 samples, 0.91%)</title><rect x="92.7438%" y="500" width="0.9070%" height="15" fill="rgb(229,215,31)" fg:x="409" fg:w="4"/><text x="92.9938%" y="510.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (4 samples, 0.91%)</title><rect x="92.7438%" y="516" width="0.9070%" height="15" fill="rgb(221,52,54)" fg:x="409" fg:w="4"/><text x="92.9938%" y="526.50"></text></g><g><title>extract_stack (traceback.py:260) (4 samples, 0.91%)</title><rect x="92.7438%" y="532" width="0.9070%" height="15" fill="rgb(252,129,43)" fg:x="409" fg:w="4"/><text x="92.9938%" y="542.50"></text></g><g><title>extract (traceback.py:449) (4 samples, 0.91%)</title><rect x="92.7438%" y="548" width="0.9070%" height="15" fill="rgb(248,183,27)" fg:x="409" fg:w="4"/><text x="92.9938%" y="558.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (4 samples, 0.91%)</title><rect x="92.7438%" y="564" width="0.9070%" height="15" fill="rgb(250,0,22)" fg:x="409" fg:w="4"/><text x="92.9938%" y="574.50"></text></g><g><title>checkcache (linecache.py:94) (3 samples, 0.68%)</title><rect x="92.9705%" y="580" width="0.6803%" height="15" fill="rgb(213,166,10)" fg:x="410" fg:w="3"/><text x="93.2205%" y="590.50"></text></g><g><title>__aenter__ (asyncpg\pool.py:1024) (1 samples, 0.23%)</title><rect x="93.6508%" y="388" width="0.2268%" height="15" fill="rgb(207,163,36)" fg:x="413" fg:w="1"/><text x="93.9008%" y="398.50"></text></g><g><title>_acquire (asyncpg\pool.py:864) (1 samples, 0.23%)</title><rect x="93.6508%" y="404" width="0.2268%" height="15" fill="rgb(208,122,22)" fg:x="413" fg:w="1"/><text x="93.9008%" y="414.50"></text></g><g><title>_acquire_impl (asyncpg\pool.py:849) (1 samples, 0.23%)</title><rect x="93.6508%" y="420" width="0.2268%" height="15" fill="rgb(207,104,49)" fg:x="413" fg:w="1"/><text x="93.9008%" y="430.50"></text></g><g><title>acquire (asyncpg\pool.py:170) (1 samples, 0.23%)</title><rect x="93.6508%" y="436" width="0.2268%" height="15" fill="rgb(248,211,50)" fg:x="413" fg:w="1"/><text x="93.9008%" y="446.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="93.6508%" y="452" width="0.2268%" height="15" fill="rgb(217,13,45)" fg:x="413" fg:w="1"/><text x="93.9008%" y="462.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="93.6508%" y="468" width="0.2268%" height="15" fill="rgb(211,216,49)" fg:x="413" fg:w="1"/><text x="93.9008%" y="478.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="93.6508%" y="484" width="0.2268%" height="15" fill="rgb(221,58,53)" fg:x="413" fg:w="1"/><text x="93.9008%" y="494.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="93.6508%" y="500" width="0.2268%" height="15" fill="rgb(220,112,41)" fg:x="413" fg:w="1"/><text x="93.9008%" y="510.50"></text></g><g><title>line (traceback.py:377) (1 samples, 0.23%)</title><rect x="93.6508%" y="516" width="0.2268%" height="15" fill="rgb(236,38,28)" fg:x="413" fg:w="1"/><text x="93.9008%" y="526.50"></text></g><g><title>shield (asyncio\tasks.py:951) (1 samples, 0.23%)</title><rect x="93.8776%" y="420" width="0.2268%" height="15" fill="rgb(227,195,22)" fg:x="414" fg:w="1"/><text x="94.1276%" y="430.50"></text></g><g><title>ensure_future (asyncio\tasks.py:748) (1 samples, 0.23%)</title><rect x="93.8776%" y="436" width="0.2268%" height="15" fill="rgb(214,55,33)" fg:x="414" fg:w="1"/><text x="94.1276%" y="446.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="93.8776%" y="452" width="0.2268%" height="15" fill="rgb(248,80,13)" fg:x="414" fg:w="1"/><text x="94.1276%" y="462.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="93.8776%" y="468" width="0.2268%" height="15" fill="rgb(238,52,6)" fg:x="414" fg:w="1"/><text x="94.1276%" y="478.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="93.8776%" y="484" width="0.2268%" height="15" fill="rgb(224,198,47)" fg:x="414" fg:w="1"/><text x="94.1276%" y="494.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="93.8776%" y="500" width="0.2268%" height="15" fill="rgb(233,171,20)" fg:x="414" fg:w="1"/><text x="94.1276%" y="510.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="93.8776%" y="516" width="0.2268%" height="15" fill="rgb(241,30,25)" fg:x="414" fg:w="1"/><text x="94.1276%" y="526.50"></text></g><g><title>get_card_for_page (gacha\services\encyclopedia_service.py:82) (3 samples, 0.68%)</title><rect x="93.6508%" y="308" width="0.6803%" height="15" fill="rgb(207,171,38)" fg:x="413" fg:w="3"/><text x="93.9008%" y="318.50"></text></g><g><title>_get_enhanced_card_data (gacha\services\encyclopedia_service.py:462) (3 samples, 0.68%)</title><rect x="93.6508%" y="324" width="0.6803%" height="15" fill="rgb(234,70,1)" fg:x="413" fg:w="3"/><text x="93.9008%" y="334.50"></text></g><g><title>_get_encyclopedia_extra_data (gacha\services\encyclopedia_service.py:494) (3 samples, 0.68%)</title><rect x="93.6508%" y="340" width="0.6803%" height="15" fill="rgb(232,178,18)" fg:x="413" fg:w="3"/><text x="93.9008%" y="350.50"></text></g><g><title>get_encyclopedia_extra_data (gacha\repositories\card\card_encyclopedia_repository.py:146) (3 samples, 0.68%)</title><rect x="93.6508%" y="356" width="0.6803%" height="15" fill="rgb(241,78,40)" fg:x="413" fg:w="3"/><text x="93.9008%" y="366.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:156) (3 samples, 0.68%)</title><rect x="93.6508%" y="372" width="0.6803%" height="15" fill="rgb(222,35,25)" fg:x="413" fg:w="3"/><text x="93.9008%" y="382.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (2 samples, 0.45%)</title><rect x="93.8776%" y="388" width="0.4535%" height="15" fill="rgb(207,92,16)" fg:x="414" fg:w="2"/><text x="94.1276%" y="398.50"></text></g><g><title>release (asyncpg\pool.py:905) (2 samples, 0.45%)</title><rect x="93.8776%" y="404" width="0.4535%" height="15" fill="rgb(216,59,51)" fg:x="414" fg:w="2"/><text x="94.1276%" y="414.50"></text></g><g><title>shield (asyncio\tasks.py:956) (1 samples, 0.23%)</title><rect x="94.1043%" y="420" width="0.2268%" height="15" fill="rgb(213,80,28)" fg:x="415" fg:w="1"/><text x="94.3543%" y="430.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="94.1043%" y="436" width="0.2268%" height="15" fill="rgb(220,93,7)" fg:x="415" fg:w="1"/><text x="94.3543%" y="446.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="94.1043%" y="452" width="0.2268%" height="15" fill="rgb(225,24,44)" fg:x="415" fg:w="1"/><text x="94.3543%" y="462.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="94.1043%" y="468" width="0.2268%" height="15" fill="rgb(243,74,40)" fg:x="415" fg:w="1"/><text x="94.3543%" y="478.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="94.1043%" y="484" width="0.2268%" height="15" fill="rgb(228,39,7)" fg:x="415" fg:w="1"/><text x="94.3543%" y="494.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="94.1043%" y="500" width="0.2268%" height="15" fill="rgb(227,79,8)" fg:x="415" fg:w="1"/><text x="94.3543%" y="510.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:481) (1 samples, 0.23%)</title><rect x="94.3311%" y="468" width="0.2268%" height="15" fill="rgb(236,58,11)" fg:x="416" fg:w="1"/><text x="94.5811%" y="478.50"></text></g><g><title>lazycache (linecache.py:214) (1 samples, 0.23%)</title><rect x="94.3311%" y="484" width="0.2268%" height="15" fill="rgb(249,63,35)" fg:x="416" fg:w="1"/><text x="94.5811%" y="494.50"></text></g><g><title>get_card_market_stats (gacha\services\card_info_service.py:172) (2 samples, 0.45%)</title><rect x="94.3311%" y="340" width="0.4535%" height="15" fill="rgb(252,114,16)" fg:x="416" fg:w="2"/><text x="94.5811%" y="350.50"></text></g><g><title>__aenter__ (asyncpg\pool.py:1024) (2 samples, 0.45%)</title><rect x="94.3311%" y="356" width="0.4535%" height="15" fill="rgb(254,151,24)" fg:x="416" fg:w="2"/><text x="94.5811%" y="366.50"></text></g><g><title>_acquire (asyncpg\pool.py:864) (2 samples, 0.45%)</title><rect x="94.3311%" y="372" width="0.4535%" height="15" fill="rgb(253,54,39)" fg:x="416" fg:w="2"/><text x="94.5811%" y="382.50"></text></g><g><title>_acquire_impl (asyncpg\pool.py:849) (2 samples, 0.45%)</title><rect x="94.3311%" y="388" width="0.4535%" height="15" fill="rgb(243,25,45)" fg:x="416" fg:w="2"/><text x="94.5811%" y="398.50"></text></g><g><title>acquire (asyncpg\pool.py:170) (2 samples, 0.45%)</title><rect x="94.3311%" y="404" width="0.4535%" height="15" fill="rgb(234,134,9)" fg:x="416" fg:w="2"/><text x="94.5811%" y="414.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (2 samples, 0.45%)</title><rect x="94.3311%" y="420" width="0.4535%" height="15" fill="rgb(227,166,31)" fg:x="416" fg:w="2"/><text x="94.5811%" y="430.50"></text></g><g><title>extract_stack (traceback.py:260) (2 samples, 0.45%)</title><rect x="94.3311%" y="436" width="0.4535%" height="15" fill="rgb(245,143,41)" fg:x="416" fg:w="2"/><text x="94.5811%" y="446.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="94.3311%" y="452" width="0.4535%" height="15" fill="rgb(238,181,32)" fg:x="416" fg:w="2"/><text x="94.5811%" y="462.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="94.5578%" y="468" width="0.2268%" height="15" fill="rgb(224,113,18)" fg:x="417" fg:w="1"/><text x="94.8078%" y="478.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="94.5578%" y="484" width="0.2268%" height="15" fill="rgb(240,229,28)" fg:x="417" fg:w="1"/><text x="94.8078%" y="494.50"></text></g><g><title>get_card_for_page (gacha\services\encyclopedia_service.py:86) (3 samples, 0.68%)</title><rect x="94.3311%" y="308" width="0.6803%" height="15" fill="rgb(250,185,3)" fg:x="416" fg:w="3"/><text x="94.5811%" y="318.50"></text></g><g><title>_get_market_stats (gacha\services\encyclopedia_service.py:536) (3 samples, 0.68%)</title><rect x="94.3311%" y="324" width="0.6803%" height="15" fill="rgb(212,59,25)" fg:x="416" fg:w="3"/><text x="94.5811%" y="334.50"></text></g><g><title>get_card_market_stats (gacha\services\card_info_service.py:174) (1 samples, 0.23%)</title><rect x="94.7846%" y="340" width="0.2268%" height="15" fill="rgb(221,87,20)" fg:x="418" fg:w="1"/><text x="95.0346%" y="350.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (1 samples, 0.23%)</title><rect x="94.7846%" y="356" width="0.2268%" height="15" fill="rgb(213,74,28)" fg:x="418" fg:w="1"/><text x="95.0346%" y="366.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="94.7846%" y="372" width="0.2268%" height="15" fill="rgb(224,132,34)" fg:x="418" fg:w="1"/><text x="95.0346%" y="382.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="94.7846%" y="388" width="0.2268%" height="15" fill="rgb(222,101,24)" fg:x="418" fg:w="1"/><text x="95.0346%" y="398.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2004) (1 samples, 0.23%)</title><rect x="94.7846%" y="404" width="0.2268%" height="15" fill="rgb(254,142,4)" fg:x="418" fg:w="1"/><text x="95.0346%" y="414.50"></text></g><g><title>_get_statement (asyncpg\connection.py:432) (1 samples, 0.23%)</title><rect x="94.7846%" y="420" width="0.2268%" height="15" fill="rgb(230,229,49)" fg:x="418" fg:w="1"/><text x="95.0346%" y="430.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="94.7846%" y="436" width="0.2268%" height="15" fill="rgb(238,70,47)" fg:x="418" fg:w="1"/><text x="95.0346%" y="446.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="94.7846%" y="452" width="0.2268%" height="15" fill="rgb(231,160,17)" fg:x="418" fg:w="1"/><text x="95.0346%" y="462.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="94.7846%" y="468" width="0.2268%" height="15" fill="rgb(218,68,53)" fg:x="418" fg:w="1"/><text x="95.0346%" y="478.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="94.7846%" y="484" width="0.2268%" height="15" fill="rgb(236,111,10)" fg:x="418" fg:w="1"/><text x="95.0346%" y="494.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="94.7846%" y="500" width="0.2268%" height="15" fill="rgb(224,34,41)" fg:x="418" fg:w="1"/><text x="95.0346%" y="510.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="94.7846%" y="516" width="0.2268%" height="15" fill="rgb(241,118,19)" fg:x="418" fg:w="1"/><text x="95.0346%" y="526.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="94.7846%" y="532" width="0.2268%" height="15" fill="rgb(238,129,25)" fg:x="418" fg:w="1"/><text x="95.0346%" y="542.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="94.7846%" y="548" width="0.2268%" height="15" fill="rgb(238,22,31)" fg:x="418" fg:w="1"/><text x="95.0346%" y="558.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="94.7846%" y="564" width="0.2268%" height="15" fill="rgb(222,174,48)" fg:x="418" fg:w="1"/><text x="95.0346%" y="574.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:111) (2 samples, 0.45%)</title><rect x="95.0113%" y="340" width="0.4535%" height="15" fill="rgb(206,152,40)" fg:x="419" fg:w="2"/><text x="95.2613%" y="350.50"></text></g><g><title>__aexit__ (asyncpg\pool.py:1031) (2 samples, 0.45%)</title><rect x="95.0113%" y="356" width="0.4535%" height="15" fill="rgb(218,99,54)" fg:x="419" fg:w="2"/><text x="95.2613%" y="366.50"></text></g><g><title>release (asyncpg\pool.py:905) (2 samples, 0.45%)</title><rect x="95.0113%" y="372" width="0.4535%" height="15" fill="rgb(220,174,26)" fg:x="419" fg:w="2"/><text x="95.2613%" y="382.50"></text></g><g><title>shield (asyncio\tasks.py:956) (2 samples, 0.45%)</title><rect x="95.0113%" y="388" width="0.4535%" height="15" fill="rgb(245,116,9)" fg:x="419" fg:w="2"/><text x="95.2613%" y="398.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (2 samples, 0.45%)</title><rect x="95.0113%" y="404" width="0.4535%" height="15" fill="rgb(209,72,35)" fg:x="419" fg:w="2"/><text x="95.2613%" y="414.50"></text></g><g><title>extract_stack (traceback.py:260) (2 samples, 0.45%)</title><rect x="95.0113%" y="420" width="0.4535%" height="15" fill="rgb(226,126,21)" fg:x="419" fg:w="2"/><text x="95.2613%" y="430.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="95.0113%" y="436" width="0.4535%" height="15" fill="rgb(227,192,1)" fg:x="419" fg:w="2"/><text x="95.2613%" y="446.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="95.0113%" y="452" width="0.4535%" height="15" fill="rgb(237,180,29)" fg:x="419" fg:w="2"/><text x="95.2613%" y="462.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="95.0113%" y="468" width="0.4535%" height="15" fill="rgb(230,197,35)" fg:x="419" fg:w="2"/><text x="95.2613%" y="478.50"></text></g><g><title>encyclopedia_command (collection_cog.py:474) (15 samples, 3.40%)</title><rect x="92.2902%" y="260" width="3.4014%" height="15" fill="rgb(246,193,31)" fg:x="407" fg:w="15"/><text x="92.5402%" y="270.50">enc..</text></g><g><title>create (gacha\views\collection\encyclopedia_view.py:190) (15 samples, 3.40%)</title><rect x="92.2902%" y="276" width="3.4014%" height="15" fill="rgb(241,36,4)" fg:x="407" fg:w="15"/><text x="92.5402%" y="286.50">cre..</text></g><g><title>_fetch_page_data (gacha\views\collection\encyclopedia_view.py:224) (15 samples, 3.40%)</title><rect x="92.2902%" y="292" width="3.4014%" height="15" fill="rgb(241,130,17)" fg:x="407" fg:w="15"/><text x="92.5402%" y="302.50">_fe..</text></g><g><title>get_card_for_page (gacha\services\encyclopedia_service.py:93) (3 samples, 0.68%)</title><rect x="95.0113%" y="308" width="0.6803%" height="15" fill="rgb(206,137,32)" fg:x="419" fg:w="3"/><text x="95.2613%" y="318.50"></text></g><g><title>check_cards_owned_batch (gacha\repositories\collection\user_collection_repository.py:971) (3 samples, 0.68%)</title><rect x="95.0113%" y="324" width="0.6803%" height="15" fill="rgb(237,228,51)" fg:x="419" fg:w="3"/><text x="95.2613%" y="334.50"></text></g><g><title>fetch_all (gacha\repositories\_base_repo.py:112) (1 samples, 0.23%)</title><rect x="95.4649%" y="340" width="0.2268%" height="15" fill="rgb(243,6,42)" fg:x="421" fg:w="1"/><text x="95.7149%" y="350.50"></text></g><g><title>fetch (asyncpg\connection.py:690) (1 samples, 0.23%)</title><rect x="95.4649%" y="356" width="0.2268%" height="15" fill="rgb(251,74,28)" fg:x="421" fg:w="1"/><text x="95.7149%" y="366.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="95.4649%" y="372" width="0.2268%" height="15" fill="rgb(218,20,49)" fg:x="421" fg:w="1"/><text x="95.7149%" y="382.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="95.4649%" y="388" width="0.2268%" height="15" fill="rgb(238,28,14)" fg:x="421" fg:w="1"/><text x="95.7149%" y="398.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="95.4649%" y="404" width="0.2268%" height="15" fill="rgb(229,40,46)" fg:x="421" fg:w="1"/><text x="95.7149%" y="414.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="95.4649%" y="420" width="0.2268%" height="15" fill="rgb(244,195,20)" fg:x="421" fg:w="1"/><text x="95.7149%" y="430.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="95.4649%" y="436" width="0.2268%" height="15" fill="rgb(253,56,35)" fg:x="421" fg:w="1"/><text x="95.7149%" y="446.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="95.4649%" y="452" width="0.2268%" height="15" fill="rgb(210,149,44)" fg:x="421" fg:w="1"/><text x="95.7149%" y="462.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="95.4649%" y="468" width="0.2268%" height="15" fill="rgb(240,135,12)" fg:x="421" fg:w="1"/><text x="95.7149%" y="478.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="95.4649%" y="484" width="0.2268%" height="15" fill="rgb(251,24,50)" fg:x="421" fg:w="1"/><text x="95.7149%" y="494.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="95.4649%" y="500" width="0.2268%" height="15" fill="rgb(243,200,47)" fg:x="421" fg:w="1"/><text x="95.7149%" y="510.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="95.4649%" y="516" width="0.2268%" height="15" fill="rgb(224,166,26)" fg:x="421" fg:w="1"/><text x="95.7149%" y="526.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="95.4649%" y="532" width="0.2268%" height="15" fill="rgb(233,0,47)" fg:x="421" fg:w="1"/><text x="95.7149%" y="542.50"></text></g><g><title>checkcache (linecache.py:82) (1 samples, 0.23%)</title><rect x="95.4649%" y="548" width="0.2268%" height="15" fill="rgb(253,80,5)" fg:x="421" fg:w="1"/><text x="95.7149%" y="558.50"></text></g><g><title>encyclopedia_command (collection_cog.py:491) (1 samples, 0.23%)</title><rect x="95.6916%" y="260" width="0.2268%" height="15" fill="rgb(214,133,25)" fg:x="422" fg:w="1"/><text x="95.9416%" y="270.50"></text></g><g><title>send (discord\webhook\async_.py:1877) (1 samples, 0.23%)</title><rect x="95.6916%" y="276" width="0.2268%" height="15" fill="rgb(209,27,14)" fg:x="422" fg:w="1"/><text x="95.9416%" y="286.50"></text></g><g><title>store_view (discord\state.py:418) (1 samples, 0.23%)</title><rect x="95.6916%" y="292" width="0.2268%" height="15" fill="rgb(219,102,51)" fg:x="422" fg:w="1"/><text x="95.9416%" y="302.50"></text></g><g><title>add_view (discord\ui\view.py:572) (1 samples, 0.23%)</title><rect x="95.6916%" y="308" width="0.2268%" height="15" fill="rgb(237,18,16)" fg:x="422" fg:w="1"/><text x="95.9416%" y="318.50"></text></g><g><title>_start_listening_from_store (discord\ui\view.py:446) (1 samples, 0.23%)</title><rect x="95.6916%" y="324" width="0.2268%" height="15" fill="rgb(241,85,17)" fg:x="422" fg:w="1"/><text x="95.9416%" y="334.50"></text></g><g><title>create_task (asyncio\tasks.py:410) (1 samples, 0.23%)</title><rect x="95.6916%" y="340" width="0.2268%" height="15" fill="rgb(236,90,42)" fg:x="422" fg:w="1"/><text x="95.9416%" y="350.50"></text></g><g><title>create_task (asyncio\base_events.py:475) (1 samples, 0.23%)</title><rect x="95.6916%" y="356" width="0.2268%" height="15" fill="rgb(249,57,21)" fg:x="422" fg:w="1"/><text x="95.9416%" y="366.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="95.6916%" y="372" width="0.2268%" height="15" fill="rgb(243,12,36)" fg:x="422" fg:w="1"/><text x="95.9416%" y="382.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="95.6916%" y="388" width="0.2268%" height="15" fill="rgb(253,128,47)" fg:x="422" fg:w="1"/><text x="95.9416%" y="398.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="95.6916%" y="404" width="0.2268%" height="15" fill="rgb(207,33,20)" fg:x="422" fg:w="1"/><text x="95.9416%" y="414.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="95.6916%" y="420" width="0.2268%" height="15" fill="rgb(233,215,35)" fg:x="422" fg:w="1"/><text x="95.9416%" y="430.50"></text></g><g><title>robot_info (auxiliary\cogs\health_cog.py:36) (1 samples, 0.23%)</title><rect x="95.9184%" y="260" width="0.2268%" height="15" fill="rgb(249,188,52)" fg:x="423" fg:w="1"/><text x="96.1684%" y="270.50"></text></g><g><title>defer (discord\interactions.py:844) (1 samples, 0.23%)</title><rect x="95.9184%" y="276" width="0.2268%" height="15" fill="rgb(225,12,32)" fg:x="423" fg:w="1"/><text x="96.1684%" y="286.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="95.9184%" y="292" width="0.2268%" height="15" fill="rgb(247,98,14)" fg:x="423" fg:w="1"/><text x="96.1684%" y="302.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="95.9184%" y="308" width="0.2268%" height="15" fill="rgb(247,219,48)" fg:x="423" fg:w="1"/><text x="96.1684%" y="318.50"></text></g><g><title>_request (aiohttp\client.py:693) (1 samples, 0.23%)</title><rect x="95.9184%" y="324" width="0.2268%" height="15" fill="rgb(253,60,48)" fg:x="423" fg:w="1"/><text x="96.1684%" y="334.50"></text></g><g><title>__init__ (aiohttp\client_reqrep.py:884) (1 samples, 0.23%)</title><rect x="95.9184%" y="340" width="0.2268%" height="15" fill="rgb(245,15,52)" fg:x="423" fg:w="1"/><text x="96.1684%" y="350.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="95.9184%" y="356" width="0.2268%" height="15" fill="rgb(220,133,28)" fg:x="423" fg:w="1"/><text x="96.1684%" y="366.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="95.9184%" y="372" width="0.2268%" height="15" fill="rgb(217,180,4)" fg:x="423" fg:w="1"/><text x="96.1684%" y="382.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:500) (1 samples, 0.23%)</title><rect x="95.9184%" y="388" width="0.2268%" height="15" fill="rgb(251,24,1)" fg:x="423" fg:w="1"/><text x="96.1684%" y="398.50"></text></g><g><title>line (traceback.py:373) (1 samples, 0.23%)</title><rect x="95.9184%" y="404" width="0.2268%" height="15" fill="rgb(212,185,49)" fg:x="423" fg:w="1"/><text x="96.1684%" y="414.50"></text></g><g><title>_set_lines (traceback.py:351) (1 samples, 0.23%)</title><rect x="95.9184%" y="420" width="0.2268%" height="15" fill="rgb(215,175,22)" fg:x="423" fg:w="1"/><text x="96.1684%" y="430.50"></text></g><g><title>getline (linecache.py:26) (1 samples, 0.23%)</title><rect x="95.9184%" y="436" width="0.2268%" height="15" fill="rgb(250,205,14)" fg:x="423" fg:w="1"/><text x="96.1684%" y="446.50"></text></g><g><title>getlines (linecache.py:42) (1 samples, 0.23%)</title><rect x="95.9184%" y="452" width="0.2268%" height="15" fill="rgb(225,211,22)" fg:x="423" fg:w="1"/><text x="96.1684%" y="462.50"></text></g><g><title>updatecache (linecache.py:126) (1 samples, 0.23%)</title><rect x="95.9184%" y="468" width="0.2268%" height="15" fill="rgb(251,179,42)" fg:x="423" fg:w="1"/><text x="96.1684%" y="478.50"></text></g><g><title>robot_info (auxiliary\cogs\health_cog.py:39) (1 samples, 0.23%)</title><rect x="96.1451%" y="260" width="0.2268%" height="15" fill="rgb(208,216,51)" fg:x="424" fg:w="1"/><text x="96.3951%" y="270.50"></text></g><g><title>_collect_health_data (auxiliary\cogs\health_cog.py:145) (1 samples, 0.23%)</title><rect x="96.1451%" y="276" width="0.2268%" height="15" fill="rgb(235,36,11)" fg:x="424" fg:w="1"/><text x="96.3951%" y="286.50"></text></g><g><title>_collect_stock_market_stats (auxiliary\cogs\health_cog.py:1372) (1 samples, 0.23%)</title><rect x="96.1451%" y="292" width="0.2268%" height="15" fill="rgb(213,189,28)" fg:x="424" fg:w="1"/><text x="96.3951%" y="302.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (1 samples, 0.23%)</title><rect x="96.1451%" y="308" width="0.2268%" height="15" fill="rgb(227,203,42)" fg:x="424" fg:w="1"/><text x="96.3951%" y="318.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="96.1451%" y="324" width="0.2268%" height="15" fill="rgb(244,72,36)" fg:x="424" fg:w="1"/><text x="96.3951%" y="334.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="96.1451%" y="340" width="0.2268%" height="15" fill="rgb(213,53,17)" fg:x="424" fg:w="1"/><text x="96.3951%" y="350.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2024) (1 samples, 0.23%)</title><rect x="96.1451%" y="356" width="0.2268%" height="15" fill="rgb(207,167,3)" fg:x="424" fg:w="1"/><text x="96.3951%" y="366.50"></text></g><g><title>create_future (asyncio\base_events.py:459) (1 samples, 0.23%)</title><rect x="96.1451%" y="372" width="0.2268%" height="15" fill="rgb(216,98,30)" fg:x="424" fg:w="1"/><text x="96.3951%" y="382.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="96.1451%" y="388" width="0.2268%" height="15" fill="rgb(236,123,15)" fg:x="424" fg:w="1"/><text x="96.3951%" y="398.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="96.1451%" y="404" width="0.2268%" height="15" fill="rgb(248,81,50)" fg:x="424" fg:w="1"/><text x="96.3951%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (1 samples, 0.23%)</title><rect x="96.1451%" y="420" width="0.2268%" height="15" fill="rgb(214,120,4)" fg:x="424" fg:w="1"/><text x="96.3951%" y="430.50"></text></g><g><title>checkcache (linecache.py:94) (1 samples, 0.23%)</title><rect x="96.1451%" y="436" width="0.2268%" height="15" fill="rgb(208,179,34)" fg:x="424" fg:w="1"/><text x="96.3951%" y="446.50"></text></g><g><title>robot_info (auxiliary\cogs\health_cog.py:47) (1 samples, 0.23%)</title><rect x="96.3719%" y="260" width="0.2268%" height="15" fill="rgb(227,140,7)" fg:x="425" fg:w="1"/><text x="96.6219%" y="270.50"></text></g><g><title>send (discord\webhook\async_.py:1857) (1 samples, 0.23%)</title><rect x="96.3719%" y="276" width="0.2268%" height="15" fill="rgb(214,22,6)" fg:x="425" fg:w="1"/><text x="96.6219%" y="286.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="96.3719%" y="292" width="0.2268%" height="15" fill="rgb(207,137,27)" fg:x="425" fg:w="1"/><text x="96.6219%" y="302.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="96.3719%" y="308" width="0.2268%" height="15" fill="rgb(210,8,46)" fg:x="425" fg:w="1"/><text x="96.6219%" y="318.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="96.3719%" y="324" width="0.2268%" height="15" fill="rgb(240,16,54)" fg:x="425" fg:w="1"/><text x="96.6219%" y="334.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:725) (1 samples, 0.23%)</title><rect x="96.3719%" y="340" width="0.2268%" height="15" fill="rgb(211,209,29)" fg:x="425" fg:w="1"/><text x="96.6219%" y="350.50"></text></g><g><title>connect (aiohttp\connector.py:622) (1 samples, 0.23%)</title><rect x="96.3719%" y="356" width="0.2268%" height="15" fill="rgb(226,228,24)" fg:x="425" fg:w="1"/><text x="96.6219%" y="366.50"></text></g><g><title>_create_connection (aiohttp\connector.py:1189) (1 samples, 0.23%)</title><rect x="96.3719%" y="372" width="0.2268%" height="15" fill="rgb(222,84,9)" fg:x="425" fg:w="1"/><text x="96.6219%" y="382.50"></text></g><g><title>_create_direct_connection (aiohttp\connector.py:1530) (1 samples, 0.23%)</title><rect x="96.3719%" y="388" width="0.2268%" height="15" fill="rgb(234,203,30)" fg:x="425" fg:w="1"/><text x="96.6219%" y="398.50"></text></g><g><title>_wrap_create_connection (aiohttp\connector.py:1263) (1 samples, 0.23%)</title><rect x="96.3719%" y="404" width="0.2268%" height="15" fill="rgb(238,109,14)" fg:x="425" fg:w="1"/><text x="96.6219%" y="414.50"></text></g><g><title>create_connection (asyncio\base_events.py:1193) (1 samples, 0.23%)</title><rect x="96.3719%" y="420" width="0.2268%" height="15" fill="rgb(233,206,34)" fg:x="425" fg:w="1"/><text x="96.6219%" y="430.50"></text></g><g><title>_create_connection_transport (asyncio\base_events.py:1217) (1 samples, 0.23%)</title><rect x="96.3719%" y="436" width="0.2268%" height="15" fill="rgb(220,167,47)" fg:x="425" fg:w="1"/><text x="96.6219%" y="446.50"></text></g><g><title>_make_ssl_transport (asyncio\proactor_events.py:653) (1 samples, 0.23%)</title><rect x="96.3719%" y="452" width="0.2268%" height="15" fill="rgb(238,105,10)" fg:x="425" fg:w="1"/><text x="96.6219%" y="462.50"></text></g><g><title>__init__ (asyncio\sslproto.py:330) (1 samples, 0.23%)</title><rect x="96.3719%" y="468" width="0.2268%" height="15" fill="rgb(213,227,17)" fg:x="425" fg:w="1"/><text x="96.6219%" y="478.50"></text></g><g><title>wrap_bio (ssl.py:469) (1 samples, 0.23%)</title><rect x="96.3719%" y="484" width="0.2268%" height="15" fill="rgb(217,132,38)" fg:x="425" fg:w="1"/><text x="96.6219%" y="494.50"></text></g><g><title>_create (ssl.py:817) (1 samples, 0.23%)</title><rect x="96.3719%" y="500" width="0.2268%" height="15" fill="rgb(242,146,4)" fg:x="425" fg:w="1"/><text x="96.6219%" y="510.50"></text></g><g><title>view_collection (collection_cog.py:150) (1 samples, 0.23%)</title><rect x="96.5986%" y="260" width="0.2268%" height="15" fill="rgb(212,61,9)" fg:x="426" fg:w="1"/><text x="96.8486%" y="270.50"></text></g><g><title>defer (discord\interactions.py:844) (1 samples, 0.23%)</title><rect x="96.5986%" y="276" width="0.2268%" height="15" fill="rgb(247,126,22)" fg:x="426" fg:w="1"/><text x="96.8486%" y="286.50"></text></g><g><title>request (discord\webhook\async_.py:182) (1 samples, 0.23%)</title><rect x="96.5986%" y="292" width="0.2268%" height="15" fill="rgb(220,196,2)" fg:x="426" fg:w="1"/><text x="96.8486%" y="302.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (1 samples, 0.23%)</title><rect x="96.5986%" y="308" width="0.2268%" height="15" fill="rgb(208,46,4)" fg:x="426" fg:w="1"/><text x="96.8486%" y="318.50"></text></g><g><title>_request (aiohttp\client.py:770) (1 samples, 0.23%)</title><rect x="96.5986%" y="324" width="0.2268%" height="15" fill="rgb(252,104,46)" fg:x="426" fg:w="1"/><text x="96.8486%" y="334.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (1 samples, 0.23%)</title><rect x="96.5986%" y="340" width="0.2268%" height="15" fill="rgb(237,152,48)" fg:x="426" fg:w="1"/><text x="96.8486%" y="350.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1476) (1 samples, 0.23%)</title><rect x="96.5986%" y="356" width="0.2268%" height="15" fill="rgb(221,59,37)" fg:x="426" fg:w="1"/><text x="96.8486%" y="366.50"></text></g><g><title>write_bytes (aiohttp\client_reqrep.py:1374) (1 samples, 0.23%)</title><rect x="96.5986%" y="372" width="0.2268%" height="15" fill="rgb(209,202,51)" fg:x="426" fg:w="1"/><text x="96.8486%" y="382.50"></text></g><g><title>write_with_length (aiohttp\payload.py:425) (1 samples, 0.23%)</title><rect x="96.5986%" y="388" width="0.2268%" height="15" fill="rgb(228,81,30)" fg:x="426" fg:w="1"/><text x="96.8486%" y="398.50"></text></g><g><title>write (aiohttp\http_writer.py:194) (1 samples, 0.23%)</title><rect x="96.5986%" y="404" width="0.2268%" height="15" fill="rgb(227,42,39)" fg:x="426" fg:w="1"/><text x="96.8486%" y="414.50"></text></g><g><title>_send_headers_with_payload (aiohttp\http_writer.py:138) (1 samples, 0.23%)</title><rect x="96.5986%" y="420" width="0.2268%" height="15" fill="rgb(221,26,2)" fg:x="426" fg:w="1"/><text x="96.8486%" y="430.50"></text></g><g><title>_writelines (aiohttp\http_writer.py:108) (1 samples, 0.23%)</title><rect x="96.5986%" y="436" width="0.2268%" height="15" fill="rgb(254,61,31)" fg:x="426" fg:w="1"/><text x="96.8486%" y="446.50"></text></g><g><title>write (asyncio\sslproto.py:222) (1 samples, 0.23%)</title><rect x="96.5986%" y="452" width="0.2268%" height="15" fill="rgb(222,173,38)" fg:x="426" fg:w="1"/><text x="96.8486%" y="462.50"></text></g><g><title>_write_appdata (asyncio\sslproto.py:697) (1 samples, 0.23%)</title><rect x="96.5986%" y="468" width="0.2268%" height="15" fill="rgb(218,50,12)" fg:x="426" fg:w="1"/><text x="96.8486%" y="478.50"></text></g><g><title>_do_write (asyncio\sslproto.py:716) (1 samples, 0.23%)</title><rect x="96.5986%" y="484" width="0.2268%" height="15" fill="rgb(223,88,40)" fg:x="426" fg:w="1"/><text x="96.8486%" y="494.50"></text></g><g><title>_process_outgoing (asyncio\sslproto.py:722) (1 samples, 0.23%)</title><rect x="96.5986%" y="500" width="0.2268%" height="15" fill="rgb(237,54,19)" fg:x="426" fg:w="1"/><text x="96.8486%" y="510.50"></text></g><g><title>write (asyncio\proactor_events.py:366) (1 samples, 0.23%)</title><rect x="96.5986%" y="516" width="0.2268%" height="15" fill="rgb(251,129,25)" fg:x="426" fg:w="1"/><text x="96.8486%" y="526.50"></text></g><g><title>_loop_writing (asyncio\proactor_events.py:402) (1 samples, 0.23%)</title><rect x="96.5986%" y="532" width="0.2268%" height="15" fill="rgb(238,97,19)" fg:x="426" fg:w="1"/><text x="96.8486%" y="542.50"></text></g><g><title>send (asyncio\windows_events.py:547) (1 samples, 0.23%)</title><rect x="96.5986%" y="548" width="0.2268%" height="15" fill="rgb(240,169,18)" fg:x="426" fg:w="1"/><text x="96.8486%" y="558.50"></text></g><g><title>_register (asyncio\windows_events.py:721) (1 samples, 0.23%)</title><rect x="96.5986%" y="564" width="0.2268%" height="15" fill="rgb(230,187,49)" fg:x="426" fg:w="1"/><text x="96.8486%" y="574.50"></text></g><g><title>__init__ (asyncio\windows_events.py:56) (1 samples, 0.23%)</title><rect x="96.5986%" y="580" width="0.2268%" height="15" fill="rgb(209,44,26)" fg:x="426" fg:w="1"/><text x="96.8486%" y="590.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="96.5986%" y="596" width="0.2268%" height="15" fill="rgb(244,0,6)" fg:x="426" fg:w="1"/><text x="96.8486%" y="606.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="96.5986%" y="612" width="0.2268%" height="15" fill="rgb(248,18,21)" fg:x="426" fg:w="1"/><text x="96.8486%" y="622.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:488) (1 samples, 0.23%)</title><rect x="96.5986%" y="628" width="0.2268%" height="15" fill="rgb(245,180,19)" fg:x="426" fg:w="1"/><text x="96.8486%" y="638.50"></text></g><g><title>view_collection (collection_cog.py:262) (1 samples, 0.23%)</title><rect x="96.8254%" y="260" width="0.2268%" height="15" fill="rgb(252,118,36)" fg:x="427" fg:w="1"/><text x="97.0754%" y="270.50"></text></g><g><title>get_user_cards_paginated (gacha\services\collection_service.py:70) (1 samples, 0.23%)</title><rect x="96.8254%" y="276" width="0.2268%" height="15" fill="rgb(210,224,19)" fg:x="427" fg:w="1"/><text x="97.0754%" y="286.50"></text></g><g><title>get_user_cards_paginated (gacha\repositories\collection\user_collection_repository.py:733) (1 samples, 0.23%)</title><rect x="96.8254%" y="292" width="0.2268%" height="15" fill="rgb(218,30,24)" fg:x="427" fg:w="1"/><text x="97.0754%" y="302.50"></text></g><g><title>get_user_collection_stats (gacha\repositories\collection\user_collection_repository.py:1798) (1 samples, 0.23%)</title><rect x="96.8254%" y="308" width="0.2268%" height="15" fill="rgb(219,75,50)" fg:x="427" fg:w="1"/><text x="97.0754%" y="318.50"></text></g><g><title>fetch_one (gacha\repositories\_base_repo.py:157) (1 samples, 0.23%)</title><rect x="96.8254%" y="324" width="0.2268%" height="15" fill="rgb(234,72,50)" fg:x="427" fg:w="1"/><text x="97.0754%" y="334.50"></text></g><g><title>fetchrow (asyncpg\connection.py:748) (1 samples, 0.23%)</title><rect x="96.8254%" y="340" width="0.2268%" height="15" fill="rgb(219,100,48)" fg:x="427" fg:w="1"/><text x="97.0754%" y="350.50"></text></g><g><title>_execute (asyncpg\connection.py:1864) (1 samples, 0.23%)</title><rect x="96.8254%" y="356" width="0.2268%" height="15" fill="rgb(253,5,41)" fg:x="427" fg:w="1"/><text x="97.0754%" y="366.50"></text></g><g><title>__execute (asyncpg\connection.py:1961) (1 samples, 0.23%)</title><rect x="96.8254%" y="372" width="0.2268%" height="15" fill="rgb(247,181,11)" fg:x="427" fg:w="1"/><text x="97.0754%" y="382.50"></text></g><g><title>_do_execute (asyncpg\connection.py:2004) (1 samples, 0.23%)</title><rect x="96.8254%" y="388" width="0.2268%" height="15" fill="rgb(222,223,25)" fg:x="427" fg:w="1"/><text x="97.0754%" y="398.50"></text></g><g><title>_get_statement (asyncpg\connection.py:485) (1 samples, 0.23%)</title><rect x="96.8254%" y="404" width="0.2268%" height="15" fill="rgb(214,198,28)" fg:x="427" fg:w="1"/><text x="97.0754%" y="414.50"></text></g><g><title>put (asyncpg\connection.py:2539) (1 samples, 0.23%)</title><rect x="96.8254%" y="420" width="0.2268%" height="15" fill="rgb(230,46,43)" fg:x="427" fg:w="1"/><text x="97.0754%" y="430.50"></text></g><g><title>_new_entry (asyncpg\connection.py:2572) (1 samples, 0.23%)</title><rect x="96.8254%" y="436" width="0.2268%" height="15" fill="rgb(233,65,53)" fg:x="427" fg:w="1"/><text x="97.0754%" y="446.50"></text></g><g><title>_set_entry_timeout (asyncpg\connection.py:2568) (1 samples, 0.23%)</title><rect x="96.8254%" y="452" width="0.2268%" height="15" fill="rgb(221,121,27)" fg:x="427" fg:w="1"/><text x="97.0754%" y="462.50"></text></g><g><title>call_later (asyncio\base_events.py:799) (1 samples, 0.23%)</title><rect x="96.8254%" y="468" width="0.2268%" height="15" fill="rgb(247,70,47)" fg:x="427" fg:w="1"/><text x="97.0754%" y="478.50"></text></g><g><title>call_at (asyncio\base_events.py:816) (1 samples, 0.23%)</title><rect x="96.8254%" y="484" width="0.2268%" height="15" fill="rgb(228,85,35)" fg:x="427" fg:w="1"/><text x="97.0754%" y="494.50"></text></g><g><title>__init__ (asyncio\events.py:114) (1 samples, 0.23%)</title><rect x="96.8254%" y="500" width="0.2268%" height="15" fill="rgb(209,50,18)" fg:x="427" fg:w="1"/><text x="97.0754%" y="510.50"></text></g><g><title>__init__ (asyncio\events.py:47) (1 samples, 0.23%)</title><rect x="96.8254%" y="516" width="0.2268%" height="15" fill="rgb(250,19,35)" fg:x="427" fg:w="1"/><text x="97.0754%" y="526.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (1 samples, 0.23%)</title><rect x="96.8254%" y="532" width="0.2268%" height="15" fill="rgb(253,107,29)" fg:x="427" fg:w="1"/><text x="97.0754%" y="542.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="96.8254%" y="548" width="0.2268%" height="15" fill="rgb(252,179,29)" fg:x="427" fg:w="1"/><text x="97.0754%" y="558.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:477) (1 samples, 0.23%)</title><rect x="96.8254%" y="564" width="0.2268%" height="15" fill="rgb(238,194,6)" fg:x="427" fg:w="1"/><text x="97.0754%" y="574.50"></text></g><g><title>_request (aiohttp\client.py:693) (1 samples, 0.23%)</title><rect x="97.0522%" y="324" width="0.2268%" height="15" fill="rgb(238,164,29)" fg:x="428" fg:w="1"/><text x="97.3022%" y="334.50"></text></g><g><title>__init__ (aiohttp\client_reqrep.py:884) (1 samples, 0.23%)</title><rect x="97.0522%" y="340" width="0.2268%" height="15" fill="rgb(224,25,9)" fg:x="428" fg:w="1"/><text x="97.3022%" y="350.50"></text></g><g><title>extract_stack (traceback.py:260) (1 samples, 0.23%)</title><rect x="97.0522%" y="356" width="0.2268%" height="15" fill="rgb(244,153,23)" fg:x="428" fg:w="1"/><text x="97.3022%" y="366.50"></text></g><g><title>extract (traceback.py:449) (1 samples, 0.23%)</title><rect x="97.0522%" y="372" width="0.2268%" height="15" fill="rgb(212,203,14)" fg:x="428" fg:w="1"/><text x="97.3022%" y="382.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:480) (1 samples, 0.23%)</title><rect x="97.0522%" y="388" width="0.2268%" height="15" fill="rgb(220,164,20)" fg:x="428" fg:w="1"/><text x="97.3022%" y="398.50"></text></g><g><title>_run_once (asyncio\base_events.py:2034) (307 samples, 69.61%)</title><rect x="28.1179%" y="164" width="69.6145%" height="15" fill="rgb(222,203,48)" fg:x="124" fg:w="307"/><text x="28.3679%" y="174.50">_run_once (asyncio\base_events.py:2034)</text></g><g><title>_run (asyncio\events.py:89) (307 samples, 69.61%)</title><rect x="28.1179%" y="180" width="69.6145%" height="15" fill="rgb(215,159,22)" fg:x="124" fg:w="307"/><text x="28.3679%" y="190.50">_run (asyncio\events.py:89)</text></g><g><title>wrapper (discord\app_commands\tree.py:1151) (41 samples, 9.30%)</title><rect x="88.4354%" y="196" width="9.2971%" height="15" fill="rgb(216,183,47)" fg:x="390" fg:w="41"/><text x="88.6854%" y="206.50">wrapper (disc..</text></g><g><title>_call (discord\app_commands\tree.py:1310) (41 samples, 9.30%)</title><rect x="88.4354%" y="212" width="9.2971%" height="15" fill="rgb(229,195,25)" fg:x="390" fg:w="41"/><text x="88.6854%" y="222.50">_call (discor..</text></g><g><title>_invoke_with_namespace (discord\app_commands\commands.py:883) (41 samples, 9.30%)</title><rect x="88.4354%" y="228" width="9.2971%" height="15" fill="rgb(224,132,51)" fg:x="390" fg:w="41"/><text x="88.6854%" y="238.50">_invoke_with_..</text></g><g><title>_do_call (discord\app_commands\commands.py:857) (41 samples, 9.30%)</title><rect x="88.4354%" y="244" width="9.2971%" height="15" fill="rgb(240,63,7)" fg:x="390" fg:w="41"/><text x="88.6854%" y="254.50">_do_call (dis..</text></g><g><title>view_collection (collection_cog.py:301) (3 samples, 0.68%)</title><rect x="97.0522%" y="260" width="0.6803%" height="15" fill="rgb(249,182,41)" fg:x="428" fg:w="3"/><text x="97.3022%" y="270.50"></text></g><g><title>send (discord\webhook\async_.py:1857) (3 samples, 0.68%)</title><rect x="97.0522%" y="276" width="0.6803%" height="15" fill="rgb(243,47,26)" fg:x="428" fg:w="3"/><text x="97.3022%" y="286.50"></text></g><g><title>request (discord\webhook\async_.py:182) (3 samples, 0.68%)</title><rect x="97.0522%" y="292" width="0.6803%" height="15" fill="rgb(233,48,2)" fg:x="428" fg:w="3"/><text x="97.3022%" y="302.50"></text></g><g><title>__aenter__ (aiohttp\client.py:1482) (3 samples, 0.68%)</title><rect x="97.0522%" y="308" width="0.6803%" height="15" fill="rgb(244,165,34)" fg:x="428" fg:w="3"/><text x="97.3022%" y="318.50"></text></g><g><title>_request (aiohttp\client.py:770) (2 samples, 0.45%)</title><rect x="97.2789%" y="324" width="0.4535%" height="15" fill="rgb(207,89,7)" fg:x="429" fg:w="2"/><text x="97.5289%" y="334.50"></text></g><g><title>_connect_and_send_request (aiohttp\client.py:746) (2 samples, 0.45%)</title><rect x="97.2789%" y="340" width="0.4535%" height="15" fill="rgb(244,117,36)" fg:x="429" fg:w="2"/><text x="97.5289%" y="350.50"></text></g><g><title>send (aiohttp\client_reqrep.py:1493) (2 samples, 0.45%)</title><rect x="97.2789%" y="356" width="0.4535%" height="15" fill="rgb(226,144,34)" fg:x="429" fg:w="2"/><text x="97.5289%" y="366.50"></text></g><g><title>__init__ (aiohttp\client_reqrep.py:349) (2 samples, 0.45%)</title><rect x="97.2789%" y="372" width="0.4535%" height="15" fill="rgb(213,23,19)" fg:x="429" fg:w="2"/><text x="97.5289%" y="382.50"></text></g><g><title>extract_stack (traceback.py:260) (2 samples, 0.45%)</title><rect x="97.2789%" y="388" width="0.4535%" height="15" fill="rgb(217,75,12)" fg:x="429" fg:w="2"/><text x="97.5289%" y="398.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="97.2789%" y="404" width="0.4535%" height="15" fill="rgb(224,159,17)" fg:x="429" fg:w="2"/><text x="97.5289%" y="414.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="97.2789%" y="420" width="0.4535%" height="15" fill="rgb(217,118,1)" fg:x="429" fg:w="2"/><text x="97.5289%" y="430.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="97.2789%" y="436" width="0.4535%" height="15" fill="rgb(232,180,48)" fg:x="429" fg:w="2"/><text x="97.5289%" y="446.50"></text></g><g><title>_run_once (asyncio\base_events.py:2037) (1 samples, 0.23%)</title><rect x="97.7324%" y="164" width="0.2268%" height="15" fill="rgb(230,27,33)" fg:x="431" fg:w="1"/><text x="97.9824%" y="174.50"></text></g><g><title>_run_once (asyncio\base_events.py:2038) (1 samples, 0.23%)</title><rect x="97.9592%" y="164" width="0.2268%" height="15" fill="rgb(205,31,21)" fg:x="432" fg:w="1"/><text x="98.2092%" y="174.50"></text></g><g><title>_format_handle (asyncio\base_events.py:75) (1 samples, 0.23%)</title><rect x="97.9592%" y="180" width="0.2268%" height="15" fill="rgb(253,59,4)" fg:x="432" fg:w="1"/><text x="98.2092%" y="190.50"></text></g><g><title>wrapper (reprlib.py:21) (1 samples, 0.23%)</title><rect x="97.9592%" y="196" width="0.2268%" height="15" fill="rgb(224,201,9)" fg:x="432" fg:w="1"/><text x="98.2092%" y="206.50"></text></g><g><title>_task_repr (asyncio\base_tasks.py:30) (1 samples, 0.23%)</title><rect x="97.9592%" y="212" width="0.2268%" height="15" fill="rgb(229,206,30)" fg:x="432" fg:w="1"/><text x="98.2092%" y="222.50"></text></g><g><title>_task_repr_info (asyncio\base_tasks.py:19) (1 samples, 0.23%)</title><rect x="97.9592%" y="228" width="0.2268%" height="15" fill="rgb(212,67,47)" fg:x="432" fg:w="1"/><text x="98.2092%" y="238.50"></text></g><g><title>wrapper (reprlib.py:21) (1 samples, 0.23%)</title><rect x="97.9592%" y="244" width="0.2268%" height="15" fill="rgb(211,96,50)" fg:x="432" fg:w="1"/><text x="98.2092%" y="254.50"></text></g><g><title>_future_repr (asyncio\base_futures.py:66) (1 samples, 0.23%)</title><rect x="97.9592%" y="260" width="0.2268%" height="15" fill="rgb(252,114,18)" fg:x="432" fg:w="1"/><text x="98.2092%" y="270.50"></text></g><g><title>_future_repr_info (asyncio\base_futures.py:57) (1 samples, 0.23%)</title><rect x="97.9592%" y="276" width="0.2268%" height="15" fill="rgb(223,58,37)" fg:x="432" fg:w="1"/><text x="98.2092%" y="286.50"></text></g><g><title>_format_callbacks (asyncio\base_futures.py:36) (1 samples, 0.23%)</title><rect x="97.9592%" y="292" width="0.2268%" height="15" fill="rgb(237,70,4)" fg:x="432" fg:w="1"/><text x="98.2092%" y="302.50"></text></g><g><title>&lt;module&gt; (BOT.PY:571) (435 samples, 98.64%)</title><rect x="0.0000%" y="68" width="98.6395%" height="15" fill="rgb(244,85,46)" fg:x="0" fg:w="435"/><text x="0.2500%" y="78.50">&lt;module&gt; (BOT.PY:571)</text></g><g><title>run (discord\client.py:906) (435 samples, 98.64%)</title><rect x="0.0000%" y="84" width="98.6395%" height="15" fill="rgb(223,39,52)" fg:x="0" fg:w="435"/><text x="0.2500%" y="94.50">run (discord\client.py:906)</text></g><g><title>run (asyncio\runners.py:195) (435 samples, 98.64%)</title><rect x="0.0000%" y="100" width="98.6395%" height="15" fill="rgb(218,200,14)" fg:x="0" fg:w="435"/><text x="0.2500%" y="110.50">run (asyncio\runners.py:195)</text></g><g><title>run (asyncio\runners.py:118) (435 samples, 98.64%)</title><rect x="0.0000%" y="116" width="98.6395%" height="15" fill="rgb(208,171,16)" fg:x="0" fg:w="435"/><text x="0.2500%" y="126.50">run (asyncio\runners.py:118)</text></g><g><title>run_until_complete (asyncio\base_events.py:712) (435 samples, 98.64%)</title><rect x="0.0000%" y="132" width="98.6395%" height="15" fill="rgb(234,200,18)" fg:x="0" fg:w="435"/><text x="0.2500%" y="142.50">run_until_complete (asyncio\base_events.py:712)</text></g><g><title>run_forever (asyncio\base_events.py:683) (435 samples, 98.64%)</title><rect x="0.0000%" y="148" width="98.6395%" height="15" fill="rgb(228,45,11)" fg:x="0" fg:w="435"/><text x="0.2500%" y="158.50">run_forever (asyncio\base_events.py:683)</text></g><g><title>_run_once (asyncio\base_events.py:2043) (2 samples, 0.45%)</title><rect x="98.1859%" y="164" width="0.4535%" height="15" fill="rgb(237,182,11)" fg:x="433" fg:w="2"/><text x="98.4359%" y="174.50"></text></g><g><title>run (concurrent\futures\thread.py:59) (4 samples, 0.91%)</title><rect x="98.6395%" y="132" width="0.9070%" height="15" fill="rgb(241,175,49)" fg:x="435" fg:w="4"/><text x="98.8895%" y="142.50"></text></g><g><title>draw_with_wishes_sync (gacha\services\draw_engine_service.py:110) (4 samples, 0.91%)</title><rect x="98.6395%" y="148" width="0.9070%" height="15" fill="rgb(247,38,35)" fg:x="435" fg:w="4"/><text x="98.8895%" y="158.50"></text></g><g><title>select_with_weights (gacha\services\wish_service.py:331) (4 samples, 0.91%)</title><rect x="98.6395%" y="164" width="0.9070%" height="15" fill="rgb(228,39,49)" fg:x="435" fg:w="4"/><text x="98.8895%" y="174.50"></text></g><g><title>all (441 samples, 100%)</title><rect x="0.0000%" y="52" width="100.0000%" height="15" fill="rgb(226,101,26)" fg:x="0" fg:w="441"/><text x="0.2500%" y="62.50"></text></g><g><title>_bootstrap (threading.py:1014) (6 samples, 1.36%)</title><rect x="98.6395%" y="68" width="1.3605%" height="15" fill="rgb(206,141,19)" fg:x="435" fg:w="6"/><text x="98.8895%" y="78.50"></text></g><g><title>_bootstrap_inner (threading.py:1043) (6 samples, 1.36%)</title><rect x="98.6395%" y="84" width="1.3605%" height="15" fill="rgb(211,200,13)" fg:x="435" fg:w="6"/><text x="98.8895%" y="94.50"></text></g><g><title>run (threading.py:994) (6 samples, 1.36%)</title><rect x="98.6395%" y="100" width="1.3605%" height="15" fill="rgb(241,121,6)" fg:x="435" fg:w="6"/><text x="98.8895%" y="110.50"></text></g><g><title>_worker (concurrent\futures\thread.py:93) (6 samples, 1.36%)</title><rect x="98.6395%" y="116" width="1.3605%" height="15" fill="rgb(234,221,29)" fg:x="435" fg:w="6"/><text x="98.8895%" y="126.50"></text></g><g><title>run (concurrent\futures\thread.py:65) (2 samples, 0.45%)</title><rect x="99.5465%" y="132" width="0.4535%" height="15" fill="rgb(229,136,5)" fg:x="439" fg:w="2"/><text x="99.7965%" y="142.50"></text></g><g><title>set_result (concurrent\futures\_base.py:550) (2 samples, 0.45%)</title><rect x="99.5465%" y="148" width="0.4535%" height="15" fill="rgb(238,36,11)" fg:x="439" fg:w="2"/><text x="99.7965%" y="158.50"></text></g><g><title>_invoke_callbacks (concurrent\futures\_base.py:340) (2 samples, 0.45%)</title><rect x="99.5465%" y="164" width="0.4535%" height="15" fill="rgb(251,55,41)" fg:x="439" fg:w="2"/><text x="99.7965%" y="174.50"></text></g><g><title>_call_set_state (asyncio\futures.py:400) (2 samples, 0.45%)</title><rect x="99.5465%" y="180" width="0.4535%" height="15" fill="rgb(242,34,40)" fg:x="439" fg:w="2"/><text x="99.7965%" y="190.50"></text></g><g><title>call_soon_threadsafe (asyncio\base_events.py:881) (2 samples, 0.45%)</title><rect x="99.5465%" y="196" width="0.4535%" height="15" fill="rgb(215,42,17)" fg:x="439" fg:w="2"/><text x="99.7965%" y="206.50"></text></g><g><title>_call_soon (asyncio\base_events.py:853) (2 samples, 0.45%)</title><rect x="99.5465%" y="212" width="0.4535%" height="15" fill="rgb(207,44,46)" fg:x="439" fg:w="2"/><text x="99.7965%" y="222.50"></text></g><g><title>__init__ (asyncio\events.py:47) (2 samples, 0.45%)</title><rect x="99.5465%" y="228" width="0.4535%" height="15" fill="rgb(211,206,28)" fg:x="439" fg:w="2"/><text x="99.7965%" y="238.50"></text></g><g><title>extract_stack (asyncio\format_helpers.py:80) (2 samples, 0.45%)</title><rect x="99.5465%" y="244" width="0.4535%" height="15" fill="rgb(237,167,16)" fg:x="439" fg:w="2"/><text x="99.7965%" y="254.50"></text></g><g><title>extract (traceback.py:449) (2 samples, 0.45%)</title><rect x="99.5465%" y="260" width="0.4535%" height="15" fill="rgb(233,66,6)" fg:x="439" fg:w="2"/><text x="99.7965%" y="270.50"></text></g><g><title>_extract_from_extended_frame_gen (traceback.py:495) (2 samples, 0.45%)</title><rect x="99.5465%" y="276" width="0.4535%" height="15" fill="rgb(246,123,29)" fg:x="439" fg:w="2"/><text x="99.7965%" y="286.50"></text></g><g><title>checkcache (linecache.py:94) (2 samples, 0.45%)</title><rect x="99.5465%" y="292" width="0.4535%" height="15" fill="rgb(209,62,40)" fg:x="439" fg:w="2"/><text x="99.7965%" y="302.50"></text></g></svg></svg>